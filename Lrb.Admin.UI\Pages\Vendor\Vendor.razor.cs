﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.Vendor
{
    [Authorize(Roles = "Admin,Manager")]
    public partial class Vendor
    {
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public SpinnerService Spinner { get; set; }
        public VendorModel Model { get; set; } = new();
        public GetAllVendorFilter Filter { get; set; } = new();
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public double MaxPageNumber { get; set; }
        public int TotalCount { get; set; }
        public int ShowingCount { get; set; }

        public List<int> count = new() { 10, 25, 50, 100 };
        public ObservableCollection<VendorModel> Collection { get; set; } = new();
        public string? Token { get; set; }


        protected override async Task OnInitializedAsync()
        {
            await GetVendorData();
        }

        public async Task GetVendorData()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<VendorModel, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await VendorService.GetVendorDetailAsync(Filter);
                });
                while (!fetchData.IsCompleted)
                {
                    Spinner.Show();
                    await fetchData;
                    fetchData.Dispose();
                    Spinner.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<VendorModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity adminUser = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = userActivityHistory.TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.VendorTable,
                        Token = Token
                    };

                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        protected void GetTrueFilter()
        {
            var vendor = Filter.GetType().GetProperties();
            foreach (var result in vendor)
            {
                if (result.GetValue(Filter) != null && result.GetValue(Filter).ToString() == "Select")
                {
                    result.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }
        public void Paginate(int page)
        {
            PageNumber = page;
        }
        private async Task IncreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber >= Math.Ceiling(MaxPageNumber))
            {
                PageNumber++;
            }
            await GetVendorData();
        }

        private async Task DecreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetVendorData();
        }
        public async Task RemoveVendorAsync(Guid id)
        {
            var response = await VendorService.DeleteVendorAsync(id);
            if (response != null)
            {
                await JsRuntime.InvokeVoidAsync("alert", "Successfully Deleted");
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity adminUser = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = userActivityHistory.TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.DeleteVendor,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(adminUser);
            }
            else
            {
                await JsRuntime.InvokeVoidAsync("alert", "Something went wrong");
            }
        }
    }
}
