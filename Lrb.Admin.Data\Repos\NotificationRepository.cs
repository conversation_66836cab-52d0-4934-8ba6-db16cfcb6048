﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Lrb.Admin.Data.Repos
{
    internal class NotificationRepository : INotificationRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string Schema = "tenantmaster";
        protected readonly string Schema1 = "LeadratBlack";
        public NotificationRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }
        public async Task<NotificationDto> CheckNotificationTokenOfUser(string tenantId, Guid userId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema1}.Devices").Select("IsDeleted").Where("IsDeleted", "=", false).Where("TenantId", "=", tenantId).Where("UserId", "=", userId);
                connection.Close();
                return query.FirstOrDefault<NotificationDto>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<Response<bool>> CaptureNotificationDetailOfUser(Notifications model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                model.Id = Guid.NewGuid();
                model.Date = DateTime.UtcNow;
                model.IsDeleted = false;
                var query = await db.Query($"{Schema}.Notifications").InsertAsync(model);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<PagedResponse<NotificationViewModel, string>> GetAllNotificationDetailsAsync(string tenantId, int pageNumber, int pageSize, NotificationFilter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                int count = await GetNotificationCountAsync(tenantId);
                var query = db.Query($"{Schema}.Notifications").Where("TenantId", "=", tenantId).Select().OrderByDesc("Date");
                if (filter.SearchByUserName != null)
                {
                    query = db.Query($"{Schema}.Notifications").Where("TenantId", "=", tenantId).WhereLike("UserName", $"%{filter.SearchByUserName}%", true).Select().OrderByDesc("Date");
                }
                query = query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                IEnumerable<NotificationViewModel> notification = await query.GetAsync<NotificationViewModel>();
                connection.Close();
                return new PagedResponse<NotificationViewModel, string>(notification, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetNotificationCountAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.Notifications").Where("TenantId", "=", tenantId).Where("IsDeleted", "=", false).Select("count(\"TenantId\")");
                connection.Close();
                return await query.CountAsync<int>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
