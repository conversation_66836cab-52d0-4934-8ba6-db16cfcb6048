﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.User;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Settings;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Lrb.Admin.Data.Services
{
    public class UserService : IUserService
    {
        private readonly IHttpService _httpService;
        private readonly LrbApiEndPoints _endPoints;
        private readonly string _baseUri1 = "https://localhost:7243/";
        private readonly IUserRepository _userRepository;
        public UserService(IHttpService httpService, IOptions<LrbApiEndPoints> options, IUserRepository userRepository)
        {
            _userRepository = userRepository;
            _httpService = httpService;
            _endPoints = options.Value;
        }
        public async Task<Response<bool>> CreateBulkUserAsync(IBrowserFile file, string fileName)
        {
            try
            {
                var resource = $"api/v1/user/bulkForAdmin";
                HttpRequestMessage request = new(HttpMethod.Post, resource);
                var response = await _httpService.PostFileAsync<Response<bool>>(_endPoints.WebApi, resource, file);
                return response;// new Response<bool>(true, "Upload Succesfull");
            }
            catch (Exception ex)
            {
                return new Response<bool>()
                {
                    Succeeded = false,
                    Errors = new List<string>() { JsonConvert.SerializeObject(ex) },
                    Message = ex.Message
                };
            }

        }

        public async Task<bool> CreateUserAsync(CreateUserModel model)
        {
            var resource = "api/users";
            var headers = new Dictionary<string, string>()
            {
                { "tenant", $"{model.TenantId}" },
            };
            try
            {
                var response = await _httpService.PostAsync<CreateUserModel, Response<bool>>(_endPoints.IdentityApi, resource, model, headers);
                return response?.Data ?? false;
            }
            catch(Exception ex)
            {
                return new();
            }
            
            
        }

        public async Task<bool> CreateSubStatusAsyncv(CustomSubStatusDto custom)
        {
            var resource = "api/users";
            var headers = new Dictionary<string, string>()
            {
                { "tenant", $"{custom.TenantId}" },
            };
            var response = await _httpService.PostAsync<CustomSubStatusDto, Response<bool>>(_endPoints.WebApi, resource, custom, headers);
            return response?.Data ?? false;
        }

        public async Task<bool> CheckIfEmailAlreadyExistsAsync(string email, string readConnectionString)
        {
            try
            {
                var response = await _userRepository.CheckIfEmailAlreadyExists(email,  readConnectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> CheckIfUserNameExistsAsync(string userName, string readConnectionString)
        {
            try
            {
                var response = await _userRepository.CheckIfUsersNameExists(userName,  readConnectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserModelDto> GetAdminUserByIdAsync(Guid id)
        {
            try
            {
                var response = await _userRepository.GetAdminUserById(id);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<string> GetDefaultCountryCodeAsync(string tenantId, string readConnectionString)
        {
            try
            {
                var response = await _userRepository.GetDefaultCountryCode(tenantId, readConnectionString);
                return response;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserCountDto> GetUserCountsAsync(string tenantId, string readConnectionString)
        {
            try
            {
                var response = await _userRepository.GetUserCountsAsync(tenantId, readConnectionString);
                return response;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
