﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using NuGet.Common;

namespace Lrb.Admin.UI.Pages.Vendor
{
    public partial class AddNewVendor
    {
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]    
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        public VendorModel Model { get; set; } = new();
        public string? Token { get; set; }

        public async Task CreateVendorAsync()
        {
            var response = VendorService.AddVendorAsync(Model);
            if (response != null)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Vendors Added Succesfully");
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity adminUser = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId= userActivityHistory.TenantId,
                    ActivityTime= DateTime.UtcNow,
                    Activity = ActivityConst.AddVendor + " " + Model.FirstName +" "+ Model.LastName +" "+ Model.PhoneNumber,
                    Token= Token,     
                };
                await ActivityService.UserActivityAsync(adminUser);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Something went Wrong Try Again");
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                Model = new();
            }
        }
    }
}
