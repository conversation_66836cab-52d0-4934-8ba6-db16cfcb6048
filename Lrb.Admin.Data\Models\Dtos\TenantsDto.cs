﻿using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class TenantsDto
    {
        public string? Id {  get; set; }
        public string? Identifier {  get; set; }
        public string? Name { get; set; }
        public string? ConnectionString {  get; set; }
        public string? ReadReplicaConnectionString { get; set; }
        public string? DisplayPrefix { get; set; }
        public string[]? ResolutionKeys { get; set; }
        public string? AdminEmail {  get; set; }
        public string? Issuer { get; set; }
        public bool IsActive {  get; set; }
        public DateTime? ValidUpto {  get; set; }
    }
}
