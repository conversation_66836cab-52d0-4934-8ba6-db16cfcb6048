﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Notification
{
    public class Notifications
    {
        public Guid Id { get; set; }
        public DateTime? Date { get; set; }
        public string? Title { get; set; }
        public string? MessageBody { get; set; }
        public bool IsAndroidNotification { get; set; }
        public bool IsIOSNotification { get; set; }
        public string? NotificationRequestDetails { get; set; }
        public string? NotificationResponseDetails { get; set; }
        public string? TenantId { get; set; }
        public Guid UserId { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsSuccess { get; set; }
        public string? UserName { get; set; }
    }
}
