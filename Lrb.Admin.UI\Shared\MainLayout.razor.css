html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden; /* Prevent scrolling on the whole page */
}
.header {
    width: 100%;
    height: 50px; /* Adjust height as needed */
    background-color: #292A2B;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000; /* Ensures it stays above other elements */
}

.logo {
    padding-left:5px;
}

.userinfo {
    display: flex;
    align-items: center;
    cursor:pointer;
}
.header-rightButton {
    display: flex;
    align-items: center;
    gap:10px;
}

.user-logo {
    margin-right: 10px;
}

.user-details {
    display: flex;
    flex-direction: column;
    padding-right:15px;
}

.username {
    font-size: 16px;
    color: #FFFFFF;
}

.user-role {
    font-size: 14px;
    color: #CCCCCC;
}
/* Ensure full height for the body and HTML */
.belowheader {
    display: flex;
    height: calc(100vh - 50px); /* Full height minus header height */
    margin-top: 50px; /* Push content below the fixed header */
}

/* Main layout */
.mainlayout {
    flex: 1; /* Make space for the fixed sidebar */
    padding: 1%;
    height: calc(100vh - 50px); /* Full height minus header */
    overflow-y: auto; /* Scrollable content */
    background-color: #F5F5F5;
}

/* Sidebar */
.sidebar { /* Adjust width as needed */
    background-color: #f8f9fa;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    height: 100%;
/*    position: fixed;*/
    left: 0;
    top: 60px; /* Below the fixed header */
    overflow-y: auto;
}

.payment-details{
    cursor:pointer;
}

@media only screen and (max-width: 700px) {
    .payment-details{
        display:none;
    }
}