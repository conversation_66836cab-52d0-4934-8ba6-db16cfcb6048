﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class TenantCompleteDetailsDto 
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public DateTime DateOfOnBoard { get; set; }
        public DateTime LicenseExpiry { get; set; }
        public string? City { get; set; }
        public int NoOfUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InActiveUser { get; set; }
        public int SoldLicenses { get; set; }
        public double NetAmount { get; set; }
        public double GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public BillingType BillingType { get; set; }
        public long? MagicBricks { get; set; }
        public long? NinetyNineAcres { get; set; }
        public long? Housing { get; set; }
        public long? QuikrHomes { get; set; }
        public long? Website { get; set; }
        public long? EstateDekho { get; set; }
        public long? OLX { get; set; }
        public long? RealEstateIndia { get; set; }
        public long? CommonFloor { get; set; }
        public long? Gmail {  get; set; }
        public long? JustLead { get; set; }
        public long? Facebook { get; set; }
        public long? GoogleAds { get; set; }
    }
}
