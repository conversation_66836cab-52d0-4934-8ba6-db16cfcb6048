﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface ISubscritionRepository
    {
        Task<Response<bool>> CreateNewSubscriptionAsync(LRBSubscription model);
        Task<SubscriptionViewModel> GetTenantSubscriptionAsync(string tenantId);
        Task<IEnumerable<LRBPayments>> GetTenantAllPaymentAsync(Guid? subscriptionId);
        Task<Response<bool>> UpdatePartPaymentAsync(string tenantId, LRBPayments dto);
        Task UpdateTenantDueAmountAsync(Guid? Id, double paidAmount,DateTime? NextDueDate,string name);
        Task<SubscriptionViewModel> GetTenantPaymentDetailAsync(string tenantId, DateTime dueDate);
        Task<Response<bool>> UpdateTenantLicenseAsync(string tenantId, UpdateSubscriptionDto model);
        Task<UpdateSubscriptionDto> GetSubscriptionToUpdateAsync(string tenantId);
        Task<int> GetNoOfLicense(string tenantId);
        Task<PagedResponse<LRBSubscription, string>> GetSubscriptionDetails(string tenantId);
        Task<int> UpdateSubscription(LRBSubscription dto);
        Task<LRBSubscription> GetTenantSubscriptionByIdAsync(Guid? subscriptionId, bool isAddOn);
        Task<TenantsDto> GetAllLicencesValidity(string TenantId);
        Task<Response<bool>> CreateAddOnSubscriptionAsync(LRBSubscriptionAddOns model);
        Task<IEnumerable<LRBSubscriptionAddOns>> GetAllAddOnSubscriptions(Guid? subscriptionId);
        Task<int> UpdateAddOnSubscription(LRBSubscriptionAddOns dto);
        Task<LRBSubscriptionAddOns> GetAddOnSubscriptionByIdAsync(Guid? subscriptionId);
        Task<LRBSubscriptionAddOns> GetAddOnSubscriptionByIdAsync(string? TenantId);
        Task<Response<bool>> DeleteSubscriptionById(Guid? Id, string? entityName);
        Task<int> GetLatestInvoiceNumber();
        Task<Response<bool>> DeleteSubscriptionPayment(LRBPayments payments);
        Task<int> GetSubscriptionCount(string tenantId);
        Task<bool> DeleteAddonSubscription(Guid? AddonId);
        Task<bool> DeleteSubscription(Guid? subscriptionId);
    }
}
