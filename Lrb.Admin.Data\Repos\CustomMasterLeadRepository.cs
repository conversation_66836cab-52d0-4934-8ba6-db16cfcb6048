﻿using Dapper;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata.Compilers;
using SqlKata.Execution;
using SqlKata;

namespace Lrb.Admin.Data.Repos
{
    public class CustomMasterLeadRepository : ICustomMasterLeadRepository
    {
        protected Query Query;
        private readonly IDBConnectionFactory _dBFactory;
        protected readonly string Schema = "LeadratBlack";
        public CustomMasterLeadRepository(IDBConnectionFactory dBFactory)
        {
            _dBFactory = dBFactory;
        }

        public async Task<string?> CreateLeadSubStatus(string tenantId, CustomSubStatusDto custom)
        {
            try
            {
                using var connection = await _dBFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = await db.Query($"{Schema}.CustomMasterLeadStatuses").InsertAsync(custom);
                connection.Close();
                return custom.DisplayName;
            }
            catch(Exception ex)
            {
                return null;
            } 
        }

        public async Task<bool> DeleteLeadSubStatusesById(Guid? id, Guid? transferingId)
        {
            try
            {
                using var connection = await _dBFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                DateTime deletedOn = DateTime.UtcNow;
                if (transferingId == null)
                {
                    var sql = $"UPDATE \"{Schema}\".\"CustomMasterLeadStatuses\"\r\nSET \"IsDeleted\" = true, \"DeletedOn\" = '{deletedOn}'\r\nWHERE \"Id\" = '{id}'";
                    
                    var rowsAffected = await connection.ExecuteAsync(sql);
                    connection.Close();

                    if (rowsAffected > 0)
                    {
                        return true;
                    }
                    return false;
                }
                else
                {
                    var rowsAffected = await db.Query($"{Schema}.Leads")
                    .Where("CustomLeadStatusId", "=", id)
                    .UpdateAsync(new { CustomLeadStatusId = transferingId });

                    var sql = $"UPDATE \"{Schema}\".\"CustomMasterLeadStatuses\" SET \"IsDeleted\" = true WHERE \"Id\" = '{id}'";
                    var result = await connection.ExecuteAsync(sql);connection.Close();
                    if (rowsAffected > 0)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<CustomSubStatusDto> GetCustomMasterLeadStatusById(Guid tenantId)
        {
            try
            {
                using var connection = await _dBFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Query = db.Query($"{Schema}.MasterLeadStatuses").Select();
                
                IEnumerable<GetTenantUserModel> tenantDetail = await Query.GetAsync<GetTenantUserModel>();
                connection.Close();
                throw new Exception();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<long> GetLeadCountRequest(Guid? id, string? tenantId)
        {
            try
            {
                using var connection = await _dBFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());

                var query = db.Query($"{Schema}.Leads").Where("IsDeleted","=",false).Where("TenantId", "=", tenantId)
                    .Where("CustomLeadStatusId", "=", id).Select("count(\"Id\")");

                return query.Count<long>(); ;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLeadCountRequest: {ex.Message}");
                return 0;
            }
        }


        public async Task<IEnumerable<CustomMainStatusesDTO>> GetAllMainStatuses(string? tenantId)
        {
            try
            {
                using var connection = await _dBFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());

                var query = $"SELECT * FROM \"LeadratBlack\".\"CustomMasterLeadStatuses\" Where \"TenantId\" = @TenantId and \"Level\" = 0 and \"IsDeleted\" = false";
                var parameters = new DynamicParameters();
                parameters.Add("@TenantId", tenantId);
                var result  = (await connection.QueryAsync<CustomMainStatusesDTO>(query,parameters)).ToList();
                connection.Close();
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<CustomSubStatusDto>> GetMasterLeadSubStatuses(string? tenantId)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());

            var query = $"SELECT* FROM \"LeadratBlack\".\"CustomMasterLeadStatuses\" WHERE \"TenantId\" = @TenantId and \"Level\" = 1 and \"IsDeleted\" = false";
            var parameters = new DynamicParameters();
            parameters.Add("@TenantId", tenantId);
            var result = (await connection.QueryAsync<CustomSubStatusDto>(query,parameters)).ToList();
            connection.Close();
            return result;
        }

        public async Task<Guid> GetMainStatusId(string? tenantId,string? displayNameMainStatus)
        {
            try
            {
                var connection = await _dBFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());

                var innerQuery = $"Select \"Id\" from \"LeadratBlack\".\"CustomMasterLeadStatuses\" " +
                    $"Where \r\n\"DisplayName\" = '{displayNameMainStatus}' and \"TenantId\" = '{tenantId}'";
                var result = (await connection.QueryAsync<Guid>(innerQuery)).FirstOrDefault();

                return result;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
            
        }

        public async Task<Guid> GetMasterLeadStatusBaseId(Guid? baseId)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());

            var innerQuery = $"Select \"Id\" from \"{Schema}\".\"MasterLeadStatuses\" " +
                $"Where \r\n\"BaseId\" = '{baseId}'";
            var result = (await connection.QueryAsync<Guid>(innerQuery)).FirstOrDefault();
            return result;
        }

        public async Task<CustomSubStatusDto> UpdateSubStatusAsync(Guid id, Guid baseId, string status, string displayName)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> GetUserActivity(string? tenantId, string? name)
        {
            using (var connection = await _dBFactory.CreateReadConnectionAsync())
            {
                var query = "SELECT \"IsActive\" FROM \"tenantmaster\".\"TenantProfile1\" " +
                            "WHERE \"TenantId\" = @TenantId AND \"UserName\" = @UserName";

                var result = await connection.QueryFirstOrDefaultAsync<bool>(query, new { TenantId = tenantId, UserName = name });

                return result;
            }
        }

    }
}
