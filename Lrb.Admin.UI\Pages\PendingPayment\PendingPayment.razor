﻿@page "/pending-payment"
@using Radzen.Blazor.Rendering;

@inject DialogService DialogService
@using Document = iTextSharp.text.Document;
@using PdfWriter = iTextSharp.text.pdf.PdfWriter;
@using System.Threading.Tasks;
@using Lrb.Admin.UI.Modal
@inject NotificationService NotificationService
<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
</head>

<div class="tenantmasterheader">
    <div class="headerText">
        <span class="headername">
            <svg width="273" height="25" viewBox="0 0 273 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.23589 12.2657L12.634 17.2137C12.8784 17.4886 13.333 17.3157 13.333 16.9479L13.333 7.05208C13.333 6.68427 12.8784 6.51143 12.634 6.78633L8.23589 11.7343C8.10118 11.8858 8.10118 12.1142 8.23589 12.2657Z" fill="#292A2B" />
                <path d="M31.88 20V6H37.7C38.5 6 39.22 6.19333 39.86 6.58C40.5133 6.96667 41.0333 7.49333 41.42 8.16C41.8067 8.82667 42 9.57333 42 10.4C42 11.24 41.8067 12 41.42 12.68C41.0333 13.3467 40.5133 13.88 39.86 14.28C39.22 14.68 38.5 14.88 37.7 14.88H34.48V20H31.88ZM34.48 12.48H37.52C37.8533 12.48 38.1533 12.3867 38.42 12.2C38.7 12.0133 38.92 11.7667 39.08 11.46C39.2533 11.1533 39.34 10.8067 39.34 10.42C39.34 10.0333 39.2533 9.69333 39.08 9.4C38.92 9.09333 38.7 8.85333 38.42 8.68C38.1533 8.49333 37.8533 8.4 37.52 8.4H34.48V12.48ZM49.0558 20.2C47.9491 20.2 46.9691 19.9667 46.1158 19.5C45.2758 19.0333 44.6158 18.4 44.1358 17.6C43.6691 16.7867 43.4358 15.86 43.4358 14.82C43.4358 13.9933 43.5691 13.24 43.8358 12.56C44.1024 11.88 44.4691 11.2933 44.9358 10.8C45.4158 10.2933 45.9824 9.90667 46.6358 9.64C47.3024 9.36 48.0291 9.22 48.8158 9.22C49.5091 9.22 50.1558 9.35333 50.7558 9.62C51.3558 9.88667 51.8758 10.2533 52.3158 10.72C52.7558 11.1733 53.0891 11.72 53.3158 12.36C53.5558 12.9867 53.6691 13.6733 53.6558 14.42L53.6358 15.28H45.0958L44.6358 13.6H51.5758L51.2558 13.94V13.5C51.2158 13.0867 51.0824 12.7267 50.8558 12.42C50.6291 12.1 50.3358 11.8533 49.9758 11.68C49.6291 11.4933 49.2424 11.4 48.8158 11.4C48.1624 11.4 47.6091 11.5267 47.1558 11.78C46.7158 12.0333 46.3824 12.4 46.1558 12.88C45.9291 13.3467 45.8158 13.9333 45.8158 14.64C45.8158 15.32 45.9558 15.9133 46.2358 16.42C46.5291 16.9267 46.9358 17.32 47.4558 17.6C47.9891 17.8667 48.6091 18 49.3158 18C49.8091 18 50.2624 17.92 50.6758 17.76C51.0891 17.6 51.5358 17.3133 52.0158 16.9L53.2358 18.6C52.8758 18.9333 52.4624 19.22 51.9958 19.46C51.5424 19.6867 51.0624 19.8667 50.5558 20C50.0491 20.1333 49.5491 20.2 49.0558 20.2ZM55.8345 20V9.46H58.2345L58.2745 11.62L57.8145 11.86C57.9479 11.38 58.2079 10.9467 58.5945 10.56C58.9812 10.16 59.4412 9.84 59.9745 9.6C60.5079 9.36 61.0545 9.24 61.6145 9.24C62.4145 9.24 63.0812 9.4 63.6145 9.72C64.1612 10.04 64.5679 10.52 64.8345 11.16C65.1145 11.8 65.2545 12.6 65.2545 13.56V20H62.7945V13.74C62.7945 13.2067 62.7212 12.7667 62.5745 12.42C62.4279 12.06 62.2012 11.8 61.8945 11.64C61.5879 11.4667 61.2145 11.3867 60.7745 11.4C60.4145 11.4 60.0812 11.46 59.7745 11.58C59.4812 11.6867 59.2212 11.8467 58.9945 12.06C58.7812 12.26 58.6079 12.4933 58.4745 12.76C58.3545 13.0267 58.2945 13.32 58.2945 13.64V20H57.0745C56.8345 20 56.6079 20 56.3945 20C56.1945 20 56.0079 20 55.8345 20ZM72.262 20.2C71.3287 20.2 70.4887 19.9667 69.742 19.5C69.0087 19.02 68.422 18.3733 67.982 17.56C67.5554 16.7333 67.342 15.7867 67.342 14.72C67.342 13.6667 67.5554 12.7267 67.982 11.9C68.4087 11.0733 68.9887 10.4267 69.722 9.96C70.4687 9.48 71.3087 9.24 72.242 9.24C72.7487 9.24 73.2354 9.32 73.702 9.48C74.182 9.64 74.6087 9.86 74.982 10.14C75.3554 10.4067 75.6487 10.7067 75.862 11.04C76.0887 11.3733 76.2154 11.72 76.242 12.08L75.582 12.16V5.2H78.042V20H75.662L75.602 17.52L76.082 17.56C76.0687 17.8933 75.9487 18.2133 75.722 18.52C75.5087 18.8267 75.2287 19.1067 74.882 19.36C74.5354 19.6133 74.1287 19.82 73.662 19.98C73.2087 20.1267 72.742 20.2 72.262 20.2ZM72.742 18.12C73.3154 18.12 73.822 17.9733 74.262 17.68C74.702 17.3867 75.042 16.9867 75.282 16.48C75.5354 15.9733 75.662 15.3867 75.662 14.72C75.662 14.0667 75.5354 13.4867 75.282 12.98C75.042 12.46 74.702 12.06 74.262 11.78C73.822 11.4867 73.3154 11.34 72.742 11.34C72.1687 11.34 71.662 11.4867 71.222 11.78C70.782 12.06 70.4354 12.46 70.182 12.98C69.942 13.4867 69.822 14.0667 69.822 14.72C69.822 15.3867 69.942 15.9733 70.182 16.48C70.4354 16.9867 70.782 17.3867 71.222 17.68C71.662 17.9733 72.1687 18.12 72.742 18.12ZM81.1145 20V9.46H83.5745V20H81.1145ZM82.3145 7.22C81.8212 7.22 81.4345 7.1 81.1545 6.86C80.8879 6.60667 80.7545 6.25333 80.7545 5.8C80.7545 5.37333 80.8945 5.02667 81.1745 4.76C81.4545 4.49333 81.8345 4.36 82.3145 4.36C82.8212 4.36 83.2079 4.48667 83.4745 4.74C83.7545 4.98 83.8945 5.33333 83.8945 5.8C83.8945 6.21333 83.7545 6.55333 83.4745 6.82C83.1945 7.08667 82.8079 7.22 82.3145 7.22ZM86.6939 20V9.46H89.0939L89.1339 11.62L88.6739 11.86C88.8072 11.38 89.0672 10.9467 89.4539 10.56C89.8406 10.16 90.3006 9.84 90.8339 9.6C91.3672 9.36 91.9139 9.24 92.4739 9.24C93.2739 9.24 93.9406 9.4 94.4739 9.72C95.0206 10.04 95.4272 10.52 95.6939 11.16C95.9739 11.8 96.1139 12.6 96.1139 13.56V20H93.6539V13.74C93.6539 13.2067 93.5806 12.7667 93.4339 12.42C93.2872 12.06 93.0606 11.8 92.7539 11.64C92.4472 11.4667 92.0739 11.3867 91.6339 11.4C91.2739 11.4 90.9406 11.46 90.6339 11.58C90.3406 11.6867 90.0806 11.8467 89.8539 12.06C89.6406 12.26 89.4672 12.4933 89.3339 12.76C89.2139 13.0267 89.1539 13.32 89.1539 13.64V20H87.9339C87.6939 20 87.4672 20 87.2539 20C87.0539 20 86.8672 20 86.6939 20ZM103.621 24.6C102.835 24.6 102.048 24.48 101.261 24.24C100.488 24 99.8614 23.6867 99.3814 23.3L100.261 21.48C100.541 21.68 100.855 21.8533 101.201 22C101.548 22.1467 101.915 22.26 102.301 22.34C102.701 22.42 103.095 22.46 103.481 22.46C104.188 22.46 104.775 22.3467 105.241 22.12C105.721 21.9067 106.081 21.5733 106.321 21.12C106.561 20.68 106.681 20.1267 106.681 19.46V17.52L107.041 17.66C106.961 18.0733 106.741 18.4667 106.381 18.84C106.021 19.2133 105.568 19.52 105.021 19.76C104.475 19.9867 103.915 20.1 103.341 20.1C102.341 20.1 101.455 19.8667 100.681 19.4C99.9214 18.9333 99.3147 18.3 98.8614 17.5C98.4214 16.6867 98.2014 15.7533 98.2014 14.7C98.2014 13.6467 98.4214 12.7133 98.8614 11.9C99.3014 11.0733 99.9014 10.4267 100.661 9.96C101.435 9.48 102.301 9.24 103.261 9.24C103.661 9.24 104.048 9.28667 104.421 9.38C104.795 9.47333 105.135 9.60667 105.441 9.78C105.761 9.95333 106.048 10.1467 106.301 10.36C106.555 10.5733 106.761 10.8 106.921 11.04C107.081 11.28 107.181 11.5133 107.221 11.74L106.701 11.9L106.741 9.46H109.161V19.28C109.161 20.1333 109.035 20.8867 108.781 21.54C108.528 22.1933 108.161 22.7467 107.681 23.2C107.201 23.6667 106.621 24.0133 105.941 24.24C105.261 24.48 104.488 24.6 103.621 24.6ZM103.721 18C104.321 18 104.848 17.86 105.301 17.58C105.768 17.3 106.128 16.9133 106.381 16.42C106.635 15.9267 106.761 15.36 106.761 14.72C106.761 14.0667 106.628 13.4933 106.361 13C106.108 12.4933 105.755 12.1 105.301 11.82C104.848 11.54 104.321 11.4 103.721 11.4C103.121 11.4 102.595 11.5467 102.141 11.84C101.688 12.12 101.328 12.5133 101.061 13.02C100.808 13.5133 100.681 14.08 100.681 14.72C100.681 15.3467 100.808 15.9133 101.061 16.42C101.328 16.9133 101.688 17.3 102.141 17.58C102.595 17.86 103.121 18 103.721 18ZM118.325 20V6H124.145C124.945 6 125.665 6.19333 126.305 6.58C126.959 6.96667 127.479 7.49333 127.865 8.16C128.252 8.82667 128.445 9.57333 128.445 10.4C128.445 11.24 128.252 12 127.865 12.68C127.479 13.3467 126.959 13.88 126.305 14.28C125.665 14.68 124.945 14.88 124.145 14.88H120.925V20H118.325ZM120.925 12.48H123.965C124.299 12.48 124.599 12.3867 124.865 12.2C125.145 12.0133 125.365 11.7667 125.525 11.46C125.699 11.1533 125.785 10.8067 125.785 10.42C125.785 10.0333 125.699 9.69333 125.525 9.4C125.365 9.09333 125.145 8.85333 124.865 8.68C124.599 8.49333 124.299 8.4 123.965 8.4H120.925V12.48ZM134.541 20.2C133.674 20.2 132.888 19.96 132.181 19.48C131.474 19 130.908 18.3467 130.481 17.52C130.054 16.6933 129.841 15.7533 129.841 14.7C129.841 13.6467 130.054 12.7067 130.481 11.88C130.908 11.0533 131.488 10.4067 132.221 9.94C132.954 9.47333 133.781 9.24 134.701 9.24C135.234 9.24 135.721 9.32 136.161 9.48C136.601 9.62667 136.988 9.84 137.321 10.12C137.654 10.4 137.928 10.72 138.141 11.08C138.368 11.44 138.521 11.8267 138.601 12.24L138.061 12.1V9.46H140.541V20H138.041V17.48L138.621 17.38C138.528 17.74 138.354 18.0933 138.101 18.44C137.861 18.7733 137.554 19.0733 137.181 19.34C136.821 19.5933 136.414 19.8 135.961 19.96C135.521 20.12 135.048 20.2 134.541 20.2ZM135.221 18.02C135.794 18.02 136.301 17.88 136.741 17.6C137.181 17.32 137.521 16.9333 137.761 16.44C138.014 15.9333 138.141 15.3533 138.141 14.7C138.141 14.06 138.014 13.4933 137.761 13C137.521 12.5067 137.181 12.12 136.741 11.84C136.301 11.56 135.794 11.42 135.221 11.42C134.648 11.42 134.141 11.56 133.701 11.84C133.274 12.12 132.941 12.5067 132.701 13C132.461 13.4933 132.341 14.06 132.341 14.7C132.341 15.3533 132.461 15.9333 132.701 16.44C132.941 16.9333 133.274 17.32 133.701 17.6C134.141 17.88 134.648 18.02 135.221 18.02ZM144.759 24.6L146.999 19.42L147.019 21.06L141.819 9.46H144.659L147.559 16.34C147.652 16.5133 147.745 16.7467 147.839 17.04C147.932 17.32 148.012 17.6 148.079 17.88L147.579 18.04C147.672 17.7867 147.772 17.52 147.879 17.24C147.985 16.9467 148.085 16.6467 148.179 16.34L150.659 9.46H153.519L149.119 20L147.319 24.6H144.759ZM155.013 20V9.46H157.433L157.473 11.58L157.073 11.74C157.193 11.3933 157.373 11.0733 157.613 10.78C157.853 10.4733 158.14 10.2133 158.473 10C158.807 9.77333 159.16 9.6 159.533 9.48C159.907 9.34667 160.287 9.28 160.673 9.28C161.247 9.28 161.753 9.37333 162.193 9.56C162.647 9.73333 163.02 10.0133 163.313 10.4C163.62 10.7867 163.847 11.28 163.993 11.88L163.613 11.8L163.773 11.48C163.92 11.16 164.127 10.8733 164.393 10.62C164.66 10.3533 164.96 10.12 165.293 9.92C165.627 9.70667 165.973 9.54667 166.333 9.44C166.707 9.33333 167.073 9.28 167.433 9.28C168.233 9.28 168.9 9.44 169.433 9.76C169.967 10.08 170.367 10.5667 170.633 11.22C170.9 11.8733 171.033 12.68 171.033 13.64V20H168.553V13.8C168.553 13.2667 168.48 12.8267 168.333 12.48C168.2 12.1333 167.987 11.88 167.693 11.72C167.413 11.5467 167.06 11.46 166.633 11.46C166.3 11.46 165.98 11.52 165.673 11.64C165.38 11.7467 165.127 11.9067 164.913 12.12C164.7 12.32 164.533 12.5533 164.413 12.82C164.293 13.0867 164.233 13.38 164.233 13.7V20H161.753V13.78C161.753 13.2733 161.68 12.8533 161.533 12.52C161.387 12.1733 161.173 11.9133 160.893 11.74C160.613 11.5533 160.273 11.46 159.873 11.46C159.54 11.46 159.227 11.52 158.933 11.64C158.64 11.7467 158.387 11.9 158.173 12.1C157.96 12.3 157.793 12.5333 157.673 12.8C157.553 13.0667 157.493 13.36 157.493 13.68V20H155.013ZM178.704 20.2C177.598 20.2 176.618 19.9667 175.764 19.5C174.924 19.0333 174.264 18.4 173.784 17.6C173.318 16.7867 173.084 15.86 173.084 14.82C173.084 13.9933 173.218 13.24 173.484 12.56C173.751 11.88 174.118 11.2933 174.584 10.8C175.064 10.2933 175.631 9.90667 176.284 9.64C176.951 9.36 177.678 9.22 178.464 9.22C179.158 9.22 179.804 9.35333 180.404 9.62C181.004 9.88667 181.524 10.2533 181.964 10.72C182.404 11.1733 182.738 11.72 182.964 12.36C183.204 12.9867 183.318 13.6733 183.304 14.42L183.284 15.28H174.744L174.284 13.6H181.224L180.904 13.94V13.5C180.864 13.0867 180.731 12.7267 180.504 12.42C180.278 12.1 179.984 11.8533 179.624 11.68C179.278 11.4933 178.891 11.4 178.464 11.4C177.811 11.4 177.258 11.5267 176.804 11.78C176.364 12.0333 176.031 12.4 175.804 12.88C175.578 13.3467 175.464 13.9333 175.464 14.64C175.464 15.32 175.604 15.9133 175.884 16.42C176.178 16.9267 176.584 17.32 177.104 17.6C177.638 17.8667 178.258 18 178.964 18C179.458 18 179.911 17.92 180.324 17.76C180.738 17.6 181.184 17.3133 181.664 16.9L182.884 18.6C182.524 18.9333 182.111 19.22 181.644 19.46C181.191 19.6867 180.711 19.8667 180.204 20C179.698 20.1333 179.198 20.2 178.704 20.2ZM185.483 20V9.46H187.883L187.923 11.62L187.463 11.86C187.596 11.38 187.856 10.9467 188.243 10.56C188.63 10.16 189.09 9.84 189.623 9.6C190.156 9.36 190.703 9.24 191.263 9.24C192.063 9.24 192.73 9.4 193.263 9.72C193.81 10.04 194.216 10.52 194.483 11.16C194.763 11.8 194.903 12.6 194.903 13.56V20H192.443V13.74C192.443 13.2067 192.37 12.7667 192.223 12.42C192.076 12.06 191.85 11.8 191.543 11.64C191.236 11.4667 190.863 11.3867 190.423 11.4C190.063 11.4 189.73 11.46 189.423 11.58C189.13 11.6867 188.87 11.8467 188.643 12.06C188.43 12.26 188.256 12.4933 188.123 12.76C188.003 13.0267 187.943 13.32 187.943 13.64V20H186.723C186.483 20 186.256 20 186.043 20C185.843 20 185.656 20 185.483 20ZM198.81 20V6.78H201.27V20H198.81ZM196.73 11.74V9.46H203.57V11.74H196.73ZM209.355 20.2C208.421 20.2 207.581 20.0467 206.835 19.74C206.101 19.42 205.501 18.9667 205.035 18.38L206.635 17C207.035 17.4533 207.481 17.78 207.975 17.98C208.468 18.18 208.988 18.28 209.535 18.28C209.761 18.28 209.961 18.2533 210.135 18.2C210.321 18.1467 210.481 18.0667 210.615 17.96C210.748 17.8533 210.848 17.7333 210.915 17.6C210.995 17.4533 211.035 17.2933 211.035 17.12C211.035 16.8 210.915 16.5467 210.675 16.36C210.541 16.2667 210.328 16.1667 210.035 16.06C209.755 15.9533 209.388 15.8467 208.935 15.74C208.215 15.5533 207.615 15.34 207.135 15.1C206.655 14.8467 206.281 14.5667 206.015 14.26C205.788 14.0067 205.615 13.72 205.495 13.4C205.388 13.08 205.335 12.7333 205.335 12.36C205.335 11.8933 205.435 11.4733 205.635 11.1C205.848 10.7133 206.135 10.38 206.495 10.1C206.855 9.82 207.275 9.60667 207.755 9.46C208.235 9.31333 208.735 9.24 209.255 9.24C209.788 9.24 210.301 9.30667 210.795 9.44C211.301 9.57333 211.768 9.76667 212.195 10.02C212.635 10.26 213.008 10.5533 213.315 10.9L211.955 12.4C211.701 12.16 211.421 11.9467 211.115 11.76C210.821 11.5733 210.521 11.4267 210.215 11.32C209.908 11.2 209.621 11.14 209.355 11.14C209.101 11.14 208.875 11.1667 208.675 11.22C208.475 11.26 208.308 11.3267 208.175 11.42C208.041 11.5133 207.935 11.6333 207.855 11.78C207.788 11.9133 207.755 12.0733 207.755 12.26C207.768 12.42 207.808 12.5733 207.875 12.72C207.955 12.8533 208.061 12.9667 208.195 13.06C208.341 13.1533 208.561 13.26 208.855 13.38C209.148 13.5 209.528 13.6133 209.995 13.72C210.648 13.8933 211.195 14.0867 211.635 14.3C212.075 14.5133 212.421 14.76 212.675 15.04C212.928 15.28 213.108 15.56 213.215 15.88C213.321 16.2 213.375 16.5533 213.375 16.94C213.375 17.5667 213.195 18.1267 212.835 18.62C212.488 19.1133 212.008 19.5 211.395 19.78C210.795 20.06 210.115 20.2 209.355 20.2ZM224.66 20V8.4H220.66V6H231.38V8.4H227.26V20H224.66ZM235.479 20.2C234.612 20.2 233.825 19.96 233.119 19.48C232.412 19 231.845 18.3467 231.419 17.52C230.992 16.6933 230.779 15.7533 230.779 14.7C230.779 13.6467 230.992 12.7067 231.419 11.88C231.845 11.0533 232.425 10.4067 233.159 9.94C233.892 9.47333 234.719 9.24 235.639 9.24C236.172 9.24 236.659 9.32 237.099 9.48C237.539 9.62667 237.925 9.84 238.259 10.12C238.592 10.4 238.865 10.72 239.079 11.08C239.305 11.44 239.459 11.8267 239.539 12.24L238.999 12.1V9.46H241.479V20H238.979V17.48L239.559 17.38C239.465 17.74 239.292 18.0933 239.039 18.44C238.799 18.7733 238.492 19.0733 238.119 19.34C237.759 19.5933 237.352 19.8 236.899 19.96C236.459 20.12 235.985 20.2 235.479 20.2ZM236.159 18.02C236.732 18.02 237.239 17.88 237.679 17.6C238.119 17.32 238.459 16.9333 238.699 16.44C238.952 15.9333 239.079 15.3533 239.079 14.7C239.079 14.06 238.952 13.4933 238.699 13C238.459 12.5067 238.119 12.12 237.679 11.84C237.239 11.56 236.732 11.42 236.159 11.42C235.585 11.42 235.079 11.56 234.639 11.84C234.212 12.12 233.879 12.5067 233.639 13C233.399 13.4933 233.279 14.06 233.279 14.7C233.279 15.3533 233.399 15.9333 233.639 16.44C233.879 16.9333 234.212 17.32 234.639 17.6C235.079 17.88 235.585 18.02 236.159 18.02ZM250.151 20.2C249.684 20.2 249.224 20.1333 248.771 20C248.331 19.8533 247.931 19.66 247.571 19.42C247.211 19.18 246.911 18.9133 246.671 18.62C246.431 18.3133 246.271 18.0067 246.191 17.7L246.771 17.44L246.711 19.96H244.331V5.2H246.791V11.9L246.351 11.7C246.417 11.38 246.564 11.08 246.791 10.8C247.031 10.5067 247.324 10.2467 247.671 10.02C248.017 9.78 248.397 9.59333 248.811 9.46C249.224 9.31333 249.644 9.24 250.071 9.24C251.017 9.24 251.851 9.47333 252.571 9.94C253.304 10.4067 253.877 11.0533 254.291 11.88C254.717 12.7067 254.931 13.6467 254.931 14.7C254.931 15.7667 254.724 16.7133 254.311 17.54C253.897 18.3667 253.324 19.02 252.591 19.5C251.871 19.9667 251.057 20.2 250.151 20.2ZM249.631 18.04C250.191 18.04 250.691 17.9 251.131 17.62C251.571 17.3267 251.917 16.9333 252.171 16.44C252.424 15.9333 252.551 15.3533 252.551 14.7C252.551 14.06 252.424 13.4933 252.171 13C251.931 12.5067 251.591 12.12 251.151 11.84C250.711 11.56 250.204 11.42 249.631 11.42C249.057 11.42 248.551 11.56 248.111 11.84C247.671 12.12 247.324 12.5067 247.071 13C246.817 13.4933 246.691 14.06 246.691 14.7C246.691 15.3533 246.817 15.9333 247.071 16.44C247.324 16.9333 247.671 17.3267 248.111 17.62C248.551 17.9 249.057 18.04 249.631 18.04ZM257.144 20V5.2H259.624V20H257.144ZM267.259 20.2C266.152 20.2 265.172 19.9667 264.319 19.5C263.479 19.0333 262.819 18.4 262.339 17.6C261.872 16.7867 261.639 15.86 261.639 14.82C261.639 13.9933 261.772 13.24 262.039 12.56C262.306 11.88 262.672 11.2933 263.139 10.8C263.619 10.2933 264.186 9.90667 264.839 9.64C265.506 9.36 266.232 9.22 267.019 9.22C267.712 9.22 268.359 9.35333 268.959 9.62C269.559 9.88667 270.079 10.2533 270.519 10.72C270.959 11.1733 271.292 11.72 271.519 12.36C271.759 12.9867 271.872 13.6733 271.859 14.42L271.839 15.28H263.299L262.839 13.6H269.779L269.459 13.94V13.5C269.419 13.0867 269.286 12.7267 269.059 12.42C268.832 12.1 268.539 11.8533 268.179 11.68C267.832 11.4933 267.446 11.4 267.019 11.4C266.366 11.4 265.812 11.5267 265.359 11.78C264.919 12.0333 264.586 12.4 264.359 12.88C264.132 13.3467 264.019 13.9333 264.019 14.64C264.019 15.32 264.159 15.9133 264.439 16.42C264.732 16.9267 265.139 17.32 265.659 17.6C266.192 17.8667 266.812 18 267.519 18C268.012 18 268.466 17.92 268.879 17.76C269.292 17.6 269.739 17.3133 270.219 16.9L271.439 18.6C271.079 18.9333 270.666 19.22 270.199 19.46C269.746 19.6867 269.266 19.8667 268.759 20C268.252 20.1333 267.752 20.2 267.259 20.2Z" fill="#292A2B" />
            </svg>
        </span>
    </div>
    <div class="headerbuttons">
        <span @onclick="async () => { Filter = new(); Paginate(1); await OnInitializedAsync();}">
            <svg width="89" height="38" viewBox="0 0 89 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="89" height="38" rx="4" fill="#292A2B" />
                <path d="M16.4396 24.4261C16.5371 24.4727 16.6439 24.4967 16.752 24.4964C16.9183 24.4964 17.0796 24.4398 17.2094 24.3359C17.3392 24.232 17.4298 24.0869 17.4662 23.9247C17.5026 23.7625 17.4827 23.5926 17.4097 23.4432C17.3367 23.2938 17.215 23.1737 17.0647 23.1027C16.1347 22.6641 15.3813 21.9225 14.928 20.9996C14.4747 20.0767 14.3484 19.0272 14.5698 18.023C14.7912 17.0189 15.3471 16.1198 16.1465 15.4731C16.616 15.0932 17.1531 14.8137 17.7247 14.6462L17.1747 15.7462C17.119 15.8577 17.0927 15.9816 17.0984 16.1061C17.104 16.2307 17.1413 16.3517 17.2068 16.4577C17.2723 16.5638 17.3638 16.6513 17.4726 16.7121C17.5815 16.7728 17.704 16.8048 17.8287 16.8049C17.9648 16.8055 18.0983 16.7678 18.2141 16.6963C18.3299 16.6247 18.4233 16.5222 18.4838 16.4002L19.6545 14.0587C19.7412 13.8851 19.7554 13.6842 19.694 13.5001C19.6326 13.3161 19.5005 13.1639 19.3269 13.0772C19.2197 13.0236 19.102 12.9977 18.9843 13.0003C18.9763 13 18.9683 13 18.9603 13C17.6004 13.009 16.2839 13.4797 15.2266 14.335C14.1693 15.1902 13.4338 16.3793 13.1409 17.7073C12.8479 19.0353 13.0148 20.4234 13.6141 21.6441C14.2135 22.8648 15.2097 23.8457 16.4396 24.4261Z" fill="white" />
                <path d="M19.0193 24.9998C19.0246 24.9999 19.0299 25 19.0352 25H19.0397C20.3996 24.991 21.7161 24.5203 22.7734 23.665C23.8307 22.8098 24.5662 21.6207 24.8591 20.2927C25.1521 18.9647 24.9852 17.5766 24.3859 16.3559C23.7865 15.1351 22.7903 14.1542 21.5604 13.5739C21.463 13.5272 21.3563 13.503 21.2483 13.503C21.1095 13.5036 20.9737 13.5433 20.8565 13.6176C20.7393 13.692 20.6454 13.7979 20.5858 13.9232C20.5448 14.0101 20.5213 14.1042 20.5167 14.2001C20.5121 14.2961 20.5265 14.392 20.5589 14.4824C20.5914 14.5728 20.6414 14.656 20.706 14.727C20.7707 14.7981 20.8487 14.8558 20.9356 14.8967C21.8655 15.3354 22.6188 16.0771 23.0721 17C23.5253 17.923 23.6516 18.9725 23.4302 19.9767C23.2088 20.9808 22.6528 21.8799 21.8535 22.5267C21.384 22.9066 20.847 23.1861 20.2753 23.3536L20.8252 22.2538C20.912 22.0802 20.9262 21.8793 20.8647 21.6952C20.8033 21.5112 20.6713 21.359 20.4977 21.2723C20.3241 21.1856 20.1232 21.1714 19.9391 21.2328C19.755 21.2943 19.6029 21.4263 19.5162 21.5999L18.3454 23.9413C18.2898 24.0528 18.2635 24.1767 18.2691 24.3013C18.2747 24.4258 18.312 24.5468 18.3775 24.6528C18.443 24.7589 18.5346 24.8464 18.6434 24.9072C18.7522 24.968 18.8748 24.9999 18.9994 25C19.006 25 19.0127 25 19.0193 24.9998Z" fill="white" />
                <path d="M35.128 24V15.6H38.776C39.28 15.6 39.74 15.72 40.156 15.96C40.572 16.192 40.9 16.512 41.14 16.92C41.388 17.32 41.512 17.772 41.512 18.276C41.512 18.756 41.388 19.2 41.14 19.608C40.9 20.008 40.572 20.328 40.156 20.568C39.748 20.8 39.288 20.916 38.776 20.916H36.652V24H35.128ZM40 24L37.864 20.208L39.472 19.908L41.848 24.012L40 24ZM36.652 19.56H38.788C39.02 19.56 39.22 19.508 39.388 19.404C39.564 19.292 39.7 19.14 39.796 18.948C39.892 18.756 39.94 18.544 39.94 18.312C39.94 18.048 39.88 17.82 39.76 17.628C39.64 17.436 39.472 17.284 39.256 17.172C39.04 17.06 38.792 17.004 38.512 17.004H36.652V19.56ZM45.9608 24.12C45.2968 24.12 44.7088 23.98 44.1968 23.7C43.6928 23.42 43.2968 23.04 43.0088 22.56C42.7288 22.072 42.5888 21.516 42.5888 20.892C42.5888 20.396 42.6688 19.944 42.8288 19.536C42.9888 19.128 43.2088 18.776 43.4888 18.48C43.7768 18.176 44.1168 17.944 44.5088 17.784C44.9088 17.616 45.3448 17.532 45.8168 17.532C46.2328 17.532 46.6208 17.612 46.9808 17.772C47.3408 17.932 47.6528 18.152 47.9168 18.432C48.1808 18.704 48.3808 19.032 48.5168 19.416C48.6608 19.792 48.7288 20.204 48.7208 20.652L48.7088 21.168H43.5848L43.3088 20.16H47.4728L47.2808 20.364V20.1C47.2568 19.852 47.1768 19.636 47.0408 19.452C46.9048 19.26 46.7288 19.112 46.5128 19.008C46.3048 18.896 46.0728 18.84 45.8168 18.84C45.4248 18.84 45.0928 18.916 44.8208 19.068C44.5568 19.22 44.3568 19.44 44.2208 19.728C44.0848 20.008 44.0168 20.36 44.0168 20.784C44.0168 21.192 44.1008 21.548 44.2688 21.852C44.4448 22.156 44.6888 22.392 45.0008 22.56C45.3208 22.72 45.6928 22.8 46.1168 22.8C46.4128 22.8 46.6848 22.752 46.9328 22.656C47.1808 22.56 47.4488 22.388 47.7368 22.14L48.4688 23.16C48.2528 23.36 48.0048 23.532 47.7248 23.676C47.4528 23.812 47.1648 23.92 46.8608 24C46.5568 24.08 46.2568 24.12 45.9608 24.12ZM50.6041 24V17.112C50.6041 16.728 50.6881 16.388 50.8561 16.092C51.0241 15.788 51.2561 15.552 51.5521 15.384C51.8481 15.208 52.1921 15.12 52.5841 15.12C52.8561 15.12 53.1081 15.168 53.3401 15.264C53.5721 15.352 53.7681 15.476 53.9281 15.636L53.4721 16.752C53.3681 16.664 53.2561 16.596 53.1361 16.548C53.0241 16.5 52.9161 16.476 52.8121 16.476C52.6521 16.476 52.5161 16.504 52.4041 16.56C52.3001 16.608 52.2201 16.684 52.1641 16.788C52.1161 16.892 52.0921 17.016 52.0921 17.16V24H51.3481C51.2041 24 51.0681 24 50.9401 24C50.8201 24 50.7081 24 50.6041 24ZM49.6201 19.104V17.808H53.5201V19.104H49.6201ZM54.739 24V17.676H56.191L56.215 19.692L56.011 19.236C56.099 18.916 56.251 18.628 56.467 18.372C56.683 18.116 56.931 17.916 57.211 17.772C57.499 17.62 57.799 17.544 58.111 17.544C58.247 17.544 58.375 17.556 58.495 17.58C58.623 17.604 58.727 17.632 58.807 17.664L58.411 19.284C58.323 19.236 58.215 19.196 58.087 19.164C57.959 19.132 57.831 19.116 57.703 19.116C57.503 19.116 57.311 19.156 57.127 19.236C56.951 19.308 56.795 19.412 56.659 19.548C56.523 19.684 56.415 19.844 56.335 20.028C56.263 20.204 56.227 20.404 56.227 20.628V24H54.739ZM62.4843 24.12C61.8203 24.12 61.2323 23.98 60.7203 23.7C60.2163 23.42 59.8203 23.04 59.5323 22.56C59.2523 22.072 59.1123 21.516 59.1123 20.892C59.1123 20.396 59.1923 19.944 59.3523 19.536C59.5123 19.128 59.7323 18.776 60.0123 18.48C60.3003 18.176 60.6403 17.944 61.0323 17.784C61.4323 17.616 61.8683 17.532 62.3403 17.532C62.7563 17.532 63.1443 17.612 63.5043 17.772C63.8643 17.932 64.1763 18.152 64.4403 18.432C64.7043 18.704 64.9043 19.032 65.0403 19.416C65.1843 19.792 65.2523 20.204 65.2443 20.652L65.2323 21.168H60.1083L59.8323 20.16H63.9963L63.8043 20.364V20.1C63.7803 19.852 63.7003 19.636 63.5643 19.452C63.4283 19.26 63.2523 19.112 63.0363 19.008C62.8283 18.896 62.5963 18.84 62.3403 18.84C61.9483 18.84 61.6163 18.916 61.3443 19.068C61.0803 19.22 60.8803 19.44 60.7443 19.728C60.6083 20.008 60.5403 20.36 60.5403 20.784C60.5403 21.192 60.6243 21.548 60.7923 21.852C60.9683 22.156 61.2123 22.392 61.5243 22.56C61.8443 22.72 62.2163 22.8 62.6403 22.8C62.9363 22.8 63.2083 22.752 63.4563 22.656C63.7043 22.56 63.9723 22.388 64.2603 22.14L64.9922 23.16C64.7763 23.36 64.5283 23.532 64.2483 23.676C63.9763 23.812 63.6883 23.92 63.3843 24C63.0803 24.08 62.7803 24.12 62.4843 24.12ZM68.6635 24.12C68.1035 24.12 67.5995 24.028 67.1515 23.844C66.7115 23.652 66.3515 23.38 66.0715 23.028L67.0315 22.2C67.2715 22.472 67.5395 22.668 67.8355 22.788C68.1315 22.908 68.4435 22.968 68.7715 22.968C68.9075 22.968 69.0275 22.952 69.1315 22.92C69.2435 22.888 69.3395 22.84 69.4195 22.776C69.4995 22.712 69.5595 22.64 69.5995 22.56C69.6475 22.472 69.6715 22.376 69.6715 22.272C69.6715 22.08 69.5995 21.928 69.4555 21.816C69.3755 21.76 69.2475 21.7 69.0715 21.636C68.9035 21.572 68.6835 21.508 68.4115 21.444C67.9795 21.332 67.6195 21.204 67.3315 21.06C67.0435 20.908 66.8195 20.74 66.6595 20.556C66.5235 20.404 66.4195 20.232 66.3475 20.04C66.2835 19.848 66.2515 19.64 66.2515 19.416C66.2515 19.136 66.3115 18.884 66.4315 18.66C66.5595 18.428 66.7315 18.228 66.9475 18.06C67.1635 17.892 67.4155 17.764 67.7035 17.676C67.9915 17.588 68.2915 17.544 68.6035 17.544C68.9235 17.544 69.2315 17.584 69.5275 17.664C69.8315 17.744 70.1115 17.86 70.3675 18.012C70.6315 18.156 70.8555 18.332 71.0395 18.54L70.2235 19.44C70.0715 19.296 69.9035 19.168 69.7195 19.056C69.5435 18.944 69.3635 18.856 69.1795 18.792C68.9955 18.72 68.8235 18.684 68.6635 18.684C68.5115 18.684 68.3755 18.7 68.2555 18.732C68.1355 18.756 68.0355 18.796 67.9555 18.852C67.8755 18.908 67.8115 18.98 67.7635 19.068C67.7235 19.148 67.7035 19.244 67.7035 19.356C67.7115 19.452 67.7355 19.544 67.7755 19.632C67.8235 19.712 67.8875 19.78 67.9675 19.836C68.0555 19.892 68.1875 19.956 68.3635 20.028C68.5395 20.1 68.7675 20.168 69.0475 20.232C69.4395 20.336 69.7675 20.452 70.0315 20.58C70.2955 20.708 70.5035 20.856 70.6555 21.024C70.8075 21.168 70.9155 21.336 70.9795 21.528C71.0435 21.72 71.0755 21.932 71.0755 22.164C71.0755 22.54 70.9675 22.876 70.7515 23.172C70.5435 23.468 70.2555 23.7 69.8875 23.868C69.5275 24.036 69.1195 24.12 68.6635 24.12ZM72.3757 24V15.12H73.8397V18.972L73.5637 19.116C73.6437 18.828 73.7997 18.568 74.0317 18.336C74.2637 18.096 74.5397 17.904 74.8597 17.76C75.1797 17.616 75.5077 17.544 75.8437 17.544C76.3237 17.544 76.7237 17.64 77.0437 17.832C77.3717 18.024 77.6157 18.312 77.7757 18.696C77.9437 19.08 78.0277 19.56 78.0277 20.136V24H76.5517V20.244C76.5517 19.924 76.5077 19.66 76.4197 19.452C76.3317 19.236 76.1957 19.08 76.0117 18.984C75.8277 18.88 75.6037 18.832 75.3397 18.84C75.1237 18.84 74.9237 18.876 74.7397 18.948C74.5637 19.012 74.4077 19.108 74.2717 19.236C74.1437 19.356 74.0397 19.496 73.9597 19.656C73.8877 19.816 73.8517 19.992 73.8517 20.184V24H73.1197C72.9757 24 72.8397 24 72.7117 24C72.5917 24 72.4797 24 72.3757 24Z" fill="white" />
            </svg>
        </span>
        <span @onclick="async () => {ShowEmailTemplateTenantModal();}">
            <svg width="79" height="38" viewBox="0 0 79 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="79" height="38" fill="#292A2B" />
                <path d="M17.14 23.1C16.76 23.1 16.4067 23.05 16.08 22.95C15.76 22.85 15.4667 22.7 15.2 22.5C14.94 22.2933 14.7067 22.04 14.5 21.74L15.24 20.89C15.5667 21.3567 15.88 21.68 16.18 21.86C16.48 22.04 16.8367 22.13 17.25 22.13C17.5033 22.13 17.7333 22.09 17.94 22.01C18.1467 21.93 18.31 21.82 18.43 21.68C18.55 21.54 18.61 21.38 18.61 21.2C18.61 21.08 18.59 20.9667 18.55 20.86C18.51 20.7533 18.4467 20.6567 18.36 20.57C18.28 20.4833 18.1733 20.4033 18.04 20.33C17.9133 20.2567 17.7633 20.1933 17.59 20.14C17.4167 20.08 17.2167 20.03 16.99 19.99C16.63 19.9167 16.3167 19.82 16.05 19.7C15.7833 19.58 15.56 19.43 15.38 19.25C15.2 19.07 15.0667 18.8667 14.98 18.64C14.8933 18.4067 14.85 18.1467 14.85 17.86C14.85 17.58 14.91 17.32 15.03 17.08C15.1567 16.84 15.3267 16.6333 15.54 16.46C15.76 16.28 16.0167 16.1433 16.31 16.05C16.6033 15.95 16.92 15.9 17.26 15.9C17.62 15.9 17.95 15.9467 18.25 16.04C18.55 16.1333 18.8167 16.2733 19.05 16.46C19.2833 16.64 19.4767 16.8633 19.63 17.13L18.87 17.88C18.7367 17.66 18.5867 17.4767 18.42 17.33C18.2533 17.1767 18.07 17.0633 17.87 16.99C17.67 16.91 17.4567 16.87 17.23 16.87C16.97 16.87 16.7433 16.91 16.55 16.99C16.3567 17.07 16.2033 17.1833 16.09 17.33C15.9833 17.47 15.93 17.6367 15.93 17.83C15.93 17.97 15.9567 18.1 16.01 18.22C16.0633 18.3333 16.1433 18.4367 16.25 18.53C16.3633 18.6167 16.5133 18.6967 16.7 18.77C16.8867 18.8367 17.11 18.8967 17.37 18.95C17.7367 19.03 18.0633 19.1333 18.35 19.26C18.6367 19.38 18.88 19.5233 19.08 19.69C19.28 19.8567 19.43 20.0433 19.53 20.25C19.6367 20.4567 19.69 20.68 19.69 20.92C19.69 21.3667 19.5867 21.7533 19.38 22.08C19.1733 22.4067 18.88 22.66 18.5 22.84C18.12 23.0133 17.6667 23.1 17.14 23.1ZM23.4102 23.1C22.8702 23.1 22.3902 22.9867 21.9702 22.76C21.5569 22.5267 21.2302 22.21 20.9902 21.81C20.7569 21.41 20.6402 20.95 20.6402 20.43C20.6402 20.0167 20.7069 19.64 20.8402 19.3C20.9736 18.96 21.1569 18.6667 21.3902 18.42C21.6302 18.1667 21.9136 17.9733 22.2402 17.84C22.5736 17.7 22.9336 17.63 23.3202 17.63C23.6602 17.63 23.9769 17.6967 24.2702 17.83C24.5636 17.9567 24.8169 18.1333 25.0302 18.36C25.2502 18.5867 25.4169 18.8567 25.5302 19.17C25.6502 19.4767 25.7069 19.8133 25.7002 20.18L25.6902 20.62H21.4002L21.1702 19.8H24.8002L24.6502 19.97V19.73C24.6302 19.51 24.5569 19.3133 24.4302 19.14C24.3036 18.9667 24.1436 18.83 23.9502 18.73C23.7569 18.63 23.5469 18.58 23.3202 18.58C22.9602 18.58 22.6569 18.65 22.4102 18.79C22.1636 18.9233 21.9769 19.1233 21.8502 19.39C21.7236 19.65 21.6602 19.9733 21.6602 20.36C21.6602 20.7267 21.7369 21.0467 21.8902 21.32C22.0436 21.5867 22.2602 21.7933 22.5402 21.94C22.8202 22.0867 23.1436 22.16 23.5102 22.16C23.7702 22.16 24.0102 22.1167 24.2302 22.03C24.4569 21.9433 24.7002 21.7867 24.9602 21.56L25.4802 22.29C25.3202 22.45 25.1236 22.59 24.8902 22.71C24.6636 22.83 24.4202 22.9267 24.1602 23C23.9069 23.0667 23.6569 23.1 23.4102 23.1ZM26.8196 23V17.75H27.8496V18.83L27.6696 18.95C27.7363 18.7167 27.8663 18.5033 28.0596 18.31C28.2529 18.11 28.4829 17.95 28.7496 17.83C29.0163 17.7033 29.2896 17.64 29.5696 17.64C29.9696 17.64 30.3029 17.72 30.5696 17.88C30.8363 18.0333 31.0363 18.27 31.1696 18.59C31.3029 18.91 31.3696 19.3133 31.3696 19.8V23H30.3396V19.87C30.3396 19.57 30.2996 19.3233 30.2196 19.13C30.1396 18.93 30.0163 18.7867 29.8496 18.7C29.6829 18.6067 29.4763 18.5667 29.2296 18.58C29.0296 18.58 28.8463 18.6133 28.6796 18.68C28.5129 18.74 28.3663 18.8267 28.2396 18.94C28.1196 19.0533 28.0229 19.1867 27.9496 19.34C27.8829 19.4867 27.8496 19.6467 27.8496 19.82V23H27.3396C27.2529 23 27.1663 23 27.0796 23C26.9929 23 26.9063 23 26.8196 23ZM34.9569 23.1C34.4769 23.1 34.0469 22.9833 33.6669 22.75C33.2935 22.51 32.9969 22.1867 32.7769 21.78C32.5569 21.3667 32.4469 20.8967 32.4469 20.37C32.4469 19.8433 32.5535 19.3767 32.7669 18.97C32.9869 18.5567 33.2835 18.2333 33.6569 18C34.0302 17.76 34.4535 17.64 34.9269 17.64C35.1869 17.64 35.4369 17.6833 35.6769 17.77C35.9235 17.85 36.1435 17.9633 36.3369 18.11C36.5302 18.25 36.6802 18.4067 36.7869 18.58C36.9002 18.7467 36.9569 18.9167 36.9569 19.09L36.6569 19.11V15.6H37.6869V23H36.6569V21.75H36.8569C36.8569 21.91 36.8035 22.07 36.6969 22.23C36.5902 22.3833 36.4469 22.5267 36.2669 22.66C36.0935 22.7933 35.8902 22.9 35.6569 22.98C35.4302 23.06 35.1969 23.1 34.9569 23.1ZM35.0969 22.21C35.4102 22.21 35.6869 22.13 35.9269 21.97C36.1669 21.81 36.3535 21.5933 36.4869 21.32C36.6269 21.04 36.6969 20.7233 36.6969 20.37C36.6969 20.0167 36.6269 19.7033 36.4869 19.43C36.3535 19.15 36.1669 18.93 35.9269 18.77C35.6869 18.61 35.4102 18.53 35.0969 18.53C34.7835 18.53 34.5069 18.61 34.2669 18.77C34.0269 18.93 33.8369 19.15 33.6969 19.43C33.5635 19.7033 33.4969 20.0167 33.4969 20.37C33.4969 20.7233 33.5635 21.04 33.6969 21.32C33.8369 21.5933 34.0269 21.81 34.2669 21.97C34.5069 22.13 34.7835 22.21 35.0969 22.21ZM42.1675 23V16H46.5775V17.04H43.2475V21.96H46.5775V23H42.1675ZM42.6575 19.93V18.89H46.0275V19.93H42.6575ZM48.0301 23V17.75H49.0701V18.87L48.8801 18.99C48.9334 18.8167 49.0167 18.65 49.1301 18.49C49.2501 18.33 49.3934 18.19 49.5601 18.07C49.7334 17.9433 49.9167 17.8433 50.1101 17.77C50.3101 17.6967 50.5134 17.66 50.7201 17.66C51.0201 17.66 51.2834 17.71 51.5101 17.81C51.7367 17.91 51.9234 18.06 52.0701 18.26C52.2167 18.46 52.3234 18.71 52.3901 19.01L52.2301 18.97L52.3001 18.8C52.3734 18.6467 52.4734 18.5033 52.6001 18.37C52.7334 18.23 52.8834 18.1067 53.0501 18C53.2167 17.8933 53.3934 17.81 53.5801 17.75C53.7667 17.69 53.9501 17.66 54.1301 17.66C54.5234 17.66 54.8467 17.74 55.1001 17.9C55.3601 18.06 55.5534 18.3033 55.6801 18.63C55.8134 18.9567 55.8801 19.3633 55.8801 19.85V23H54.8401V19.91C54.8401 19.61 54.8001 19.3667 54.7201 19.18C54.6467 18.9867 54.5334 18.8433 54.3801 18.75C54.2267 18.6567 54.0301 18.61 53.7901 18.61C53.6034 18.61 53.4267 18.6433 53.2601 18.71C53.1001 18.77 52.9601 18.8567 52.8401 18.97C52.7201 19.0833 52.6267 19.2167 52.5601 19.37C52.4934 19.5167 52.4601 19.68 52.4601 19.86V23H51.4201V19.89C51.4201 19.6167 51.3801 19.3867 51.3001 19.2C51.2201 19.0067 51.1034 18.86 50.9501 18.76C50.7967 18.66 50.6101 18.61 50.3901 18.61C50.2034 18.61 50.0301 18.6433 49.8701 18.71C49.7101 18.77 49.5701 18.8567 49.4501 18.97C49.3301 19.0767 49.2367 19.2067 49.1701 19.36C49.1034 19.5067 49.0701 19.6667 49.0701 19.84V23H48.0301ZM59.2791 23.1C58.8457 23.1 58.4491 22.98 58.0891 22.74C57.7357 22.5 57.4524 22.1733 57.2391 21.76C57.0257 21.3467 56.9191 20.88 56.9191 20.36C56.9191 19.8333 57.0257 19.3667 57.2391 18.96C57.4591 18.5467 57.7524 18.2233 58.1191 17.99C58.4924 17.7567 58.9091 17.64 59.3691 17.64C59.6424 17.64 59.8924 17.68 60.1191 17.76C60.3457 17.84 60.5424 17.9533 60.7091 18.1C60.8824 18.24 61.0224 18.4033 61.1291 18.59C61.2424 18.7767 61.3124 18.9767 61.3391 19.19L61.1091 19.11V17.75H62.1491V23H61.1091V21.75L61.3491 21.68C61.3091 21.86 61.2257 22.0367 61.0991 22.21C60.9791 22.3767 60.8224 22.5267 60.6291 22.66C60.4424 22.7933 60.2324 22.9 59.9991 22.98C59.7724 23.06 59.5324 23.1 59.2791 23.1ZM59.5491 22.15C59.8624 22.15 60.1391 22.0733 60.3791 21.92C60.6191 21.7667 60.8057 21.5567 60.9391 21.29C61.0791 21.0167 61.1491 20.7067 61.1491 20.36C61.1491 20.02 61.0791 19.7167 60.9391 19.45C60.8057 19.1833 60.6191 18.9733 60.3791 18.82C60.1391 18.6667 59.8624 18.59 59.5491 18.59C59.2424 18.59 58.9691 18.6667 58.7291 18.82C58.4957 18.9733 58.3091 19.1833 58.1691 19.45C58.0357 19.7167 57.9691 20.02 57.9691 20.36C57.9691 20.7067 58.0357 21.0167 58.1691 21.29C58.3091 21.5567 58.4957 21.7667 58.7291 21.92C58.9691 22.0733 59.2424 22.15 59.5491 22.15ZM63.7572 23V17.75H64.7872V23H63.7572ZM64.2572 16.59C64.0372 16.59 63.8672 16.5333 63.7472 16.42C63.6272 16.3067 63.5672 16.1467 63.5672 15.94C63.5672 15.7467 63.6272 15.59 63.7472 15.47C63.8739 15.35 64.0439 15.29 64.2572 15.29C64.4772 15.29 64.6472 15.3467 64.7672 15.46C64.8872 15.5733 64.9472 15.7333 64.9472 15.94C64.9472 16.1333 64.8839 16.29 64.7572 16.41C64.6372 16.53 64.4705 16.59 64.2572 16.59ZM66.3904 23V15.6H67.4204V23H66.3904Z" fill="#F8F8F8" />
            </svg>
        </span>
    </div>
</div>

<div class="TenantMasterTable">
    <div class="TenantMasterTableHeader">
        <div class="SearchBar">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="searchicon">
                <path d="M16.6673 16.6654L15.334 15.332M3.33398 9.66537C3.33398 6.16756 6.16951 3.33203 9.66732 3.33203C13.1651 3.33203 16.0006 6.16756 16.0006 9.66537C16.0006 13.1632 13.1651 15.9987 9.66732 15.9987C6.16951 15.9987 3.33398 13.1632 3.33398 9.66537Z" stroke="#BBBBBB" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <input type="text" placeholder="Press Enter to Search" class="Searchbarbar" @bind-value="Filter.SearchTerm" @oninput="e => Filter.SearchTerm = e.Value?.ToString()" @onkeydown="HandleKeyDown">
            <span class="searchButton" @onclick="GetPendingPaymentsAsync">
                Search
            </span>
        </div>
        <div class="buttons">
            <span class="buttonbutton" @onclick="@OpenFilter">Filter</span>
            <span class="buttonbutton" @onclick="@DownloadPendingPaymentAsync">Export Tenant</span>
            <span class="buttonbutton" title="Page Size">
                <select id="pagesizeinput" @bind="PageSize" @bind:after="GetPendingPaymentsAsync">
                    @foreach (var dv in DropdownValues)
                    {
                        <option value="@dv">@dv</option>
                    }
                </select>
            </span>
        </div>
    </div>
    @if (Collection == null)
    {
        <span>No Data Found</span>
    }
    else
    {
        @if (Collection.Any(i => i != null))
        {
            <div class="all-tables">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                            <th>Sl.No</th>
                            <th>TenantId</th>
                            <th>TenantName</th>
                            <th>OnBoardDate</th>
                            <th>LicenseValidity</th>
                            <th>VendorNames</th>
                            <th>LastInvoiceNumber</th>
                            <th>GSTNumber</th>
                            <th>TotalAmount</th>
                            <th>PaidAmount</th>
                            <th>NetAmount</th>
                            <th>PendingAmount</th>
                            <th>NextDueDate</th>
                            <th>Status</th>
                            <th>Source</th>
                            <th>BillingType</th>
                            <th>TDS</th>
                            <th>VendorEmail</th>

                        </tr>
                        </thead>
                        <tbody>
                            @foreach (var items in Collection.Select((value, i) => new { value, i }))
                            {
                                <tr>

                                    <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                                    <td><span class="sort-link" @onclick="@(async () => await NavigateToComponent(items.value?.TenantId))" style="cursor :pointer"> @items.value?.TenantId </span></td>
                                    <td>@items?.value?.TenantName</td>
                                    <td>@items?.value?.OnBoardDate?.ToString("dd/MM/yyyy")</td>
                                    <td>@items?.value?.LicenseValidity?.ToString("dd/MM/yyyy")</td>
                                    <td>@items?.value?.VendorNames</td>
                                    <td>@items?.value?.LastInvoiceNumber</td>
                                    <td>@items?.value?.GSTNumber</td>
                                    <td>@items?.value?.TotalAmount</td>
                                    <td>@items?.value?.PaidAmount</td>
                                    <td>@items?.value?.NetAmount</td>
                                    <td>@items?.value?.PendingAmount</td>
                                    <td>@items?.value?.NextDueDate?.ToString("dd/MM/yyyy")</td>
                                    <td>@items?.value?.Status</td>
                                    <td>@items?.value?.Source</td>
                                    <td>@items?.value?.BillingType</td>
                                    <td>@items?.value?.TDS</td>
                                    <td>@items?.value?.VendorEmail</td>

                                </tr>
                            }
                        </tbody>
                        
                    </table>
                </div>
               
            </div>
        }
        else
        {
            <span>No Data Found</span>
        }
    }

    <div class="tenantmasterfooter">
        <div class="pagesize">

            <div class="pagesizebox">
                
                <span>@ShowingCount of @TotalCount</span>
            </div>
        </div>
        <div class="pagenumber">
            @if (Collection.Count < PageSize)
            {
                <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()">Back</span>
            }
            else
            {
                @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                {
                    if (i < PageNumber || i == 0)
                    {
                        <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()"> &lt;&lt; </span>
                    }
                    else if (i == PageNumber)
                    {
                        <span id="pagenumbercircle">@i</span>
                    }
                    else if (i > PageNumber)
                    {
                        <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber()">&gt;&gt;</span>
                    }
                }
            }



        </div>
    </div>
</div>