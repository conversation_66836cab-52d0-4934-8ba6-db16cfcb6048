﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class AddOnSubscriptionsDto
    {
        private double _perUserCost;
        private bool _gstinclude = true;
        private double _netamount;
        private double _dueAmount;
        private double _paidNetAmount;
        private double _paidGSTAmount;
        private double _gstamt;
        
        public double PerUserCost
        {
            get => _perUserCost;
            set
            {
                _perUserCost = value;
            }
        }
        public bool GstInclude
        {
            get => _gstinclude;
            set
            {
                _gstinclude = value;
            }
        }
        public double NetAmount {
            get
            {
                _netamount = (PerUserCost * SoldLicenses * DaysLeft);
                return Math.Round(_netamount,2);
                //return _netamount;
            }
            set => _netamount = value;
        }
        public int SoldLicenses { get; set; } = 1;
        public double DueAmount
        {
            get => Math.Round(_netamount + _gstamt - _paidNetAmount - _paidGSTAmount,2);
            set => _dueAmount = value;
        }
        public double TotalAmount
        {
            get
            {
                return Math.Round(NetAmount+GstAmount,2);
                //return NetAmount + GstAmount;
            }
            set { }
        }
        public PaymentMode PaymentMode { get; set; }
        public PaymentType Type { get; set; }
        public int DaysLeft { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;
        public double GstAmount
        {
            get
            {
                _gstamt = GstInclude ? (_netamount * (18.0 / 100.0)) : 0;
                return Math.Round(_gstamt,2);
                //return _gstamt;
            }
            set => _gstamt = value;
        }
        public string? Description { get; set; }
        public double PaidNetAmount 
        {
            get => _paidNetAmount;
            set => _paidNetAmount = value;
        }
        public double PaidGSTAmount
        {
            get => _paidGSTAmount;
            set => _paidGSTAmount = value;
        }
        public double PaidTotalAmount
        {
            get => Math.Round((_paidNetAmount + _paidGSTAmount),2);
            set => _netamount = value;
        }
        public DateTime? DueDate { get; set; } = DateTime.UtcNow.AddMonths(1);
        public bool IsDeleted { get; set; }
        public DateTime? DeletedOn { get; set; }
        public DateTime? LastModifiedOn { get; set; } = DateTime.UtcNow;
        public bool IsActive {  get; set; }
        public bool IsExpired {  get; set; }
        public bool IsAddOn {  get; set; }
        public string? TenantId {  get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? LastModifiedBy { get; set; }
    }
}
