﻿using Lrb.Admin.Data.Models.Wrappers;
using Microsoft.AspNetCore.Components.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services.HTTP
{
    public interface IHttpService
    {


        /// <summary>
        /// Executes Http GET Request
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        Task<TResponse> GetAsync<TResponse>(string baseUrl, string resource, Dictionary<string, string> headers = null);
        /// <summary>
        /// Executes Http POST Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="ArgumentNullException"></exception>
        Task<TResponse> PostAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : class;
        /// <summary>
        /// Executes Http PUT Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        Task<TResponse> PutAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO = null, Dictionary<string, string> headers = null) where TBody : class;
        /// <summary>
        /// Executes Http POST Request with filebytes
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="headers"></param>
        /// <param name="fileBytes"></param>
        /// <returns></returns>
        Task<TResponse> PostFileAsync<TResponse>(string baseUrl, string resource, IBrowserFile file, Dictionary<string, string>? headers = null);
        /// <summary>
        /// Executes Http DELETE Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        Task<TResponse> DeleteAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO = null, Dictionary<string, string> headers = null) where TBody : class;
        /// <summary>
        /// Executes Any Http Resquest
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        /// <exception cref="ArgumentNullException"></exception>
        Task<TResponse> ExcuteAsync<TResponse>(HttpRequestMessage request, CancellationToken cancellationToken);
        ///// <summary>
        ///// Executes Http PUT Request
        ///// </summary>
        ///// <typeparam name="TBody"></typeparam>
        ///// <typeparam name="TResponse"></typeparam>
        ///// <param name="resource"></param>
        ///// <param name="DTO"></param>
        ///// <param name="headers"></param>
        ///// <returns name="TResponse"></returns>
        Task<TResponse> PatchAsync<TBody, TResponse>(string baseUrl, string resource, Dictionary<string, string> headers = null);
        /// <summary>
        /// Executes Http POST Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="ArgumentNullException"></exception>
        Task<TResponse> PostSmsAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : Dictionary<string, string>;
        Task<bool> PostAsync<T1, T2>(string url, T1 formContent, Dictionary<string, string> headers);
    }
}
