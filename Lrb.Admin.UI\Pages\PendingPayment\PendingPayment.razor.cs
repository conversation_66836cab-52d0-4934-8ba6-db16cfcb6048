﻿using System.Collections.ObjectModel;
using System.Globalization;
using Blazored.LocalStorage;
using iText.Kernel.Pdf;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Utility;
using Lrb.Admin.UI.Pages.Login;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Radzen.Blazor;
using Radzen.Blazor.Rendering;
using Radzen;
using Lrb.Admin.Data.Settings;
using Lrb.Admin.Data.Services.HTTP;
using Query = SqlKata.Query;
using iText.Forms.Fields;
using iText.Forms;
using RestSharp;
using Lrb.Admin.UI.Pages.PendingPayment;
using Lrb.Admin.UI.Pages.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.CodeAnalysis.FlowAnalysis.DataFlow;
using Blazored.Modal;
using Lrb.Admin.Data.Models.Identity;
using Lrb.Admin.UI.Modal.Notification;
using Blazored.Modal.Services;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Reflection.Metadata;


namespace Lrb.Admin.UI.Pages.PendingPayment
{
    [Authorize(Roles = "Admin")]
    public partial class PendingPayment
    {
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public NavigationManager? Navigation { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public SpinnerService? SpinnerService { get; set; }
        [Inject]
        public IEmailService? _emailService { get; set; }
        [Inject]
        public IModalService Modal { get; set; }
        public PendingPaymentFilterParameter? Filter { get; set; } = new();
        public int PageSize { get; set; } = 10;
        public int PageNumber { get; set; } = 1;
        public double MaxPageNumber { get; set; }
        public int? TotalCount { get; set; }

        public List<int> Count = new() { 10, 20, 30, 40, 50, 100 };
        public int ShowingCount { get; set; }
        public ObservableCollection<GetPendingPaymentModel> Collection { get; set; } = new();
        public List<GetPendingPaymentModel> List { get; set; } = new();
        public string? Token { get; set; }
        public RadzenButton? button { get; set; }
        public Popup? popup { get; set; }
        public List<int> DropdownValues { get; set; } = new List<int> { 10, 25, 50, 100, 500 };
        public string? TenantId { get; set; }

        private readonly LrbApiEndPoints? _apiEndPoints;
        private readonly IHttpService? _httpService;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                await GetPendingPaymentsAsync();
            }
            catch (Exception ex)
            {

            }
        }
        public async Task GetPendingPaymentsAsync()
        {
            try
            {
                if (PageSize > Filter.PageSize)
                {
                    PageNumber = 1;
                }
                await GetTrueFilter();
                PagedResponse<GetPendingPaymentModel, string> response = null;
                if (Filter == null)
                {
                    TotalCount = 0;
                }
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await PaymentService.GetAllPendingPaymentAsync(Filter);
                });
                while (!fetchTask.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetPendingPaymentModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = Collection.Count;
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    StateHasChanged();
                    //await popup.CloseAsync();
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        public async Task OpenFilter()
        {
            var parameter = new ModalParameters
            {
                {nameof(PendingPaymentFilter.PageSize),PageSize },
                {nameof(PendingPaymentFilter.PageNumber),PageNumber },
                {nameof(PendingPaymentFilter.Filter),Filter },
                {nameof(PendingPaymentFilter.Count),Count },
                {nameof(PendingPaymentFilter.GetPendingPaymentsAsync),GetPendingPaymentsAsync}
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var model = Modal.Show<PendingPaymentFilter>("Pending Payment Filter", parameter, options);
        }
        protected async Task GetTrueFilter()
        {
            var tenants = Filter.GetType().GetProperties();
            foreach (var tenant in tenants)
            {

                if (tenant.GetValue(Filter) != null && tenant.GetValue(Filter).ToString() == "Select")
                {
                    tenant.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }

        public void Paginate(int page)
        {
            PageNumber = page;
        }
        private async Task IncreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber >= (Math.Ceiling(MaxPageNumber)))
            {
                PageNumber++;
            }
            await GetPendingPaymentsAsync();
        }

        private async Task DecreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetPendingPaymentsAsync();
        }

        public async Task SortColumnAsync()
        {
            PagedResponse<GetPendingPaymentModel, string> response = await PaymentService.GetAllPendingPaymentAsync(Filter);
            if (response != null && response.Items != null)
            {
                Collection.Clear();
                Collection = new ObservableCollection<GetPendingPaymentModel>(response.Items);
                TotalCount = (int)response.TotalCount;
                MaxPageNumber = (double)TotalCount / (double)PageSize;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
        }

        private async Task NavigateToComponent(string tenantId)
        {
            JsRuntime.InvokeVoidAsync("open", $"/tenant/{tenantId}", "_blank");
        }

        private async Task DownloadPendingPaymentAsync()
        {
            try
            {
                List<PendingPaymentViewModelExport> list = await PaymentService.GetExportAllPendingPaymentAsync(Filter);

                foreach (var item in list)
                {
                    if (DateTime.TryParseExact(item.OnBoardDate, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime onBoardDate))
                    {
                        item.OnBoardDate = onBoardDate.ToString("dd/MM/yyyy");
                    }

                    if (DateTime.TryParseExact(item.LicenseValidity, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime licenseValidityDate))
                    {
                        item.LicenseValidity = licenseValidityDate.ToString("dd/MM/yyyy");
                    }
                }


                byte[] data = ExcelHelper.CreateExcelfromList(list);
                string fileName = "PendingPayment_data.xlsx";
                string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task ShowEmailTemplateTenantModal()
        {
            var parameter = new ModalParameters();
            parameter.Add(nameof(EmailTemplate.TenantId), TenantId);
            parameter.Add(nameof(EmailTemplate.Filter), Filter);

            var options = new ModalOptions
            {
                Size = ModalSize.Large
            };
            Modal.Show<EmailTemplate>("Email Template", parameter, options);

        }

        private async Task HandleKeyDown(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                await GetPendingPaymentsAsync();
            }
        }
    }
}













