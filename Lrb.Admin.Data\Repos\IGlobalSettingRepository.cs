﻿using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface IGlobalSettingRepository
    {
        Task<GlobalSettingViewModel> GetSettingByTenant(string? tenantId, string? readConnectionString);
        Task<DuplicateLeadViewModel> GetDuplicateLeadAsync(string? tenantId, string? readConnectionString);
        Task<Response<bool>> UpdateInternationalSupportAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateDailyStatusEnabledAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadStatusPendingToUpdateAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadExportEnabledAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadSourceEditableAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateDuplicateLeadFeatureAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateMicrositeFeatureToggleAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateZoneLocationFeatureToggleAsync(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateShouldEnablePropertyListing(string? tenantId, string? connectionString);
        Task<Response<bool>> UpdateEnableCustomLeadForm(string? tenantId, string? connectionString);
        Task<Response<bool?>> DNSEnableDisable(string? tenantId);
        Task<bool?> GetIsDomainEnabledAsync(string? tenantId);
        Task<bool?> GetShouldHideSubscription(string? tenantId, string? readConnectionString);
        Task<Response<bool>> UpdateShouldHideSubscription(string? tenantId, string? connectionString);
        Task<Response<bool>> ToggleCallRecording(string? tenantId, string? connectionString);
        Task<Response<bool?>> GetIsCallRecordingEnabled(string? tenantId, string? connectionString);
    }
}
