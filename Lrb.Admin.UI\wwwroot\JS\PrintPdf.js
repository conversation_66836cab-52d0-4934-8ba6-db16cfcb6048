﻿
window.printPdf = () => {
    var divContent = document.getElementById("downloadPdf").innerHTML;
    console.log(document);

    var a = window.open('', '', 'height= 500, width=500');

    a.document.write('<html>');
    a.document.write('<head>');
    a.document.write('<style>');
    a.document.write(`
            input {
        background-color: transparent;
        border: 0px solid;
        height: 20px;
        width: 160px;
        color: #CCC;
    }

    .invoice-design {
        display: flex;
        border: 1px solid black;
        margin: auto;
        width: 100%;
    }
    .billing-details {
        display: inline-grid;
       border-right: 1px solid black;
        width:59%;
    }
    .billing-details-right{
        width:39%;
        margin-bottom:10%;
        display:inline-grid;
    }
    .billing-from {
        border-bottom: 1px solid black;
        display: inline-grid;
        padding-left: 1%;
    }
    .billing-to {
        display: inline-grid;
        padding-left: 1%;
    }
    .gst-in {
        display: flex;
    }
    .invoice-logo {
        border-bottom: 0px solid black;
        display: flex;
        justify-content: center;
        background-color: #50bea7;
        padding: 6px;
        width:100%;
    }
    .invoice-no{
        border-bottom: 1px solid black;
        padding: 7px;
        width:100%;
    }
    .invoice-date {
        border-bottom: 1px solid black;
        padding: 7px;
        width:100%;
    }
    .amount-due {
        border-bottom: 1px solid black;
        padding: 7px;
        width:100%;
    }
    .due-date {
        border-bottom: 1px solid black;
        padding: 7px;
        width:100%;
    }
    .license-date {
        padding: 2px;
        border-bottom: 1px solid black;
    }
    .tenant-subs {
        display: flex;
        border-left: 1px solid black;
        border-right: 1px solid black;
        width: 100%;
    }
    .subs-details{
        display:flex;
        width:100%;
        height:140px;
    }
    .description {
        display:inline-grid;
        border-right:1px solid black;
        align-self:center;
        height:100%;
        width:16%;
    }
    .payment-details {
        display: inline-grid;
        border-right: 1px solid black;
        width: 100%;
        align-self: center;
        height: 140px;
    }
    .hsn-no {
        display: inline-grid;
        border-right: 1px solid black;
        width: 15%;
    }
    .license-details {
        display: inline-grid;
        border-right: 1px solid black;
        width: 15%;
        align-self: center;
        height: 140px;
    }
    .license-validity {
        display: inline-grid;
        border-right: 1px solid black;
        width: 13%;
        align-self: center;
        height: 140px;
    }
    .subs-price {
        display: inline-grid;
        width: 14%;
        align-self: center;
        height: 140px;
    }
    .payment-date {
        display: inline-grid;
        border-right: 1px solid black;
        width: 15%;
        align-self: center;
        height: 140px;
    }
    .payment-details {
        display: flex;
        border: 1px solid black;
        margin: auto;
        width: 100%;
        height:144px;
    }
    .banking-details {
        display: inline-grid;
        padding-left: 1%;
        width: 61%;
        height:144px;
    }
    
    .payment-history {
        border-left: 1px solid black;
        width: 39%;
        display: flex;
        margin-left: -5px;
    }
    .payment-type {
        display: inline-grid;
        border-right: 1px solid black;
        width: 66.66%;
    }
    .payment {
        width: 33.33%;
        display: inline-grid;
    }
    .amount-word {
        display: flex;
        border: 1px solid black;
        width: 98.9%;
        padding-left: 8px;
        height:30px;
    }
    .payment-mode {
        display: flex;
        border: 1px solid black;
        width: 98.9%;
        padding-left: 8px;
        height:30px;
    }
    .payment-status {
        display: flex;
        border: 1px solid black;
        width: 98.9%;
        padding-left: 8px;
        height:30px;
    }
    .note {
       display: flex;
        border: 1px solid black;
        width: 98.9%;
        padding-left: 8px;
        height:30px;
    }
    .terms-condotion {
        display: flex;
        border: 1px solid black;
        width: 98.9%;
        padding-left: 8px;
        height:50px;
    }
    .download-pdf{
        display:flex;
        align-items:center;
        justify-content:center;
        padding-top:3%;
    }
    .bank-name{
        display:flex;
    }
    .account-name{
        display:flex;
    }
    .acc-no{
        display:flex;
    }
    .ifcs-code{
        display:flex;
    }
    .acc-type{
        display:flex;
    }
    .image-container {
    background-color: #50BEA7;
    position: absolute;
    top: 100px;
    left: 200px;
    }`);

    a.document.write('</style>');
    a.document.write('</head>');

    a.document.write('<body> <br>');

    // Find the image elements
    var iconImage = document.querySelector('img[src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/icon.svg"][height="50"]');
    var textImage = document.querySelector('img[src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/text.svg"][height="50"]');

    // Modify the attributes of the image elements
    if (iconImage) {
        iconImage.setAttribute('height', '40');
    }
    if (textImage) {
        textImage.setAttribute('height', '40');
        textImage.setAttribute('style', 'margin-left: 1px;');
    }

    //a.document.write('<html>');
    //a.document.write('<head>');
    //a.document.write('<link rel="stylesheet" type="text/css" href="styles.css">');
    //a.document.write('</head>');
    //a.document.write('<body>');
    //a.document.write('<div class="invoice-logo">');
    //a.document.write('<img src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/icon.svg" height="50" />');
    //a.document.write('</div>');
    //a.document.write('<div class="invoice-logo" style="margin-top: 20px;">');
    //a.document.write('<img src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/text.svg" style="margin-left:20px;" height="50" />');
    //a.document.write('</div>');
    //a.document.write('</body>');
    //a.document.write('</html>');

    a.document.write(divContent);

    a.document.write('</body></html>');

    a.document.close();

    a.onload = function () {
        a.print();
    };
}
