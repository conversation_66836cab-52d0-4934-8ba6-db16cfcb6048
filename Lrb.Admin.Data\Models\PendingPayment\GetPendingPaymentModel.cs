﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Dtos;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;

namespace Lrb.Admin.Data.Models.PendingPayment
{
    public class GetPendingPaymentModel
    {

        public Guid Id { get; set; }
        public string? TenantId { get; set; }
        public BillingType BillingType { get; set; }
        public double? GSTAmount { get; set; }
        public string? TenantName { get; set; }
        public DateTime? OnBoardDate { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public string? VendorNames { get; set; }
        public string? VendorContactNumbers { get; set; }
        public string? Source { get; set; }
        public string? LastInvoiceNumber { get; set; }
        public string? GSTNumber { get; set; }
        public double TotalAmount { get; set; }
        public long NetAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public DateTime? NextDueDate { get; set; }
        public decimal? TDS { get; set; }
        public decimal? PaidAmount { get; set; }
        public string? VendorEmail { get; set; }
        public string? TenantInfoId { get; set; } 
        public string? MerchantEmail { get; set; }
        public string? UsersId { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }

        [Required(ErrorMessage = "Sender")]
        public string Sender { get; set; } = default!;
        [Required(ErrorMessage = "Subject")]
        public string Subject { get; set; } = default!;
        [Required(ErrorMessage = "ContentBody")]
        public string ContentBody { get; set; } = default!;
        [Required(ErrorMessage = "AttachedFiles")]
        public List<string>? AttachedFiles { get; set; } = default!; 
        [Required(ErrorMessage = "ToRecipients")]
        public List<string>? ToRecipients { get; set; } = default!;
        [Required(ErrorMessage = "CcRecipients")]
        public List<string>? CcRecipients { get; set; } = default!;
        [Required(ErrorMessage = "BccRecipients")]
        public List<string>? BccRecipients { get; set; } = default!;
        public string? CompanyEmail { get; set; }
        public Guid PaymentId { get; set; }
        public int? InvoiceStatus { get; set; }
        public DateTime? DateofOnboard { get; set; }
        public DateTime? SubscriptionDate { get; set; }
        public DateTime? TransactionDate { get; set; }
        public int? NoOfLicenses { get; set; }
        public string? InvoiceNumber { get; set; }
        public double? TotalPackageAmount { get; set; }
        public double? NetAmountPaid { get; set; }
        public double? TotalAmountPaid { get; set; }
        public double? DueAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public float TDSDeductions { get; set; }
        public string? Address { get; set; }

        public PaymentMode? PaymentMode { get; set; }
        public string? SubscriptionType { get; set; }

        public string Status
        {
            get
            {
                return NextDueDate < DateTime.Now ? "Delayed" : "UpComing";
            }
        }

        public bool IsSelected { get; set; }
    }

    public class SendPendingPaymentModel
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? EmailBody { get; set; }

        [Required(ErrorMessage = "Enter Company Domain Id")]
        public string Id { get; set; } = default!;

        [Required(ErrorMessage = "Enter Company Name")]
        public string Name { get; set; } = default!;
        public string? Sender { get; set; }
        public string? Subject { get; set; }
        public string? ContentBody { get; set; }
        public string? AttachedFiles { get; set; }
        public string? ToRecipients { get; set; }
        public string? CcRecipients { get; set; }
        public string? BccRecipients { get; set; }

    }

    public class PendingPaymentViewModelExport
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public BillingType BillingType { get; set; }
        public string? OnBoardDate { get; set; }
        public string? LicenseValidity { get; set; }
        public string? VendorNames { get; set; }
        public string? LastInvoiceNumber { get; set; }
        public string? GSTNumber { get; set; }
        public long NetAmount { get; set; }
        public double? GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal? PaidAmount { get; set; }
        public DateTime? NextDueDate { get; set; }
        public decimal? TDS { get; set; }
        public string? Source { get; set; }
        public string Status
        {
            get
            {
                return NextDueDate < DateTime.Now ? "Delayed" : "UpComing";
            }
        }
        public string? VendorContactNumbers { get; set; }
        public string? VendorEmail { get; set; }
        public string? MerchantEmail { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }

    }

    public class EmailEventDto
    {
        public Guid? Id { get; set; }
        public int? Event {  get; set; }
        public string? Subject { get; set; }
        public string? Body { get; set; }
        public int? TemplateType {  get; set; }
        public int? BodyType { get; set; }
        public string? Sender { get; set; }
        public List<string>? ToRecipients { get; set; }
        public List<string>? CcRecipients { get; set; }
        public List<string>? BccRecipients { get; set; }
        public bool IsBodyHtml { get; set; }
        public List<string>? AttachedFiles { get; set; }
    }
}
