﻿@page "/register"
@using Lrb.Admin.Data.Models.Identity;

<style>
    body {
        font-family: Arial, Helvetica, sans-serif;
    }

    form {
        border: 3px solid #f1f1f1;
    }

    input[type=text], input[type=password] {
        width: 100%;
        padding: 12px 20px;
        margin: 8px 0;
        display: inline-block;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
</style>

<h3 style="font-weight:bold; color:purple; position:relative;left:5%;">RegisterUser</h3>


<div class="card card-body">
    <EditForm  Model="@UserModel">
        <DataAnnotationsValidator />
        <ValidationSummary />
        <div class="form-group" style="width:80%">
            <label for="First_Name">First Name</label>
            <InputText id="First_Name" @bind-Value="UserModel.FirstName" class="form-control" />
        </div>
        <div class="form-group" style="width:80%">
            <label for="Last_Name">Last Name</label>
            <InputText id="Last_Name" @bind-Value="UserModel.LastName" class="form-control" />
        </div>
        <div class="form-group" style="width:80%">
            <label for="UserName_Name">UserName</label>
            <InputText id="UsertName" @bind-Value="UserModel.UserName" class="form-control" />
        </div>
        @*<div class="form-group" style="width:80%">
             <label for="tenantid">TenantId</label>
             <InputText id="tenantid" @bind-Value="UserModel.TenantId" class="form-control" />
        </div>*@
        <div class="form-group" style="width:80%">
             <label for="role">Role</label>
             <InputSelect id="role" @bind-Value="UserModel.Role">
                 @foreach(var items in Enum.GetValues(typeof(Role)))
                 {
                     <Option value="@items">@items</Option>
                 }
             </InputSelect>
        </div>
        <div class="form-group" style="width:80%">
             <label for="Email">Email</label>
             <InputText id="Email" @bind-Value="UserModel.Email" class="form-control" />
        </div>
        <div class="form-group" style="width:80%">
             <label for="Phone_Number">Phone Number</label>
             <InputText id="Phone_Number" @bind-Value="UserModel.PhoneNumber" class="form-control" />
        </div>
        <div class="form-group" style="width:80%">
             <label for="Password">Password</label>
             <InputText id="Password" @bind-Value="UserModel.Password" class="form-control" />
        </div>

        <div class="register-button">
             <button class="btn btn-primary" @onclick="RegisterUserAsync">Register</button>
        </div>

    </EditForm>
</div>


