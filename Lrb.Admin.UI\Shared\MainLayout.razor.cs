﻿using Blazored.LocalStorage;
using Blazored.Modal.Services;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Utility;
using Lrb.Admin.UI.Modal;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Radzen;

namespace Lrb.Admin.UI.Shared
{
    public partial class MainLayout
    {
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorageService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        private IPaymentService PaymentService { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public NotificationService NotificationService { get; set; }
        public UserModelDto User { get; set; } = new();
        public AdminUserActivity AdminUser { get; set; } = new();
        public string? Token { get; set; }
        public string toggleClass = "sidebar";
        public bool Toggle {  get; set; }
        private readonly string _baseURL = "https://leadrat.com/";
        private readonly string _apiName = "getPaymentDetails.php";
        protected override async Task OnInitializedAsync()
        {
            Token = await LocalStorageService.GetItemAsync<string>("token");
            AdminUser = await ActivityService.GetUserByTokenAsync(Token);
            User = await AuthService.GetUser(AdminUser.AdminUserId);
        }
        public void ToggleFunction(bool Toggle)
        {
            Toggle = !Toggle;
            if(Toggle) 
            {
                toggleClass = "sidebar1";
            }
            else
            {
                toggleClass = "sidebar";
            }
        } 
        public void ShowModel()
        {
            var modal = Modal.Show<Profile>($"User Id : {User.UserName} ");
        }
        private async Task GetAllPaymetDetails()
        {
            var result = await PaymentService.GetPaymenDeatilstAsync(_baseURL, _apiName);
            if (result.Data != null && result.Data.Count() > 0)
            {

                var phonepeDetatils = result.Data.Where(x => x?.PaymentMethod?.ToLowerInvariant()?.Contains("phonepe") ?? false).ToList();
                var cashfreeDetais = result.Data.Where(x => x?.PaymentMethod?.ToLowerInvariant()?.Contains("cashfree") ?? false).ToList();
                Dictionary<string, List<PaymentDetailsDto>> paymentDict = new Dictionary<string, List<PaymentDetailsDto>>()
                {
                    {"Phonepe",phonepeDetatils},
                    {"Cashfree",cashfreeDetais}
                };
                byte[] data = ExcelHelper.CreateExcelFromList<PaymentDetailsDto>(paymentDict);
                string fileName = "payment_details.xlsx";
                string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            else
            {
                NotificationMessage notificationMessage = new NotificationMessage()
                {
                    Summary = "Not Found Payment Details",
                    Severity = NotificationSeverity.Error,
                };

                NotificationService.Notify(notificationMessage);
            }
        }

    } 
}
