﻿using Lrb.Admin.Data.Repos.Factory;
using System.Data;
using Dapper;
namespace Lrb.Admin.Data.Repos
{
    public class DapperRepositoryAsync : IDapperRepositoryAsync
    {
        private readonly IDBConnectionFactory _dbFactory;

        public DapperRepositoryAsync(IDBConnectionFactory dBConnectionFactory)
        {
            _dbFactory = dBConnectionFactory;
        }
        public async Task<IReadOnlyList<T>> QueryAsync<T>(string sql, string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
        {
            var conn = await _dbFactory.CreateReadConnectionAsync(connectionString ?? string.Empty);
            var result =  await conn.QueryAsync<T>(sql, param, transaction);
            return result.ToList();
        }

        public async Task<T?> QueryFirstOrDefaultAsync<T>(string sql, string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
        {
            var conn = await _dbFactory.CreateReadConnectionAsync(connectionString ?? string.Empty);
            var result  = await conn.QueryFirstOrDefaultAsync<T>(sql, param, transaction);
            return result;
        }

        public async Task<T> QuerySingleAsync<T>(string sql, string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
        {
            var conn = await _dbFactory.CreateReadConnectionAsync(connectionString ?? string.Empty);
            var result = await conn.QuerySingleOrDefaultAsync<T>(sql, param, transaction);
            return result;
        }

        public async Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string schemaName, string? connectionString, string spName, object param)
        {
            var conn = await _dbFactory.CreateReadConnectionAsync(connectionString ?? string.Empty);
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure);
            return res;
        }
    }
}
