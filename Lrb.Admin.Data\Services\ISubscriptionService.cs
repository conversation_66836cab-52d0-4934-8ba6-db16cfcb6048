﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface ISubscriptionService
    {
        Task<Response<bool>> CreateNewSubscriptionAsync(LRBSubscription model);
        Task<SubscriptionViewModel> GetSubscriptionAsync(string tenantId);
        Task<Response<bool>> UpdatePartPaymentAsync(string tenantId, LRBPayments dto);
        Task<IEnumerable<LRBPayments>> GetTenantAllPaymentAsync(Guid? subscriptionId);
        Task<UpdateSubscriptionDto> GetSubscriptionToUpdateAsync(string tenantId);
        Task<int> GetNoOfLicenseAsync(string tenantId);
        Task<PagedResponse<LRBSubscription, string>> GetSubscriptionDetailsAsync(string tenantId);
        Task<int> UpdateSubscriptionData(LRBSubscription dto);
        Task<LRBSubscription> GetTenantSubscriptionById(Guid? subscriptionId, bool isAddOn);
        Task<TenantsDto> GetAllLicencesValidityAsync(string TenantId);
        Task<Response<bool>> CreateAddOnSubscription(LRBSubscriptionAddOns model);
        Task<IEnumerable<LRBSubscriptionAddOns>> GetAllAddOnSubscriptionsAsync(Guid? subscriptionId);
        Task<LRBSubscriptionAddOns> GetAddOnSubscriptionById(Guid? subscriptionId);
        Task<Response<bool>> DeleteSubscriptionByIdAsync(Guid? Id, string? entityName);
        Task<int> GetLatestInvoiceNumberAsync();
        Task<Response<bool>> DeleteSubscriptionPayment(LRBPayments payments);
        Task<int> GetSubscriptionCountAsync(string tenantId);
        Task<bool> DeleteSubscriptionAsync(Guid? subscriptionId);
        Task<bool> DeleteAddonSubscriptionAsync(Guid? AddonId);
    }
}
