﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Vendor
{
    public class VendorModel
    {
        public Guid Id { get; set; }
        public string? TenantId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string? PhoneNumber { get; set; }
        public int UserLimit { get; set; }
        public string? ReferralCode { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public bool GharOfficeTenant { get; set; }
        public bool? IsDeleted { get; set; }
    }
}
