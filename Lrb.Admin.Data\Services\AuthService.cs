﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class AuthService : IAuthService
    {
        private readonly IAuthenticationRepository _repository;

        public AuthService(IAuthenticationRepository repository)
        {
            _repository = repository;
        }

        public async Task<UserModelDto> GetUser(Guid id)
        {
            try
            {
                var response = await _repository.GetUserByIdAsync(id);
                return response;
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<string>> LoginUser(UserLoginDto model)
        {
            try
            {
                var response = await _repository.ValidateUser(model);
                return response;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> RegisterUser(UserModelDto model)
        {
            var response = await _repository.RegisterUser(model);
            return new Response<bool>(response);    
        }
    }
}
