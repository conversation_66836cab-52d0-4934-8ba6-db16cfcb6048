#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

ENV ASPNETCORE_ENVIRONMENT="prd"

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["Lrb.Admin.UI/Lrb.Admin.UI.csproj", "Lrb.Admin.UI/"]
COPY ["Lrb.Admin.Data/Lrb.Admin.Data.csproj", "Lrb.Admin.Data/"]
RUN dotnet restore "Lrb.Admin.UI/Lrb.Admin.UI.csproj"
COPY . .
WORKDIR "/src/Lrb.Admin.UI"
RUN dotnet build "Lrb.Admin.UI.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Lrb.Admin.UI.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Lrb.Admin.UI.dll"]