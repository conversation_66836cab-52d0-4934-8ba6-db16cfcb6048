﻿* {
    font-family: 'Lexend Deca', sans-serif;
}

.summary-container {
    padding: 5px;
    border-radius: 12px;
    margin-bottom: 10px;
    background-color: #ffffff;
}

.tenant-profile {
    display: flex;
}

.tenant-info {
    display: flex;
    width: 87%;
}
.Searchbarbar{
    margin-right:10px;
    margin-top:2px;
}

.logo {
    margin: 1px;
    width: 8%;
    border-radius: 50%;
    overflow: hidden;
}

    .logo img {
        width: 100%;
        border-radius: 50%;
        border: 1px solid #BFBFBF;
    }

.info {
    padding: 2px;
    width: 92%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

.info-name {
    font-size: 18px;
    font-weight: 400;
    margin: 1px;
}

.info-Address {
    font-size: 12px;
    font-weight: 300;
    margin: 1px;
    opacity: 50%;
}

.info-CRMlink {
    font-size: 12px;
    font-weight: 400;
    margin: 1px;
    text-decoration: underline;
}
    .info-CRMlink a {
        color: #439B89 !important;
    }
    .info-CRMlink a:visited,
    .info-CRMlink a:active,
    .info-CRMlink a:hover,
    .info-CRMlink a:focus {
        color: #439B89 !important;
    }


.buttons {
    width: 28%;
    cursor:pointer;
    justify-content:end;
    display:flex;
    gap:4px;
}

.purchase-info {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    grid-auto-rows: 1fr; /* Ensures all rows have equal height */
    gap: 5px;
    padding: 0 5px 0 5px;
}

.purchase-details {
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 5px;
    box-sizing: border-box;
    justify-content: space-evenly;
}

.purchase-details-heading {
    font-size: 12px;
    font-weight: 300;
}

.purchase-details-info {
    font-size: 14px;
    font-weight: 500;
}
.edit{
    cursor:pointer;
}


/*lower part*/
.crm-container {
    padding: 5px;
    border-radius: 12px;
    background-color: #ffffff;
}

.options-header {
    padding-left: 15px;
    display: flex;
    margin-bottom: 5px;
    justify-content: space-evenly;
}


.options {
    width: 100%;
    display: flex;
}

.header-buttons {
    width: 30%;
    display: flex;
    justify-content:end;
    gap:4px;

}

.options-rightside {
    cursor: pointer;
}

.option-option {
    padding: 1% 2% 1% 2%;
    font-size: 13px;
    font-weight: 300;
    cursor: pointer;
}
.selected {
    font-weight: bold;
}
.ActiveInactive-button {
    color: black;
    border-block-start-color: black;
    border-block-end-color: black;
    background-color: white;
    border-radius: 20px;
}


.belowBorder {
    border-bottom: 1px solid #DDDDDD;
}


.all-tables {
    padding-left: 15px;
    overflow-y: auto;
    white-space: nowrap;
}

    .all-tables table {
        width: 100%;
        border-collapse: collapse;
    }

    .all-tables th {
        font-weight: 600 !important;
        background-color: #f2f2f2;
        font-size: 12px;
    }

    .all-tables th, .all-tables td {
        padding: 10px 50px 10px 20px;
        font-weight: 400;
        font-size: 12px;
        text-align: left;
    }

    .all-tables td {
        border-bottom: 1px solid #ddd;
    }
.tenantmasterfooter {
    display: flex;
    justify-content: end;
    gap: 3%;
    margin-right: 1%;
    font-weight: 400;
    font-size: 12px;
}

.pagesize {
    display: flex;
    justify-content: flex-end;
    gap: 3px;
    align-items: center;
    width: 100%
}

.showEntriesperPage {
}

.pagesizebox {
}

#pagesizeinput {
    height: 30px;
    font-weight: 400;
    font-size: 12px;
    margin-top: 3%;
}

.pagenumber {
    display: flex;
    justify-content: flex-end;
    gap: 3px;
    align-items: center;
}

#pagenumbercircle {
    border: 1px solid;
    padding: 5% 25%;
    border-radius: 50%;
    margin: 0% 10%;
}

.pagenumberbutton:hover {
    cursor: pointer;
}

@media only screen and (max-width: 700px) {
    .tenant-profile {
        display:block
    }
    .buttons {
        width: 50%;
        cursor: pointer;
        justify-content: end;
        display: flex;
        gap: 4px;
    }
    .purchase-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-auto-rows: 1fr; /* Ensures all rows have equal height */
    }
    .options-header {
        padding-left: 15px;
        display: flex;
        flex-direction:column;
        margin-bottom: 5px;
        justify-content: space-evenly;
    }

    .header-buttons {
        width: 100%;
        display: flex;
    }
    .tenantmasterfooter {
        display: flex;
        gap: 3%;
        margin-right: 1%;
        font-weight: 400;
        font-size: 12px;
    }

    .pagesizebox {
        width: 100%;
    }
}