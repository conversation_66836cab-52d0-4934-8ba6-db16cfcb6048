﻿using Amazon.DynamoDBv2.DocumentModel;
using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Settings;
using SqlKata;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class VendorService : IVendorService
    {
        private readonly IHttpService _httpService;
        private readonly IDapperRepositoryAsync _dapperRepository;
        private readonly IVendorRepository _VendorRepository;

        public VendorService(IHttpService httpService,IDapperRepositoryAsync dapperRepository, IVendorRepository repository)
        {
            _httpService = httpService;
            _dapperRepository = dapperRepository;
            _VendorRepository = repository;
        }

        public Task<Response<bool>> AddVendorAsync(VendorModel model)
        {
            try
            {
                var response = _VendorRepository.AddNewVendor(model);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> DeleteVendorAsync(Guid id)
        {
            try
            {
                var response = await _VendorRepository.DeleteVendor(id);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<VendorModel>> GetAccountManagerAsync(string tenantId)
        {
            try
            {
                var response = await _VendorRepository.GetAccountManager(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetUserLImitAsync(string tenantId)
        {
            try
            {
                var response = await _VendorRepository.GetUserLimit(tenantId);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<VendorModel> GetVendorByTenantIdAsync(string tenantId)
        {
            try 
            {
                var response = await _VendorRepository.GetVendorByTenantId(tenantId);
                return response;
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<VendorModel, string>> GetVendorDetailAsync(GetAllVendorFilter filter)
        {
            try
            {
                var response = await _VendorRepository.GetVendorAsync(filter.PageNumber,filter.PageSize,filter);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<List<string>> GetVendorsAllNames()
        {
            try
            {
                var response =  _VendorRepository.GetAllVendorNames();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateAccountManagerAsync(string tenantId, VendorModel model)
        {
            try
            {
                var response = await _VendorRepository.UpdateAccountManager(tenantId, model);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateUserLimitAsync(string tenantId, VendorModel model)
        {
            try
            {
                var response =  await _VendorRepository.UpdateUserLimit(tenantId, model);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateVendorAsync(Guid id, VendorModel model)
        {
            try
            {
                var response = await _VendorRepository.UpdateVendor(id, model);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> UpdateVendorListAsync(List<string> vendorName, string tenantId)
        {
            try
            {
                var response = await _VendorRepository.UpdateVendorList(vendorName, tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        
        public async Task<int> DeleteTenantIdFromVendorAsync(List<string> VendorName, string TenantId)
        {
            try
            {
                var response = await _VendorRepository.DeleteTenantIdFromVendor(VendorName, TenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
