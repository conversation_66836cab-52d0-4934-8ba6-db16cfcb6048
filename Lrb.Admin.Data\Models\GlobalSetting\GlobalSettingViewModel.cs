﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.GlobalSetting
{
    public class GlobalSettingViewModel
    {
        public bool HasInternationalSupport { get; set; }
        public bool IsLeadStatusToPendingUpdatesEnabled { get; set; }
        public bool HasDailyStatusFeatureEnabled { get; set; }
        public bool IsLeadsExportEnabled { get; set; }
        public bool IsLeadSourceEditable { get; set; }
        public bool IsMicrositeFeatureEnabled { get; set; }
        public bool IsZoneLocationEnabled { get; set; }
        public bool ShouldEnablePropertyListing { get; set; }
        public string? OTPSettings { get; set; }
        public bool? IsCustomLeadFormEnabled { get; set; }
    }
}
