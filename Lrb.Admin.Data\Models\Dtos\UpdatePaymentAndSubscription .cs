﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class UpdatePaymentAndSubscription
    {
        public int SoldLicenses {  get; set; }
        public double NetAmount {  get; set; }
        public double PaidAmount {  get; set; }
        public double GSTAmount {  get; set; }
        public double TotalAmount {  get; set; }
        public DateTime PaymentDate { get; set; }
        public string? Description { get; set; }
        public int DueAmount {  get; set; }
        public PaymentMode Mode { get; set; }
        public PaymentType Type { get; set; }
        public DateTime NextDuedate {  get; set; } = DateTime.Now.AddMonths(1);
    }
}
