﻿using Lrb.Admin.Data.Models.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Repos
{
    public interface IUserRepository
    {
        Task<bool> CheckIfEmailAlreadyExists(string email, string readConnectionString);
        Task<bool> CheckIfUsersNameExists(string userName, string readConnectionString);
        Task<UserModelDto> GetAdminUserById(Guid id);
        Task<string> GetDefaultCountryCode(string tenantId, string readConnectionString);
        Task<UserCountDto> GetUserCountsAsync(string tenantId, string readConnectionString);
    }
}
