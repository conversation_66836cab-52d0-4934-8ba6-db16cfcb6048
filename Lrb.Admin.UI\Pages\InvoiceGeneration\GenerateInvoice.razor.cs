﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Pages.InvoiceGeneration
{
    public partial class GenerateInvoice
    {
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        public GenerateInvoiceDto InvoiceDto { get; set; } = new();
        public List<string> TenantIds { get; set; } = new();

        protected override async Task OnInitializedAsync()
        {
            TenantIds = await TenantService.GetTenantIdsAsync();
        }

        public async Task Generate()
        {
            if (InvoiceDto.TenantId != null)
            {
                await LocalStorage.SetItemAsync(InvoiceDto.TenantId, InvoiceDto);
                await JSRuntime.InvokeVoidAsync("open", $"/tax-invoice/{InvoiceDto.TenantId}", "_blank");
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                InvoiceDto = new();
            }
        }
    }
}
