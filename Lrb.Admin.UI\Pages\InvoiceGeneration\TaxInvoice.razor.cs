﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Modal.Subscription;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Pages.InvoiceGeneration
{
    public partial class TaxInvoice
    {
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Parameter]
        public Guid? SubscriptionId { get; set; }
        [Parameter]
        public Guid? PaymentId { get; set; }
        [Parameter]
        public string? Description {  get; set; }
        [Parameter]
        public bool IsAddOn {  get; set; }
        public string? FromGSTIN { get; set; } = "29AAJCD9183B1ZE";
        public string? CIN_No { get; set; } = "U62099KA2023PTC171870";
        public DateTime InvoiceDate {  get; set; } = DateTime.Now;
        public string? InvoiceNo { get; set; } = "-";
        private string PdfDocument { get; set; }
        public LRBSubscription SubscriptionModel {  get; set; } = new();
        public LRBPayments PaymentModel { get; set; } = new();
        [Parameter]
        public string TenantId { get; set; }
        public Address TenantAddress { get; set; } = new();
        protected async override Task OnInitializedAsync()
        {
            SubscriptionModel = await SubscriptionService.GetTenantSubscriptionById(SubscriptionId,IsAddOn);
            PaymentModel = await PaymentService.GetPaymentDetailsByIdAsync(PaymentId);
            TenantAddress = await TenantService.GetTenantAddressAsync(TenantId);
            var number = PaymentModel.TotalAmount;
            var word = ConvertNumberToWords(number);
            InvoiceNo = PaymentModel.InvoiceNumber;
        }
        public async Task GeneratePdf()
        {
            await JSRuntime.InvokeVoidAsync("printPdf");
        }

        private string ConvertNumberToWords(double number)
        {
            int integerPart = (int)number;
            int decimalPart = (int)((number - integerPart) * 100);

            string integerPartWords = ConvertIntegerToWords(integerPart);
            string decimalPartWords = ConvertIntegerToWords(decimalPart);

            string result = integerPartWords;

            if (decimalPart > 0)
            {
                result += " rupees and " + decimalPartWords + " paise";
            }
            else
            {
                result += " rupees";
            }

            return result;
        }

        private string ConvertIntegerToWords(int number)
        {
            string[] ones = { "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };

            string[] tens = { "", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };

            if (number < 20)
            {
                return ones[number];
            }
            else if (number < 100)
            {
                return tens[number / 10] + ((number % 10 > 0) ? " " + ones[number % 10] : "");
            }
            else if (number < 1000)
            {
                return ones[number / 100] + " Hundred" + ((number % 100 > 0) ? " and " + ConvertIntegerToWords(number % 100) : "");
            }
            else if (number < 100000)
            {
                return ConvertIntegerToWords(number / 1000) + " Thousand" + ((number % 1000 > 0) ? " " + ConvertIntegerToWords(number % 1000) : "");
            }
            else if (number < 10000000)
            {
                return ConvertIntegerToWords(number / 100000) + " Lakh" + ((number % 100000 > 0) ? " " + ConvertIntegerToWords(number % 100000) : "");
            }
            else
            {
                return "Number is too large to be converted.";
            }
        }

    }
}
