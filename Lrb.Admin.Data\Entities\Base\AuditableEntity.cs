﻿namespace Lrb.Admin.Data.Entities.Base
{
    public abstract class UserLevelAuditableEntity : AuditableEntity<Guid>
    {
        public Guid UserId { get; set; }
    }
    public abstract class AuditableEntity : AuditableEntity<Guid>
    {
    }

    public abstract class AuditableEntity<T> : BaseEntity<T>
    {
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public string? TenantId { get; set; }
    }
}
