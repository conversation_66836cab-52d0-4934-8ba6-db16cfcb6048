* {
    font-family: 'Lexend Deca', sans-serif;
}
.sidebar {
    display: flex;
    flex-direction: column;
    justify-content:space-between;
    width: 190px;
    height: 100%;
    background-color: #FFFFFF;
    padding-top:5%;
}
.sidebar-collaped {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 35px;
    height: 100%;
    background-color: #FFFFFF;
    padding-top: 5%;
}
.sidebar-item-selected {
    display: flex;
    align-items: center;
    cursor: pointer;
    background-color: #3A3B3C;
    color: white;
    font-size: 14px;
    gap: 3%;
    padding: 5% 5%;
    height: 23%;
}

.sidebar-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #97959E;
    font-size: 14px;
    gap: 3%;
    padding: 5% 5%;
    height: 23%;
}

    .sidebar-item:hover {
        background-color: #3A3B3C;
        color: white;
    }

.version-arrow-container {
    display: flex;
    justify-content: space-between;
    border-start-end-radius:10px;
    align-items: center; /* Center items vertically */
    background-color: #292A2B;
}

.version {
    font-size: 12px;
    color: #DDDDDD;
    display: flex;
    justify-content: center;
    flex-grow: 1; /* Allows it to take up available space */
}


.arrow-icon {
    cursor: pointer;
    display: flex;
    align-items: center; /* Aligns the icon vertically */
}

@media only screen and (max-width: 700px) {
    .sidebar {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 35px;
        height: 100%;
        background-color: #FFFFFF;
        padding-top: 5%;
    }
    .header-text{
        display:none;
    }
    .version-arrow-container {
        display: none;
    }
}
