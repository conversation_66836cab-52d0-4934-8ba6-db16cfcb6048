﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class TenantsInfo
    {
        public string? Id { get; set; }
        public bool IsTestingAccount { get; set; }
        public bool IsDormented { get; set; }
        public DateTime? CreatedOn{ get; set; }
        public string? RegisteredName {  get; set; }
        public string? RegisteredAddress {  get; set; }
        public int TANNumber { get; set; } = 0;
        public List<LincenseHistory>? LicenseHistory {  get; set; }
        public string? GSTNumber { get; set; }
        public string? MerchantEmail { get; set; }
        public bool? IsDomainEnabled { get;set; }


    }

    public class LincenseHistory
    {
        public string? EditSource {  get; set; }
        public int LincenseData {  get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public string? LastModifiedBy { get; set; }
    }
}
