﻿* {
    font-family: 'Lexend Deca', sans-serif;
}

.Header {
}

.HeaderText {
    font-weight: 500;
    font-size: 25px;
}

.Body {
    margin-top: 1%;
}

.DomainName {
    margin-bottom: 1%;
}

.DomainNameHeadertext {
    text-decoration: underline;
    font-weight: 400;
    font-size: 14px;
}

.DomainName input {
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    height: 30px;
}

.DomainName select {
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
    height: 30px;
    width: 175px;
}
.disabled {
    pointer-events: none; /* Prevents any interaction */
    opacity: 0.5; /* Makes it look disabled */
    filter: grayscale(50%); /* Optional: Slightly grays out the section */
}


.DomainNameHeaderinput {
    font-weight: 400;
    font-size: 14px;
    padding-left: 1%;
}
    .DomainNameHeaderinput select{
        cursor : pointer;
    }

.OrganizationInfoHeaderText {
    text-decoration: underline;
    font-weight: 400;
    font-size: 15px;
}

.form-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    background: #fff;
    padding: 1%;
    border-radius: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

    .form-group label {
        font-size: 14px;
        font-weight: 400;
    }

    .form-group input,
    .form-group select {
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 16px;
        height: 35px;
        width: 200px;
    }

        .form-group input:disabled {
            background-color: #f0f0f0;
        }

        .form-group input::placeholder,
        .form-group select::placeholder {
            color: #aaa;
            font-size: 14px;
        }

.perusermonth {
    display: flex;
}

    .perusermonth input[type="checkbox"] {
        width: 15px; /* Adjust width */
        height: 15px; /* Adjust height */
        transform: scale(0.8); /* Reduce size */
        margin-left: 8px; /* Add spacing between text and checkbox */
        cursor: pointer; /* Makes it clear it's clickable */
    }


.rupeessymbol {
    position: relative;
}

    .rupeessymbol span {
        position: absolute;
        left: 10px;
        top: 27px;
        font-size: 16px;
    }

    .rupeessymbol input {
        padding-left: 20px;
        width: 100%;
    }

.percentagesymbol {
    position: relative;
}

    .percentagesymbol span {
        position: absolute;
        left: 170px;
        top: 28px;
        font-size: 16px;
    }

.OrganizationInfo {
    margin-bottom: 1%;
}

.AdminInfo {
    margin-bottom: 1%;
}

.PlanFinance {
    margin-bottom: 1%;
}

.Miscellaneous {
    margin-bottom: 1%;
}

.footer {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 14px;
    justify-content: flex-end;
}

    .footer span {
        cursor: pointer;
    }

.vendor-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.vendor-button {
    border-radius: 12px;
    border-width: thin;
    font-size: medium;
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
}

    .vendor-button .remove-icon {
        cursor: pointer;
        color: black;
        margin-left: 10px;
    }

@media only screen and (max-width: 700px){
    .form-container {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 10px;
        background: #fff;
        padding: 1%;
        border-radius: 10px;
    }
}