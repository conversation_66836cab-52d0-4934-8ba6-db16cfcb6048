﻿using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Analytics;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Services
{
    public interface ITenantService
    { 
        Task<bool> CreateTenantAsync(CreateTenantModel model);
        Task<LrbTenantInfo?> GetByIdAsync(string tenantId);
        Task<GetTenantModel> GetTenantByIdAsync(string tenantId);
        Task<GetTenantLeadsModel> GetTenantLeadsByIdAsync(string tenantId);
        Task<GetTenantUserModel> GetTenantUserByIdAsync(string tenantId);
        Task<Address> GetTenantAddressAsync(string tenantId);
        Task<PagedResponse<GetTenantLeadsModel, string>> GetTenantLeadsAsync(string tenantId, GetAllTenantParameter filter);
        Task<PagedResponse<GetTenantIntegrationModel, string>> GetTenantIntegrationAsync(string tenantId, GetAllTenantParameter filter, string? readConnectionString);
        Task<PagedResponse<GetTenantSubscriptionModel, string>> GetTenantSubscriptionAsync(string tenantId, GetAllTenantParameter filter);
        Task<Response<bool>> UpdateTenantStatusAsync(string tenantId);
        Task<TenantProfile> GetTenantWebsiteAsync(string tenantId);
        Task<List<string>> GetAnalyticsTenantId();
        Task<int> GetAdminCountAsync(string tenantId, string readConnectionString);
        Task<bool> UpdateTenantValidity(string tenantId, UpdateTenantValidityDto dto);
        Task<List<TenantViewModelExport>> GetExportAllTenantAsync(GetAllTenantParameter filter);
        Task<List<TenantCompleteDetailsDto>> GetCompleteTenantData();
        Task<GetTenantUserModel> GetUserfromIdAsync(string id);
        Task<List<string>> GetTenantIdsAsync();
        Task<bool> ToggleActivivity(string userId, bool value);
        Task UpdateIsDormentedAsync();
        Task<bool> UpdateTestingAccountAsync(string tenantId, bool toggleValue);
        Task<bool> GetAccountStatusAsync(string tenantId);
        Task<TotalSubscriptionDetails> GetTotalSubscriptionDetailsAsync(string tenantId);
        Task<Response<bool>> CreateTenantInfoAsync(TenantsInfo tenantsInfo, bool isTestIngAccount);
        Task<string> UploadTDSCertificate(List<string> base64encodedFiles, string fileName);
        Task<PagedResponse<InvoiceDto, string>> GetInvoiceDetails(InvoiceFilter filter);
        Task<bool> UpdateInvoiceNo(string OldInvoiceNo, string NewInvoiceNo, Guid PaymentId);
        Task<bool> UpdateTransactionDate(InvoiceDto InvoiceUpdate);
        Task<bool> UpdateTDSPercentage(InvoiceDto InvoiceTDS);
        Task<UpdateInvoiceDto> GetInvoiceDto(Guid? paymentId);
        Task<IEnumerable<string>> GetInvoiceNo();
        Task<bool> ToggleSubscriptionIsActiveAsync(Guid SubscriptionId, bool value);
        Task<bool> CompareTenantIdAsync(string TenantId);
        Task<PagedResponse<GetTenantModel, string>> GetAllDataAsync(GetAllTenantParameter filter);
        Task<ProfileDto> GetProfileDetailsAsync(string TenantId,string readConnectionString);
        Task<bool> UpdateTenantProfileAsync(ProfileDto profileData,string connectionString);
        Task<bool> ToggleSubscriptionAddonIsActiveAsync(Guid SubscriptionAddonId, bool value);
        Task<PagedResponse<GetTenantUserModel, string>> GetUserByTenantIdAsync(UserFilter filter, string? readConnectionString);
        Task<bool> PostCustomListingSourcesasync(string tenantId,string connectionString);
        Task<bool> UpdateTenantCache(string tenantId);
        Task<Response<bool>> CreateDifferentDatabseTenantAsync(string tenantId, string connectionString);
    }
}