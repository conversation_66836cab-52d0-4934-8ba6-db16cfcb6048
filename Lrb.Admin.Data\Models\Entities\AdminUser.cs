﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Entities
{
    public class AdminUser
    {
        public Guid Id { get; set; }
        public string UserName { get; set; }
        public string PhoneNumber { get; set; }
        public Role Role { get; set; }
        public byte[] PasswordHash 
        {
            get; set;
        }
        public byte[] PasswordSalt { get; set; }
        public bool IsDeleted { get; set; }
    }
    public enum Role
    {
        Admin,
        SalesExecutive,
        SalesRegionalHead,
        SalesCityHead,
        BDE
    }
}
