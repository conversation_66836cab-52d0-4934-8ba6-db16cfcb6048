﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Wrappers;
using SqlKata;

namespace Lrb.Admin.Data.Services
{
    public interface IEmailService
    {
        Task<List<T>> ExecuteQueryAsync<T>(Query query);
    }
}


