﻿using Microsoft.AspNetCore.Components;
using <PERSON><PERSON><PERSON>;

namespace Lrb.Admin.UI.Shared
{
    public partial class NavMenu
    {
        [Parameter]
        public bool sidebarExpanded { get; set; } = true;
        [Inject]
        protected TooltipService tooltipService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        protected string appVersion { get; set; } = default;

        protected override void OnInitialized()
        {
            var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString();
            appVersion = version ?? "Version not found";
        }

        protected void ToggleSidebar()
        {
            sidebarExpanded = !sidebarExpanded;
        }
    }
}
