﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Invoice;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Utility;
using Lrb.Admin.UI.Pages.InvoiceManagement;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace Lrb.Admin.UI.Modal.Subscription
{
    public partial class AddPayments
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public ITenantService TenantService {get; set;}
        [Parameter]
        public bool? IsAddon { get; set; }
        [Parameter]
        public double MaxPendingAmount {  get; set; }
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public int PartNumber {  get; set; }
        [Parameter]
        public int PaymentCount { get; set; }
        public double netInput { get; set; }
        public double gstInput { get; set; }
        public double paidInput { get=> netInput + gstInput; set=>TenantPaymentDto.TotalAmount = value; }
        public double pendingInput { get => MaxPendingAmount - paidInput; set => TenantPaymentDto.PendingAmount = value; }
        [Parameter]
        public Guid SubscriptionId { get; set; }
        public LRBPayments TenantPaymentDto { get; set; } = new();
        private string IsTDSApplicable { get; set; }
        private float? TDSNumber { get; set; }
        List<string> fileData = new List<string>();
        public string? Token { get; set; }

        protected override void OnInitialized()
        {
            TenantPaymentDto.CreatedOn = DateTime.UtcNow;
            TenantPaymentDto.NextDueDate = DateTime.Now.AddMonths(1);
            TenantPaymentDto.PendingAmount = MaxPendingAmount;

        }
        private async Task AddPartPaymentAsync()
        {
            Token = await LocalStorage.GetItemAsync<string>("token");
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            TenantPaymentDto.Id = Guid.NewGuid();
            TenantPaymentDto.TenantId = TenantId;
            if (IsAddon == true)
            {
                TenantPaymentDto.SubscriptionAddOnId = SubscriptionId;
            }
            else
            {
                TenantPaymentDto.SubscriptionId = SubscriptionId;
            }
            TenantPaymentDto.IsDeleted = false;
            TenantPaymentDto.LastModifiedOn = DateTime.UtcNow;
            TenantPaymentDto.NetAmount = netInput;
            TenantPaymentDto.GSTAmount = gstInput;
            //TenantPaymentDto.PaidAmount = paidinput;
            TenantPaymentDto.PendingAmount = pendingInput;
            TenantPaymentDto.TotalAmount = paidInput;
            TenantPaymentDto.PaymentMode = TenantPaymentDto.Mode.ToString();
            if(pendingInput == 0)
            {
                TenantPaymentDto.NextDueDate = null;
            }
            TenantPaymentDto.InvoiceNumber = await GenerateInvoiceNumber();
            //TenantPaymentDto.PartPaid = PaymentCount + 1;
            var response = SubscriptionService.UpdatePartPaymentAsync(TenantId, TenantPaymentDto);
            if (response.Result.Data)
            {
                string? TDSUrl = null;
                if (IsTDSApplicable == "Applicable" && fileData.Count > 0)
                {
                    string fileName = $"{TenantId}_{TenantPaymentDto.CreatedOn.ToString("ddMMyyHHmm")}";
                    var tdsresponse = await TenantService.UploadTDSCertificate(fileData, fileName);
                    TDSUrl = tdsresponse;
                }
                CreateInvoice createInvoice = new()
                {
                    Id = new Guid(),
                    TenantId = TenantPaymentDto.TenantId,
                    CreatedOn = TenantPaymentDto.CreatedOn,
                    PaymentId = TenantPaymentDto.Id,
                    InvoiceNo = TenantPaymentDto.InvoiceNumber,
                    TDSPercent = TDSNumber ?? null,
                    //SubscriptionType = "Subscription",
                    TDSCertificateURL = TDSUrl
                };
                await PaymentService.CreateInvoiceAsync(createInvoice);
                await JSRuntime.InvokeVoidAsync("alert", "Added Payment Successfully");
                AdminUserActivity userActivity = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.AddedPayment + " " + TenantPaymentDto.TenantId,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(userActivity);
                await JSRuntime.InvokeVoidAsync("location.reload");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Not Added Successfully");
                await JSRuntime.InvokeVoidAsync("location.reload");
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                TenantPaymentDto = new();
            }
        }
        public void UpdatePendingAmount()
        {
            if(MaxPendingAmount > TenantPaymentDto.TotalAmount)
            {
                TenantPaymentDto.PendingAmount = MaxPendingAmount - TenantPaymentDto.TotalAmount;
            }
            else
            {
                TenantPaymentDto.PendingAmount = TenantPaymentDto.TotalAmount - MaxPendingAmount;
            }
        }
        public async Task<string> GenerateInvoiceNumber()
        {
            if (DateTime.UtcNow.Month >= 4)
            {
                return "DSL/" + DateTime.UtcNow.Year % 100 + "-" + ((DateTime.UtcNow.Year % 100) + 1) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
            else
            {
                return "DSL/" + ((DateTime.UtcNow.Year % 100) - 1) + "-" + (DateTime.UtcNow.Year % 100) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
        }
        public void CalculateTotal(ChangeEventArgs e)
        {
            Console.WriteLine(TenantPaymentDto);
            TenantPaymentDto.TotalAmount = TenantPaymentDto.NetAmount + TenantPaymentDto.GSTAmount;
            Console.WriteLine(TenantPaymentDto);
        }

        private async Task TDSFileUpload(InputFileChangeEventArgs files)
        {
            foreach (var file in files.GetMultipleFiles())
            {
                var data = await PdfUploadHelper.PdfToByteArray(file);
                if (data != null)
                {
                    var base64Data = Convert.ToBase64String(data);
                    fileData.Add(base64Data);
                }
            }
        }
    }
}
