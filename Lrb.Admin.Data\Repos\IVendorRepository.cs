﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface IVendorRepository
    {
        Task<PagedResponse<VendorModel, string>> GetVendorAsync(int pageNumber, int pageSize, GetAllVendorFilter filter);
        Task<int> GetTotalCount();
        Task<Response<bool>> AddNewVendor(VendorModel model);
        Task<VendorModel> GetVendorById(Guid id);
        Task<VendorModel> GetVendorByTenantId(string tenantId);
        Task<Response<bool>> UpdateVendor(Guid id, VendorModel model);
        Task<Response<bool>> DeleteVendor(Guid id);
        Task<Response<bool>> UpdateUserLimit(string tenantId, VendorModel model);
        Task<int> GetUserLimit(string tenantId);
        Task<List<VendorModel>> GetAccountManager(string tenantId);
        Task<Response<bool>> UpdateAccountManager(string tenantId, VendorModel model);
        public Task<List<string>> GetAllVendorNames();
        public Task<int> UpdateVendorList(List<string> vendorName, string tenantId);
        Task<int> DeleteTenantIdFromVendor(List<string> VendorName, string TenantId);
    }
}
