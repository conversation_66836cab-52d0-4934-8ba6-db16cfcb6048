﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Filters
{
    public class InvoiceFilter : RequestParameter
    {
        public string? search { get; set; } //tenantid tenantname invoicenumber
        public DateTime? OnboardingFromDate { get; set; }
        public DateTime? OnboardingToDate { get; set; }
        public DateTime? SubscriptionFromDate { get; set; }
        public DateTime? SubscriptionToDate { get; set; }
        public DateTime? PaymentFromDate { get; set; }
        public DateTime? PaymentToDate { get; set; }
        public DateTime? LicenseValidityFromDate { get; set; }
        public DateTime? LicenseValidityToDate { get; set; }
        //public int? InvoiceStatus { get; set; }
        //public string? GSTNumber { get; set; }
        public double? TotalPackageAmountfrom { get; set; }
        public double? TotalPackageAmountto { get; set; }
        public double? TotalAmountPaidfrom { get; set; }
        public double? TotalAmountPaidto { get; set; }
        public double? NetDueAmountfrom { get; set; }
        public double? NetDueAmountto { get; set; }
        public PaymentMode? PaymentMode { get; set; }
        public BillingType? BillingType { get; set; }
        public string? Sort { get; set; }
        public bool IsDecending { get; set; }
    }
}
