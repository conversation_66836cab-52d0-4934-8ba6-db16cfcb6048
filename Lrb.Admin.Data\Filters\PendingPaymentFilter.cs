﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Admin.Data.Enums;

namespace Lrb.Admin.Data.Filters
{ 
    public class PendingPaymentFilterParameter : RequestParameter 
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? SearchTerm { get; set; }
        public PendingPaymentDateType DateType { get; set; }
        public BillingType BillingType { get; set; }
        public DateTime OnBoardDate { get; set; }
        public DateTime LicenseValidity { get; set; }
        public int NoOfUsers { get; set; }
        public string? VendorNames { get; set; }
        public string? LastInvoiceNumber { get; set; } 
        public string? GSTNumber { get; set; }
        public decimal TotalAmount { get; set; }
        public string? PendingAmount { get; set; }
        public double? PendingAmountFrom { get; set; }
        public double? PendingAmountTo { get; set; }
        public DateTime NextDueDate { get; set; }
        public decimal? TDS { get; set; }
        public decimal? PaidAmount { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? PaymentFromDate { get; set; }
        public DateTime? PaymentToDate { get; set; }
        public DateTime? OnBoardFromDate { get; set; }
        public DateTime? OnBoardToDate { get; set; }
        public DateTime? LicenseValidityFromDate { get; set; } 
        public DateTime? LicenseValidityToDate { get; set; }  
        public DateTime? NextDueFromDate { get; set; }   
        public DateTime? NextDueToDate { get; set; }
        public decimal NetAmount { get; set; }
        public string? VendorContactNumbers { get; set; }
        public string? VendorEmail { get; set; } 
        public string? Source { get; set; }
        public Guid PaymentId { get; set; }
        public DateTime? DateofOnboard { get; set; }
        public DateTime? SubscriptionDate { get; set; }
        public DateTime? TransactionDate { get; set; }
        public long? NoOfLicenses { get; set; }
        public string? InvoiceNumber { get; set; }
        public double? TotalPackageAmount { get; set; }
        public double? NetAmountPaid { get; set; }
        public double? TotalAmountPaid { get; set; }
        public double? DueAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Address { get; set; }
        public PaymentMode? PaymentMode { get; set; }
        public string? SubscriptionType { get; set; }
        public string? AdminEmail { get; set;}

        [Required(ErrorMessage = "Sender")]
        public string Sender { get; set; } = default!;
        [Required(ErrorMessage = "Subject")]
        public string Subject { get; set; } = default!;
        [Required(ErrorMessage = "ContentBody")]
        public string ContentBody { get; set; } = default!;
        [Required(ErrorMessage = "AttachedFiles")]
        public List<string>? AttachedFiles { get; set; } = default!;
        [Required(ErrorMessage = "ToRecipients")]
        public List<string>? ToRecipients { get; set; } = default!;
        [Required(ErrorMessage = "CcRecipients")]
        public List<string>? CcRecipients { get; set; } = default!;
        [Required(ErrorMessage = "BccRecipients")]
        public List<string>? BccRecipients { get; set; } = default!;
        public string? CompanyEmail { get; set; }
        public bool? IsTestingAccount { get; set; }
    }
    public enum PendingPaymentDateType
    {
        None = 0,
        OnBoardDate,
        LicenseValidityDate,
        NextDueDate
    }
}
