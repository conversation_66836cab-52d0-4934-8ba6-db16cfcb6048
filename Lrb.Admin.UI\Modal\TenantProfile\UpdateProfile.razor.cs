﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.TenantProfile
{
    public partial class UpdateProfile
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public ProfileDto ProfileData { get; set; }
        [Parameter]
        public bool IsTestingAccount { get; set; }
        [Parameter]
        public List<string>? Vendors { get; set; }
        public string? Token { get; set; }
        [Parameter]
        public List<VendorModel> VendorModel { get; set; }
        [Parameter]
        public string? ReadConnectionString { get; set; }
        [Parameter]
        public string? ConnectionString { get; set; }
        public List<string> VendorNames { get; set; } = new();
        public List<string> VendorDeleteNames { get; set; } = new();
        public string? testingAcc { get; set; }
        protected override async Task OnInitializedAsync()
        {
            testingAcc = IsTestingAccount.ToString();
            foreach (var v in VendorModel)
            {
                VendorNames.Add(v.FirstName + ' ' + v.LastName);
            }
        }
        public async Task UpdateProfileData()
        {
            if (testingAcc == "True")
            {
                IsTestingAccount = true;
            }
            else if (testingAcc == "False")
            {
                IsTestingAccount = false;
            }
            var response = await TenantService.UpdateTenantProfileAsync(ProfileData,ConnectionString ?? string.Empty);
            response = await TenantService.UpdateTestingAccountAsync(TenantId, IsTestingAccount);
            var VenderDeleteresponce = await VendorService.DeleteTenantIdFromVendorAsync(VendorDeleteNames, TenantId);
            var VenderUpdateresponce = await VendorService.UpdateVendorListAsync(VendorNames, TenantId);


            if (response)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{TenantId} Profile Updated Successfully");
                Token = await LocalStorage.GetItemAsync<string>("token");
            }
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            AdminUserActivity userActivity = new()
            {
                AdminUserId = userActivityHistory.AdminUserId,
                TenantId = TenantId,
                ActivityTime = DateTime.UtcNow,
                Activity = ActivityConst.UpdateTenantProfile + " " + ProfileData.TenantId,
                Token = Token
            };
            await ActivityService.UserActivityAsync(userActivity);
            await jSRuntime.InvokeVoidAsync("location.reload");
        }
        public void AddMultipleVendors(ChangeEventArgs evnt)
        {
            if (VendorNames.Contains("Select"))
            {
                VendorNames.Remove("Select");
            }
            if (evnt.Value.ToString() != "Select")
            {
                if (!VendorNames.Contains(evnt.Value))
                {
                    var data = evnt.Value.ToString();
                    VendorNames.Add(data);
                }
            }
        }
        public async void RemoveVendor(string VendorName)
        {
            VendorNames.Remove(VendorName);
            VendorDeleteNames.Add(VendorName);
        }
    }
}
