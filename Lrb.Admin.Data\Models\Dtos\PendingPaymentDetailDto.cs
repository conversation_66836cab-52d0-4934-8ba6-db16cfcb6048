﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Enums;
using Microsoft.AspNetCore.Http;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class PendingPaymentDetailDto 
    { 
        public Guid Id { get; set; }
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public DateTime OnBoardDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal NetAmount { get; set; }
        public decimal? PaidAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public string? GSTNumber { get; set; }
        public BillingType BillingType { get; set; } 
        public decimal? TDS { get; set; }
        public string? LastInvoiceNumber { get; set; }
        public string? VendorNames { get; set; }
        public string? VendorContactNumbers { get; set; }
        public string? Source { get; set; }
        public DateTime LicenseValidity { get; set; }
        public string? VendorEmail { get; set; }
        public string? CompanyEmail { get; set; }
        public string? RelationshipManagerEmailId { get; set; }
        public string? TenantInfoId { get; set; }
        public string? MerchantEmail { get; set; }
        public string? UsersId { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }  

    }
}
