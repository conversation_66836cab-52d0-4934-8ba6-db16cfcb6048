﻿using Microsoft.Extensions.Configuration;
using Npgsql;
using System.Data;

namespace Lrb.Admin.Data.Repos.Factory
{
    public class NpgSqlConnectionFactory : IDBConnectionFactory
    {
        private readonly string? _connectionString;
        private readonly string? _readReplicaConnectionString;

        public NpgSqlConnectionFactory(IConfiguration config)
        {
            _connectionString = config.GetConnectionString("DefaultConnection");
            _readReplicaConnectionString = config.GetConnectionString("ReadReplicaConnectionString");
        }
        public async Task CloseConnectionAsync(NpgsqlConnection dbConnection)
        {
            await dbConnection.CloseAsync();
        }

        public async Task<IDbConnection> CreateConnectionAsync(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                connectionString = _connectionString;
            }
            NpgsqlConnection dbConnection = new NpgsqlConnection(connectionString);
            //await dbConnection.OpenAsync();
            return dbConnection;
        }

        public async Task OpenConnectionAsync(NpgsqlConnection dbConnection)
        {
            await dbConnection.OpenAsync();
        }

        public async Task<IDbConnection> CreateReadConnectionAsync(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                connectionString = _readReplicaConnectionString ?? _connectionString;
            }
            NpgsqlConnection dbConnection = new NpgsqlConnection(connectionString);
            //await dbConnection.OpenAsync();
            return dbConnection;
        }
    }
}
