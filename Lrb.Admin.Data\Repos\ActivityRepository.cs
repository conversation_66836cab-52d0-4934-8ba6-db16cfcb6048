﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Lrb.Admin.Data.Repos
{
    public class ActivityRepository : IActivityRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string _schema = "tenantmaster.";
        private Query Query;

        public ActivityRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        public async Task<Response<bool>> AddAdminUserActity(AdminUserActivity activity)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                activity.Id = Guid.NewGuid();
                var affectedRow = await db.Query(_schema + typeof(AdminUserActivity).Name).InsertAsync(activity);
                connection.Close();
                return new Response<bool>(affectedRow > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateAdminActivity(AdminUserActivity activity)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var result = await db.Query(_schema + typeof(AdminUserActivity).Name).Where("Token", "=", activity.Token)
                    .UpdateAsync(activity);
                connection.Close();
                return new Response<bool>(result > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<AdminUserActivity> GetByIdTokenAsync(string idToken)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var token = db.Query(_schema + typeof(AdminUserActivity).Name).WhereLike("Token", $"%{idToken}%", true);
                var activity = await token.FirstOrDefaultAsync<AdminUserActivity>();
                connection.Close();
                return activity;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetUserActivity(int pageNumber, int pageSize, ActivityFilter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await GetActivityCount();

                Query = db.Query(_schema + typeof(AdminUserActivity).Name).LeftJoin($"{_schema}Users", i => i.On("AdminUserId", "Users.Id"))
                    .Select("FirstName", "LastName", "Role", "AdminUserActivity.TenantId", $"ActivityTime", "Activity").OrderByDesc("ActivityTime");

                if ((filter.FromDate != null && filter.FromDate != default) && (filter.ToDate != null && filter.ToDate != default))
                {
                    Query = db.Query(_schema + typeof(AdminUserActivity).Name).LeftJoin($"{_schema}Users", i => i.On("AdminUserId", "Users.Id")).
                        WhereBetween("ActivityTime", filter.FromDate, filter.ToDate)
                        .Select("FirstName", "LastName", "Role", "AdminUserActivity.TenantId", "ActivityTime", "Activity").OrderByDesc("ActivityTime");

                }
                Query = Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                IEnumerable<GetActivityDto> activities = await Query.GetAsync<GetActivityDto>();
                var interval = new TimeSpan(5, 30, 0);
                foreach (var activityDto in activities)
                {
                    activityDto.ActivityTime = activityDto?.ActivityTime.Value.Add(interval);
                }
                connection.Close();
                return new PagedResponse<GetActivityDto, string>(activities, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetActivityCount()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query(_schema + typeof(AdminUserActivity).Name).Select("count(\"Id\")");
                connection.Close();
                return await query.CountAsync<int>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
