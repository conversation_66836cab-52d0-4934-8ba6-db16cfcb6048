﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class CustomSubStatusService : ICustomSubStatusService
    {
        private readonly ICustomMasterLeadRepository _repository;
        public CustomSubStatusService(ICustomMasterLeadRepository repository)
        {
            _repository = repository;
        }
        public Task<string?> CreateMasterLeadSubStatus(string tenantId, CustomSubStatusDto custom)
        {
            return _repository.CreateLeadSubStatus(tenantId, custom);
        }

        public Task<bool> DeleteMasterLeadSubStatusesById(Guid? id, Guid? transferingId)
        {
            return _repository.DeleteLeadSubStatusesById(id,transferingId);
        }

        public Task<IEnumerable<CustomMainStatusesDTO>> GetAllMainStatuses(string? tenantId)
        {
            return _repository.GetAllMainStatuses(tenantId);
        }
        
        public Task<CustomSubStatusDto> GetCustomMasterLeadStatusById(Guid id)
        {
            return _repository.GetCustomMasterLeadStatusById(id);
        }

        public Task<IEnumerable<CustomSubStatusDto>> GetCustomMasterLeadSubStatuses(string? tenantId)
        {
            return _repository.GetMasterLeadSubStatuses(tenantId);
        }

        public Task<long> GetLeadCountRequest(Guid? id,string? tenantId)
        {
            return _repository.GetLeadCountRequest(id, tenantId);
        }

        public Task<Guid> GetMasterStatusId(Guid? baseId)
        {
           return _repository.GetMasterLeadStatusBaseId(baseId);
        }

        public Task<Guid> GetStatusId(string? tenantId, string? statusDisplayName)
        {
            return _repository.GetMainStatusId(tenantId, statusDisplayName);
        }

        public Task<bool> GetUserAvailablity(string? tenantId, string? name)
        {
            return _repository.GetUserActivity(tenantId,name);
        }

        public Task<CustomSubStatusDto> UpdateMasterLeadSubStatus(Guid id, Guid baseId, string status, string displayName)
        {
            return _repository.UpdateSubStatusAsync(id, baseId, status, displayName);
        }
    }
}
