﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class TaxInvoiceDto
    {
        public string? FromGSTIN { get; set; }
        public string? ToGSTIN { get; set; }
        public string? InvoiceNo { get; set; }
        public DateTime? InvoiceDate { get; set; } = DateTime.Now;
        public double DueAmount { get; set; }
        public DateTime? DueDate { get; set; }  
        public DateTime? LicenseValidity { get; set; }
    }
}
