﻿@page "/profile"
@using Lrb.Admin.Data.Models.Entities;

<style>
    .blazored-modal[b-mwsrhhqq2g] {
        display: flex;
        justify-content: flex-end; /* Aligns to the right */
        align-items: flex-start; /* Aligns to the top */
        position: fixed;
        top: 20px; /* Adjust as needed */
        right: 20px; /* Adjust as needed */
        width: auto; /* Auto width */
        height: auto;
        z-index: 1050; /* Ensures it's above other elements */
    }

    .blazored-modal {
        background-color: #FFFFFF;
        border-radius: 12px;
        border: 1px solid #ddd;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        max-width: 300px; /* Adjust modal width */
        position: relative;
    }

    .bm-close[b-mwsrhhqq2g] {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: transparent;
        border: none;
        font-size: 18px;
        cursor: pointer;
        font-weight: bold;
    }

    .profile-card {
        background-color: #FFFFFF;
        border-radius: 12px;
        border: 1px solid #ddd;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 300px;
        text-align: center;
    }

    .profile-header {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        padding-bottom: 10px;
    }

    .profile-image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 1px solid #ddd;
    }

    .user-name {
        font-weight: bold;
        font-size: 18px;
        color: black;
    }

    .user-role {
        color: gray;
        font-size: 14px;
    }

    .profile-divider {
        width: 100%;
        border-top: 1px solid #ddd;
        margin: 10px 0;
    }

    .profile-info {
        display: flex;
        flex-direction: column;
        gap: 15px;
        text-align: left;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: bold;
        color: black;
        cursor: pointer;
    }

    .icon-placeholder {
    }
</style>

<div class="profile-card">
    <!-- Profile Header -->
    <div class="profile-header">
        <img src="https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/face_human_blank_user_avatar_mannequin_dummy-512.webp"
             class="profile-image" />

        <div class="user-name">@User?.FirstName @User?.LastName</div>
        <div class="user-role">@User?.Role</div>
    </div>

    <div class="profile-divider"></div>

    <!-- Profile Details -->
    <div class="profile-info">
        <div class="info-item">
            <span class="icon-placeholder">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.86406 5.60235L7.27943 6.34662C7.65427 7.01829 7.50379 7.89941 6.91341 8.48979C6.91341 8.4898 6.91342 8.48979 6.91341 8.48979C6.91334 8.48987 6.19736 9.20598 7.49569 10.5043C8.79359 11.8022 9.50969 11.0871 9.51021 11.0866C9.51019 11.0866 9.51022 11.0866 9.51021 11.0866C10.1006 10.4962 10.9817 10.3457 11.6534 10.7206L12.3977 11.1359C13.4119 11.702 13.5317 13.1243 12.6402 14.0158C12.1045 14.5515 11.4483 14.9683 10.7228 14.9958C9.50162 15.0421 7.4277 14.733 5.34733 12.6527C3.26696 10.5723 2.9579 8.49838 3.00419 7.27718C3.03169 6.55174 3.44851 5.89551 3.9842 5.35983C4.87568 4.46834 6.29803 4.58812 6.86406 5.60235Z" fill="#343739" />
                    <path d="M8.92607 3.40309C8.96844 3.1414 9.21583 2.96388 9.47751 3.00625C9.49371 3.00935 9.54583 3.01909 9.57314 3.02517C9.62775 3.03733 9.70393 3.05606 9.79891 3.08372C9.98884 3.13905 10.2542 3.23019 10.5727 3.37619C11.2103 3.66852 12.0588 4.17988 12.9393 5.06039C13.8198 5.9409 14.3312 6.78942 14.6235 7.42704C14.7695 7.74552 14.8607 8.01086 14.916 8.2008C14.9436 8.29578 14.9624 8.37196 14.9745 8.42657C14.9806 8.45388 14.9851 8.4758 14.9882 8.492L14.9918 8.51197C15.0342 8.77366 14.8583 9.03127 14.5966 9.07363C14.3357 9.11588 14.0899 8.93922 14.0464 8.67876C14.0451 8.67177 14.0414 8.65297 14.0375 8.63525C14.0296 8.59981 14.0159 8.5436 13.9943 8.46926C13.951 8.32057 13.8758 8.09956 13.7509 7.82712C13.5014 7.28293 13.0527 6.53145 12.2605 5.73921C11.4683 4.94698 10.7168 4.49835 10.1726 4.24885C9.90015 4.12395 9.67914 4.04873 9.53044 4.00542C9.45611 3.98377 9.36267 3.96232 9.32722 3.95443C9.06676 3.91102 8.88383 3.66403 8.92607 3.40309Z" fill="#343739" />
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.07087 5.61075C9.1437 5.35585 9.40937 5.20826 9.66427 5.28108L9.5324 5.74262C9.66427 5.28108 9.66427 5.28108 9.66427 5.28108L9.66519 5.28135L9.66617 5.28163L9.66831 5.28226L9.67327 5.28374L9.68593 5.28768C9.69558 5.29077 9.70763 5.29479 9.722 5.29989C9.75074 5.31008 9.78873 5.32459 9.83533 5.34456C9.92857 5.38452 10.056 5.44623 10.2125 5.53884C10.5257 5.72424 10.9534 6.03219 11.4557 6.53458C11.9581 7.03696 12.2661 7.4646 12.4515 7.77785C12.5441 7.93434 12.6058 8.06174 12.6458 8.15498C12.6657 8.20158 12.6802 8.23957 12.6904 8.26831C12.6955 8.28268 12.6995 8.29473 12.7026 8.30438L12.7066 8.31704L12.7081 8.322L12.7087 8.32414L12.709 8.32512C12.709 8.32512 12.7092 8.32604 12.2477 8.45791L12.7092 8.32604C12.7821 8.58094 12.6345 8.84661 12.3796 8.91944C12.1268 8.99165 11.8635 8.84716 11.7881 8.59624L11.7857 8.58934C11.7823 8.57973 11.7752 8.56079 11.7634 8.53314C11.7397 8.47789 11.6967 8.3875 11.6253 8.2668C11.4826 8.02569 11.2249 7.66137 10.7769 7.2134C10.3289 6.76543 9.96462 6.5077 9.72351 6.365C9.60281 6.29356 9.51242 6.25062 9.45717 6.22694C9.42952 6.21509 9.41058 6.20803 9.40097 6.20462L9.39407 6.20225C9.14315 6.12681 8.99866 5.86348 9.07087 5.61075Z" fill="#343739" />
                </svg>
            </span>
            <span>@User?.PhoneNumber</span>
        </div>

        <div class="info-item" @onclick="NavigateToRegisterPage">
            <span class="icon-placeholder">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16 9C16 12.866 12.866 16 9 16C5.13401 16 2 12.866 2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9ZM9 5.675C9.28995 5.675 9.525 5.91005 9.525 6.2V8.475H11.8C12.0899 8.475 12.325 8.71005 12.325 9C12.325 9.28995 12.0899 9.525 11.8 9.525H9.525V11.8C9.525 12.0899 9.28995 12.325 9 12.325C8.71005 12.325 8.475 12.0899 8.475 11.8V9.525H6.2C5.91005 9.525 5.675 9.28995 5.675 9C5.675 8.71005 5.91005 8.475 6.2 8.475H8.475V6.2C8.475 5.91005 8.71005 5.675 9 5.675Z" fill="#343739" />
                </svg>

            </span>
            <span style="color: blue;">Add New User</span>
        </div>

        <div class="info-item" @onclick="LogoutTenant">
            <span class="icon-placeholder">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.26474 2.32597C6.45025 1.53964 8.00745 2.26053 8.23841 3.58453H10.5501C11.6168 3.58453 12.4816 4.44486 12.4816 5.50615C12.4816 5.79559 12.2457 6.03022 11.9548 6.03022C11.6639 6.03022 11.4281 5.79559 11.4281 5.50615C11.4281 5.02375 11.035 4.63268 10.5501 4.63268H8.26754V13.3673H10.5501C11.035 13.3673 11.4281 12.9763 11.4281 12.4939C11.4281 12.2044 11.6639 11.9698 11.9548 11.9698C12.2457 11.9698 12.4816 12.2044 12.4816 12.4939C12.4816 13.5551 11.6168 14.4155 10.5501 14.4155H8.23841C8.00745 15.7395 6.45025 16.4604 5.26474 15.674L3.86007 14.7423C3.32274 14.3859 3 13.786 3 13.1435V4.85655C3 4.21405 3.32274 3.61406 3.86007 3.25766L5.26474 2.32597Z" fill="#343739" />
                    <path d="M12.987 10.582C12.7813 10.3773 12.7813 10.0455 12.987 9.84083L13.4925 9.33795L9.8478 9.33795C9.55688 9.33795 9.32104 9.10331 9.32104 8.81387C9.32104 8.52443 9.55688 8.28979 9.8478 8.28979L13.4925 8.28979L12.987 7.78691C12.7813 7.58224 12.7813 7.25041 12.987 7.04575C13.1927 6.84108 13.5262 6.84108 13.732 7.04575L14.64 7.94918C15.12 8.42674 15.12 9.201 14.64 9.67855L13.732 10.582C13.5262 10.7867 13.1927 10.7867 12.987 10.582Z" fill="#343739" />
                </svg>

            </span>
            <span style="color: blue;">Log Out</span>
        </div>
    </div>
</div>
