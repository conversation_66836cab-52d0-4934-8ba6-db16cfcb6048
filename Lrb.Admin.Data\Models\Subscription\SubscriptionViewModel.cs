﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Subscription
{
    public class SubscriptionViewModel
    {
        public Guid Id { get; set; }
        public string? TenantId { get; set; }
        public string? Name { get; set; }
        public DateTime LicenseCreated { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public double NetAmount { get; set; }
        public int SoldLicenses { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public int PartNumber { get; set; }
        public double Amount { get; set; }
        public DateTime PartPaymentDate { get; set; }
        public int PartPaid { get; set; }
        public PaymentType Type { get; set; }
        public PaymentMode Mode { get; set; }
        public DateTime NextDueDate { get; set; }
        public DateTime PaymentDate { get; set; }
        public double TotalAmount { get; set; }
        public double GSTAmount { get; set; }
        public string? GSTNumber {  get; set; }
        public BillingType BillingType { get; set; }
        public string? Description {  get; set; }
    }
}
