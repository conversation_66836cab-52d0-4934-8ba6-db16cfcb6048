﻿
@inject TooltipService tooltipService
<AuthorizeView>
    <Authorized>
        <button @onclick="LogoutTenant" class="btn btn-danger">Logout</button>
        @* <RadzenButton Click="LogoutTenant" Text="Logout" Icon="logout" Size="ButtonSize.Small" ButtonStyle="ButtonStyle.Danger" MouseEnter="@(args => ShowTooltip(args, new TooltipOptions(){ Position = TooltipPosition.Bottom,Text="Logout" }))" /> *@
    </Authorized>
    <NotAuthorized>

    </NotAuthorized>
</AuthorizeView>

@code {
 
    void ShowTooltip(ElementReference elementReference, TooltipOptions options = null) => tooltipService.Open(elementReference, options.Text,options);
}


