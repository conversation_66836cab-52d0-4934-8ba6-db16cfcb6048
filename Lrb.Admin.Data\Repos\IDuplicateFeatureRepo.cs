﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Lead;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface IDuplicateFeatureRepo
    {
        Task<PagedResponse<DuplicateFeatureViewModel, string>> GetAllDuplicateFeatureAsync(int pageNumber, int pageSize, DuplicateFeatureFilter filter);
        Task<DuplicateFeatureViewModel> GetDuplicateFeatureById(string tenantId);
        Task<int> TotalDuplicateFeatureCount();

    }
}
