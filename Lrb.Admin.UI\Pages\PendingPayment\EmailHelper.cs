﻿using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Services;
using System.Net.Mail;
using System.Net.Mime;
using System.Net;
using System.Runtime.InteropServices;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Components;

namespace Lrb.Admin.UI.Pages.PendingPayment
{
    public static class EmailHelper
    {
        
        #region Email Body 
        public static async Task<EmailEventDto> SendPendingPaymentDueMail(GetPendingPaymentModel pendingemail, EmailEventDto emailEventDto)
         
        {

            var file = DueEmailHelper.ReplaceVariables(emailEventDto.Body ?? string.Empty, new Dictionary<string, string>()
                    {
                        { "#tenantid#", pendingemail.TenantId ?? string.Empty },
                        { "#TenantName#", pendingemail.TenantName ?? string.Empty },
                        { "#PendingAmount#", pendingemail.PendingAmount.ToString() ?? string.Empty },
                        { "#NextDueDate#", pendingemail.NextDueDate?.ToString("dd/MM/yyyy") ?? string.Empty },
                        { "#LastInvoiceNumber#", pendingemail.LastInvoiceNumber?.ToString() ?? string.Empty },
                        { "#GSTNumber#", pendingemail?.GSTNumber?.ToString() ?? string.Empty },
                        { "#TotalAmount#", pendingemail?.TotalAmount.ToString() ?? string.Empty }
                    });

            emailEventDto.Sender = LeadratEmails.NoReplyEmail;
            emailEventDto.Subject = $"{pendingemail.TenantName} Payment Due Reminder";
            emailEventDto.Body = file;
            emailEventDto.ToRecipients = new List<string>();
            if (pendingemail.ToRecipients.Contains("Merchant Email") && pendingemail.MerchantEmail != null)
            {
                pendingemail.ToRecipients.Remove("Merchant Email");
                pendingemail.ToRecipients.Add(pendingemail.MerchantEmail);
                emailEventDto.ToRecipients.AddRange(pendingemail.ToRecipients);
            }
            else
            {
                emailEventDto.ToRecipients.AddRange(pendingemail.ToRecipients);
            }
            if(pendingemail.CcRecipients.Contains("Vendor Email") && pendingemail.VendorEmail != null)
            {
                pendingemail.CcRecipients.Remove("Vendor Email");
                pendingemail.CcRecipients.Add(pendingemail.VendorEmail);
                emailEventDto.CcRecipients.AddRange(pendingemail.CcRecipients);
            }
            else
            {
                emailEventDto.CcRecipients.AddRange(pendingemail.CcRecipients);
            }
            emailEventDto.BccRecipients = new List<string>();
            if (pendingemail.BccRecipients != null)
            {
                emailEventDto.BccRecipients.AddRange(pendingemail.BccRecipients);
            }
            
            emailEventDto.IsBodyHtml = true;

            return emailEventDto;
        }
        #endregion
    }

}
