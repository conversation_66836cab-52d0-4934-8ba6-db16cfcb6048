﻿using Lrb.Admin.Data.Entities.Base;

namespace Lrb.Admin.Data.Entities
{
    public class Address : BaseEntity
    {
        public string? PlaceId { get; set; }
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public bool IsGoogleMapLocation { get; set; }

        public override string ToString()
        {
            //using in lead location filter
            var address = (!string.IsNullOrWhiteSpace(SubLocality) ? SubLocality + ", " : null) + (!string.IsNullOrWhiteSpace(Locality) ? Locality + ", " : null) +
            (!string.IsNullOrWhiteSpace(District) ? District + ", " : null) + (!string.IsNullOrWhiteSpace(City) ? City + ", " : null) +
            (!string.IsNullOrWhiteSpace(State) ? State + ", " : null) + (!string.IsNullOrWhiteSpace(Country) ? Country + ", " : null) +
            (!string.IsNullOrWhiteSpace(PostalCode) ? PostalCode + "." : null);
            return address;
        }
    }
}
