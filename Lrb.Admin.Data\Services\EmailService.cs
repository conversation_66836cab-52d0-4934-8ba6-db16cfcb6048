﻿using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.JSInterop;
using RestSharp;
using Lrb.Admin.Data.Repos.Factory;
using Newtonsoft.Json;
using Lrb.Admin.Data.Models.Common;
using System.Text.Json;
using iTextSharp.text.pdf;
using Lrb.Admin.Data.Models.Dtos;

public class EmailService : IEmailService
{
    private readonly IJSRuntime _jsruntime;
    private readonly IHttpService _httpService;
    private readonly LrbApiEndPoints _apiEndPoints;
    private readonly LrbDomains _domains;
    private readonly GoDaddySettings _goDaddySetings;
    private readonly IDapperRepositoryAsync _dapperRepository;
    private readonly ITenantRepository _tenantRepository;
    private readonly IConfiguration _config;
    private readonly string _baseUri = "https://localhost:7243/";
    private readonly string _baseUri1 = "https://localhost:7243/";
    public IEmailService _emailService;
    private readonly IDBConnectionFactory _dbFactory;


    public EmailService(IHttpService httpService, IDapperRepositoryAsync dapperRepository, IOptions<LrbApiEndPoints> endPointsoptions, IOptions<LrbDomains> domainOptions, IOptions<GoDaddySettings> goDaddySetings, ITenantRepository tenantRepository, IJSRuntime jsruntime, IDBConnectionFactory dbFactory)
    {
        _httpService = httpService;
        _apiEndPoints = endPointsoptions.Value;
        _domains = domainOptions.Value;
        _goDaddySetings = goDaddySetings.Value;
        _dapperRepository = dapperRepository;
        _tenantRepository = tenantRepository;
        _jsruntime = jsruntime;
        _dbFactory = dbFactory;
    }

    public class ApiErrorResponse
    {
        public string Title { get; set; }
        public int Status { get; set; }
        public Dictionary<string, string[]> Errors { get; set; }
    }

    public Task<List<T>> ExecuteQueryAsync<T>(SqlKata.Query query)
    {
        var result = new List<T>();
        return Task.FromResult(result);
    }
}










