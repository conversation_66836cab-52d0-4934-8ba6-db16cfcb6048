﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Common
{
    public class AddressModel
    {
        [Required]
        public string? PlaceId { get; set; }

        [Required(ErrorMessage ="Enter SubLocality")]
        public string? SubLocality { get; set; }
        public string? Locality { get; set; }
        public string? District { get; set; }

        [Required(ErrorMessage ="Enter City Name")]
        public string? City { get; set; }

        [Required(ErrorMessage ="Enter State Name")]
        public string? State { get; set; }

        [Required(ErrorMessage ="Enter Country Name ")]
        public string? Country { get; set; }

        [Required(ErrorMessage ="Enter Postal Code")]
        public string? PostalCode { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public bool IsGoogleMapLocation { get; set; }
    }
}
