﻿using Amazon.DynamoDBv2.DataModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Analytics
{
    [DynamoDBTable("AnalyticsRecord")]
    public class AnalyticsRecord
    {
        [DynamoDBHashKey("Id")]
        public Guid Id { get; set; } = default!;
        [DynamoDBProperty("UserId")]
        public Guid UserId { get; set; } = default!;
        [DynamoDBProperty("Username")]
        public string Username { get; set; } = default!;
        [DynamoDBProperty("TenantId")]
        public string TenantId { get; set; } = default!;
        [DynamoDBProperty("IsWebApp")]
        public bool IsWebApp { get; set; }
        [DynamoDBProperty("IsMobileApp")]
        public bool IsMobileApp { get; set; }
        [DynamoDBProperty("Browser")]
        public string? Browser { get; set; }
        [DynamoDBProperty("BrowserVersion")]
        public string? BrowserVersion { get; set; }
        [DynamoDBProperty("Device")]
        public string? Device { get; set; }
        [DynamoDBProperty("DeviceType")]
        public string? DeviceType { get; set; }
        [DynamoDBProperty("Orientation")]
        public string? Orientation { get; set; }
        [DynamoDBProperty("Os")]
        public string? Os { get; set; }
        [DynamoDBProperty("OsVersion")]
        public string? OsVersion { get; set; }
        [DynamoDBProperty("PlatformId")]
        public string? PlatformId { get; set; }
        [DynamoDBProperty("UserAgent")]
        public string? UserAgent { get; set; }
        [DynamoDBProperty("Latitude")]
        public string? Latitude { get; set; }
        [DynamoDBProperty("Longitude")]
        public string? Longitude { get; set; }
        [DynamoDBProperty("Platform")]
        public string? Platform { get; set; }
        [DynamoDBProperty("IpAddress")]
        public string? IpAddress { get; set; }
        [DynamoDBProperty("CreatedOn")]
        public DateTime? CreatedOn { get; set; }

     /*   public AnalyticsRecord(Guid id,Guid userId,string userName,string tenantId,bool isWebApp,bool isMobileApp,string browser
            ,string browserVersion,string device,string deviceType,string orientation,string os,string osVersion,string platformId,
            string userAgent,string latitude,string longitude,string ipAddress,DateTime createdOn)
        {
            Id = id;
            UserId = userId;
            Username = userName;
            TenantId = tenantId;
            IsWebApp = isWebApp;
            IsMobileApp = isMobileApp;
            Browser = browser;
            BrowserVersion = browserVersion;
            Device = device;
            DeviceType = deviceType;
            Orientation = orientation;
            Os = os;
            OsVersion = osVersion;
            PlatformId= platformId;
            UserAgent= userAgent;
            Latitude = latitude;
            Longitude = longitude;
            IpAddress = ipAddress;
            CreatedOn = createdOn;
        }*/
    }
}
