﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IVendorService
    {
        Task<PagedResponse<VendorModel, string>> GetVendorDetailAsync(GetAllVendorFilter filter);
        Task<Response<bool>> AddVendorAsync(VendorModel model);
        Task<Response<bool>> UpdateVendorAsync(Guid id, VendorModel model);
        Task<Response<bool>> DeleteVendorAsync(Guid id);
        Task<int> GetUserLImitAsync(string tenantId);
        Task<Response<bool>> UpdateUserLimitAsync(string tenantId, VendorModel model);
        Task<List<VendorModel>> GetAccountManagerAsync(string tenantId);
        Task<Response<bool>> UpdateAccountManagerAsync(string tenantId, VendorModel model);
        Task<VendorModel> GetVendorByTenantIdAsync(string tenantId);
        Task<List<string>> GetVendorsAllNames();
        public Task<int> UpdateVendorListAsync(List<string> vendorName, string tenantId);
        Task<int> DeleteTenantIdFromVendorAsync(List<string> VendorName, string TenantId);
    }
}
