﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IAuthService
    {
        Task<Response<string>> LoginUser(UserLoginDto model);
        Task<Response<bool>> RegisterUser(UserModelDto model);
        Task<UserModelDto> GetUser(Guid id);
    }
}
