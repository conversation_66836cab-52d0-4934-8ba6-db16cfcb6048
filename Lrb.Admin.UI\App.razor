﻿@*<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(Program).Assembly" PreferExactMatches="@true">
    <Found Context="routeData">
        <AuthorizeView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" >
        @*<FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <p role="alert">Sorry, there's nothing at this address.</p>
        </LayoutView>
    </NotFound>
</Router>
</CascadingAuthenticationState>*@

<CascadingAuthenticationState>
   <Router AppAssembly="@typeof(Program).Assembly"PreferExactMatches="@true">  
      <Found Context="routeData">  
      <AuthorizeRouteView RouteData="@routeData"DefaultLayout="@typeof(MainLayout)"> 
          <NotAuthorized>
                    <p>You are Not authorized  to Access It ....</p>
                    @*<Lrb.Admin.UI.Pages.Login.LoginPage />
                    <Lrb.Admin.UI.Pages.Login.RegisterUser />*@
          </NotAuthorized>
      </AuthorizeRouteView>
      <FocusOnNavigate RouteData="@routeData" Selector="h1" />
   </Found>  
   <NotFound>  
      <LayoutView Layout="@typeof(MainLayout)">  
         <p>Sorry, there's nothing at this address.</p>  
      </LayoutView>  
   </NotFound>  
</Router>    
</CascadingAuthenticationState>

<CascadingBlazoredModal />