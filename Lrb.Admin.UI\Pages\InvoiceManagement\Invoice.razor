﻿@page "/InvoiceManagement"
<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
</head>

<div class="tenantmasterheader">
    <div class="headerText">
        <span class="headername">
            <svg width="241" height="25" viewBox="0 0 241 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.23687 12.2657L12.635 17.2137C12.8794 17.4886 13.334 17.3157 13.334 16.9479L13.334 7.05208C13.334 6.68427 12.8794 6.51143 12.635 6.78633L8.23687 11.7343C8.10215 11.8858 8.10215 12.1142 8.23687 12.2657Z" fill="#292A2B" />
                <path d="M31.46 20V17.7H33.76V8.3H31.46V6H38.66V8.3H36.36V17.7H38.66V20H31.46ZM41.5377 20V9.46H43.9377L43.9777 11.62L43.5177 11.86C43.651 11.38 43.911 10.9467 44.2977 10.56C44.6843 10.16 45.1443 9.84 45.6777 9.6C46.211 9.36 46.7577 9.24 47.3177 9.24C48.1177 9.24 48.7843 9.4 49.3177 9.72C49.8643 10.04 50.271 10.52 50.5377 11.16C50.8177 11.8 50.9577 12.6 50.9577 13.56V20H48.4977V13.74C48.4977 13.2067 48.4243 12.7667 48.2777 12.42C48.131 12.06 47.9043 11.8 47.5977 11.64C47.291 11.4667 46.9177 11.3867 46.4777 11.4C46.1177 11.4 45.7843 11.46 45.4777 11.58C45.1843 11.6867 44.9243 11.8467 44.6977 12.06C44.4843 12.26 44.311 12.4933 44.1777 12.76C44.0577 13.0267 43.9977 13.32 43.9977 13.64V20H42.7777C42.5377 20 42.311 20 42.0977 20C41.8977 20 41.711 20 41.5377 20ZM57.1052 20L52.6652 9.46H55.3452L58.3452 17.34L57.8252 17.48L60.9852 9.46H63.6052L58.9452 20H57.1052ZM69.9319 20.2C68.8785 20.2 67.9385 19.9667 67.1119 19.5C66.2852 19.02 65.6319 18.3667 65.1519 17.54C64.6719 16.7133 64.4319 15.7733 64.4319 14.72C64.4319 13.6667 64.6719 12.7267 65.1519 11.9C65.6319 11.0733 66.2852 10.4267 67.1119 9.96C67.9385 9.48 68.8785 9.24 69.9319 9.24C70.9719 9.24 71.9052 9.48 72.7319 9.96C73.5585 10.4267 74.2052 11.0733 74.6719 11.9C75.1519 12.7267 75.3919 13.6667 75.3919 14.72C75.3919 15.7733 75.1519 16.7133 74.6719 17.54C74.2052 18.3667 73.5585 19.02 72.7319 19.5C71.9052 19.9667 70.9719 20.2 69.9319 20.2ZM69.9319 18.02C70.5052 18.02 71.0119 17.8733 71.4519 17.58C71.9052 17.2867 72.2585 16.8933 72.5119 16.4C72.7785 15.9067 72.9052 15.3467 72.8919 14.72C72.9052 14.08 72.7785 13.5133 72.5119 13.02C72.2585 12.5267 71.9052 12.14 71.4519 11.86C71.0119 11.5667 70.5052 11.42 69.9319 11.42C69.3585 11.42 68.8385 11.5667 68.3719 11.86C67.9185 12.14 67.5652 12.5333 67.3119 13.04C67.0585 13.5333 66.9319 14.0933 66.9319 14.72C66.9319 15.3467 67.0585 15.9067 67.3119 16.4C67.5652 16.8933 67.9185 17.2867 68.3719 17.58C68.8385 17.8733 69.3585 18.02 69.9319 18.02ZM77.8723 20V9.46H80.3323V20H77.8723ZM79.0723 7.22C78.579 7.22 78.1923 7.1 77.9123 6.86C77.6457 6.60667 77.5123 6.25333 77.5123 5.8C77.5123 5.37333 77.6523 5.02667 77.9323 4.76C78.2123 4.49333 78.5923 4.36 79.0723 4.36C79.579 4.36 79.9657 4.48667 80.2323 4.74C80.5123 4.98 80.6523 5.33333 80.6523 5.8C80.6523 6.21333 80.5123 6.55333 80.2323 6.82C79.9523 7.08667 79.5657 7.22 79.0723 7.22ZM87.9717 20.2C86.9851 20.2 86.0984 19.96 85.3117 19.48C84.5251 19 83.9051 18.3467 83.4517 17.52C82.9984 16.6933 82.7717 15.76 82.7717 14.72C82.7717 13.68 82.9984 12.7467 83.4517 11.92C83.9051 11.0933 84.5251 10.44 85.3117 9.96C86.0984 9.48 86.9851 9.24 87.9717 9.24C88.9184 9.24 89.7784 9.42 90.5517 9.78C91.3251 10.14 91.9251 10.64 92.3517 11.28L90.9917 12.92C90.7917 12.64 90.5384 12.3867 90.2317 12.16C89.9251 11.9333 89.5984 11.7533 89.2517 11.62C88.9051 11.4867 88.5584 11.42 88.2117 11.42C87.6251 11.42 87.0984 11.5667 86.6317 11.86C86.1784 12.14 85.8184 12.5333 85.5517 13.04C85.2851 13.5333 85.1517 14.0933 85.1517 14.72C85.1517 15.3467 85.2851 15.9067 85.5517 16.4C85.8317 16.8933 86.2051 17.2867 86.6717 17.58C87.1384 17.8733 87.6584 18.02 88.2317 18.02C88.5784 18.02 88.9117 17.9667 89.2317 17.86C89.5651 17.74 89.8784 17.5733 90.1717 17.36C90.4651 17.1467 90.7384 16.88 90.9917 16.56L92.3517 18.22C91.8984 18.8067 91.2717 19.2867 90.4717 19.66C89.6851 20.02 88.8517 20.2 87.9717 20.2ZM99.173 20.2C98.0663 20.2 97.0863 19.9667 96.233 19.5C95.393 19.0333 94.733 18.4 94.253 17.6C93.7863 16.7867 93.553 15.86 93.553 14.82C93.553 13.9933 93.6863 13.24 93.953 12.56C94.2196 11.88 94.5863 11.2933 95.053 10.8C95.533 10.2933 96.0996 9.90667 96.753 9.64C97.4196 9.36 98.1463 9.22 98.933 9.22C99.6263 9.22 100.273 9.35333 100.873 9.62C101.473 9.88667 101.993 10.2533 102.433 10.72C102.873 11.1733 103.206 11.72 103.433 12.36C103.673 12.9867 103.786 13.6733 103.773 14.42L103.753 15.28H95.213L94.753 13.6H101.693L101.373 13.94V13.5C101.333 13.0867 101.2 12.7267 100.973 12.42C100.746 12.1 100.453 11.8533 100.093 11.68C99.7463 11.4933 99.3596 11.4 98.933 11.4C98.2796 11.4 97.7263 11.5267 97.273 11.78C96.833 12.0333 96.4996 12.4 96.273 12.88C96.0463 13.3467 95.933 13.9333 95.933 14.64C95.933 15.32 96.073 15.9133 96.353 16.42C96.6463 16.9267 97.053 17.32 97.573 17.6C98.1063 17.8667 98.7263 18 99.433 18C99.9263 18 100.38 17.92 100.793 17.76C101.206 17.6 101.653 17.3133 102.133 16.9L103.353 18.6C102.993 18.9333 102.58 19.22 102.113 19.46C101.66 19.6867 101.18 19.8667 100.673 20C100.166 20.1333 99.6663 20.2 99.173 20.2ZM112.271 20V6H114.731L119.831 14.3L118.391 14.28L123.551 6H125.891V20H123.311V14.74C123.311 13.54 123.337 12.46 123.391 11.5C123.457 10.54 123.564 9.58667 123.711 8.64L124.031 9.5L119.691 16.2H118.371L114.171 9.56L114.451 8.64C114.597 9.53333 114.697 10.4533 114.751 11.4C114.817 12.3333 114.851 13.4467 114.851 14.74V20H112.271ZM133.193 20.2C132.327 20.2 131.54 19.96 130.833 19.48C130.127 19 129.56 18.3467 129.133 17.52C128.707 16.6933 128.493 15.7533 128.493 14.7C128.493 13.6467 128.707 12.7067 129.133 11.88C129.56 11.0533 130.14 10.4067 130.873 9.94C131.607 9.47333 132.433 9.24 133.353 9.24C133.887 9.24 134.373 9.32 134.813 9.48C135.253 9.62667 135.64 9.84 135.973 10.12C136.307 10.4 136.58 10.72 136.793 11.08C137.02 11.44 137.173 11.8267 137.253 12.24L136.713 12.1V9.46H139.193V20H136.693V17.48L137.273 17.38C137.18 17.74 137.007 18.0933 136.753 18.44C136.513 18.7733 136.207 19.0733 135.833 19.34C135.473 19.5933 135.067 19.8 134.613 19.96C134.173 20.12 133.7 20.2 133.193 20.2ZM133.873 18.02C134.447 18.02 134.953 17.88 135.393 17.6C135.833 17.32 136.173 16.9333 136.413 16.44C136.667 15.9333 136.793 15.3533 136.793 14.7C136.793 14.06 136.667 13.4933 136.413 13C136.173 12.5067 135.833 12.12 135.393 11.84C134.953 11.56 134.447 11.42 133.873 11.42C133.3 11.42 132.793 11.56 132.353 11.84C131.927 12.12 131.593 12.5067 131.353 13C131.113 13.4933 130.993 14.06 130.993 14.7C130.993 15.3533 131.113 15.9333 131.353 16.44C131.593 16.9333 131.927 17.32 132.353 17.6C132.793 17.88 133.3 18.02 133.873 18.02ZM142.045 20V9.46H144.445L144.485 11.62L144.025 11.86C144.159 11.38 144.419 10.9467 144.805 10.56C145.192 10.16 145.652 9.84 146.185 9.6C146.719 9.36 147.265 9.24 147.825 9.24C148.625 9.24 149.292 9.4 149.825 9.72C150.372 10.04 150.779 10.52 151.045 11.16C151.325 11.8 151.465 12.6 151.465 13.56V20H149.005V13.74C149.005 13.2067 148.932 12.7667 148.785 12.42C148.639 12.06 148.412 11.8 148.105 11.64C147.799 11.4667 147.425 11.3867 146.985 11.4C146.625 11.4 146.292 11.46 145.985 11.58C145.692 11.6867 145.432 11.8467 145.205 12.06C144.992 12.26 144.819 12.4933 144.685 12.76C144.565 13.0267 144.505 13.32 144.505 13.64V20H143.285C143.045 20 142.819 20 142.605 20C142.405 20 142.219 20 142.045 20ZM158.213 20.2C157.346 20.2 156.56 19.96 155.853 19.48C155.146 19 154.58 18.3467 154.153 17.52C153.726 16.6933 153.513 15.7533 153.513 14.7C153.513 13.6467 153.726 12.7067 154.153 11.88C154.58 11.0533 155.16 10.4067 155.893 9.94C156.626 9.47333 157.453 9.24 158.373 9.24C158.906 9.24 159.393 9.32 159.833 9.48C160.273 9.62667 160.66 9.84 160.993 10.12C161.326 10.4 161.6 10.72 161.813 11.08C162.04 11.44 162.193 11.8267 162.273 12.24L161.733 12.1V9.46H164.213V20H161.713V17.48L162.293 17.38C162.2 17.74 162.026 18.0933 161.773 18.44C161.533 18.7733 161.226 19.0733 160.853 19.34C160.493 19.5933 160.086 19.8 159.633 19.96C159.193 20.12 158.72 20.2 158.213 20.2ZM158.893 18.02C159.466 18.02 159.973 17.88 160.413 17.6C160.853 17.32 161.193 16.9333 161.433 16.44C161.686 15.9333 161.813 15.3533 161.813 14.7C161.813 14.06 161.686 13.4933 161.433 13C161.193 12.5067 160.853 12.12 160.413 11.84C159.973 11.56 159.466 11.42 158.893 11.42C158.32 11.42 157.813 11.56 157.373 11.84C156.946 12.12 156.613 12.5067 156.373 13C156.133 13.4933 156.013 14.06 156.013 14.7C156.013 15.3533 156.133 15.9333 156.373 16.44C156.613 16.9333 156.946 17.32 157.373 17.6C157.813 17.88 158.32 18.02 158.893 18.02ZM171.805 24.6C171.018 24.6 170.232 24.48 169.445 24.24C168.672 24 168.045 23.6867 167.565 23.3L168.445 21.48C168.725 21.68 169.038 21.8533 169.385 22C169.732 22.1467 170.098 22.26 170.485 22.34C170.885 22.42 171.278 22.46 171.665 22.46C172.372 22.46 172.958 22.3467 173.425 22.12C173.905 21.9067 174.265 21.5733 174.505 21.12C174.745 20.68 174.865 20.1267 174.865 19.46V17.52L175.225 17.66C175.145 18.0733 174.925 18.4667 174.565 18.84C174.205 19.2133 173.752 19.52 173.205 19.76C172.658 19.9867 172.098 20.1 171.525 20.1C170.525 20.1 169.638 19.8667 168.865 19.4C168.105 18.9333 167.498 18.3 167.045 17.5C166.605 16.6867 166.385 15.7533 166.385 14.7C166.385 13.6467 166.605 12.7133 167.045 11.9C167.485 11.0733 168.085 10.4267 168.845 9.96C169.618 9.48 170.485 9.24 171.445 9.24C171.845 9.24 172.232 9.28667 172.605 9.38C172.978 9.47333 173.318 9.60667 173.625 9.78C173.945 9.95333 174.232 10.1467 174.485 10.36C174.738 10.5733 174.945 10.8 175.105 11.04C175.265 11.28 175.365 11.5133 175.405 11.74L174.885 11.9L174.925 9.46H177.345V19.28C177.345 20.1333 177.218 20.8867 176.965 21.54C176.712 22.1933 176.345 22.7467 175.865 23.2C175.385 23.6667 174.805 24.0133 174.125 24.24C173.445 24.48 172.672 24.6 171.805 24.6ZM171.905 18C172.505 18 173.032 17.86 173.485 17.58C173.952 17.3 174.312 16.9133 174.565 16.42C174.818 15.9267 174.945 15.36 174.945 14.72C174.945 14.0667 174.812 13.4933 174.545 13C174.292 12.4933 173.938 12.1 173.485 11.82C173.032 11.54 172.505 11.4 171.905 11.4C171.305 11.4 170.778 11.5467 170.325 11.84C169.872 12.12 169.512 12.5133 169.245 13.02C168.992 13.5133 168.865 14.08 168.865 14.72C168.865 15.3467 168.992 15.9133 169.245 16.42C169.512 16.9133 169.872 17.3 170.325 17.58C170.778 17.86 171.305 18 171.905 18ZM185.13 20.2C184.023 20.2 183.043 19.9667 182.19 19.5C181.35 19.0333 180.69 18.4 180.21 17.6C179.743 16.7867 179.51 15.86 179.51 14.82C179.51 13.9933 179.643 13.24 179.91 12.56C180.177 11.88 180.543 11.2933 181.01 10.8C181.49 10.2933 182.057 9.90667 182.71 9.64C183.377 9.36 184.103 9.22 184.89 9.22C185.583 9.22 186.23 9.35333 186.83 9.62C187.43 9.88667 187.95 10.2533 188.39 10.72C188.83 11.1733 189.163 11.72 189.39 12.36C189.63 12.9867 189.743 13.6733 189.73 14.42L189.71 15.28H181.17L180.71 13.6H187.65L187.33 13.94V13.5C187.29 13.0867 187.157 12.7267 186.93 12.42C186.703 12.1 186.41 11.8533 186.05 11.68C185.703 11.4933 185.317 11.4 184.89 11.4C184.237 11.4 183.683 11.5267 183.23 11.78C182.79 12.0333 182.457 12.4 182.23 12.88C182.003 13.3467 181.89 13.9333 181.89 14.64C181.89 15.32 182.03 15.9133 182.31 16.42C182.603 16.9267 183.01 17.32 183.53 17.6C184.063 17.8667 184.683 18 185.39 18C185.883 18 186.337 17.92 186.75 17.76C187.163 17.6 187.61 17.3133 188.09 16.9L189.31 18.6C188.95 18.9333 188.537 19.22 188.07 19.46C187.617 19.6867 187.137 19.8667 186.63 20C186.123 20.1333 185.623 20.2 185.13 20.2ZM191.869 20V9.46H194.289L194.329 11.58L193.929 11.74C194.049 11.3933 194.229 11.0733 194.469 10.78C194.709 10.4733 194.995 10.2133 195.329 10C195.662 9.77333 196.015 9.6 196.389 9.48C196.762 9.34667 197.142 9.28 197.529 9.28C198.102 9.28 198.609 9.37333 199.049 9.56C199.502 9.73333 199.875 10.0133 200.169 10.4C200.475 10.7867 200.702 11.28 200.849 11.88L200.469 11.8L200.629 11.48C200.775 11.16 200.982 10.8733 201.249 10.62C201.515 10.3533 201.815 10.12 202.149 9.92C202.482 9.70667 202.829 9.54667 203.189 9.44C203.562 9.33333 203.929 9.28 204.289 9.28C205.089 9.28 205.755 9.44 206.289 9.76C206.822 10.08 207.222 10.5667 207.489 11.22C207.755 11.8733 207.889 12.68 207.889 13.64V20H205.409V13.8C205.409 13.2667 205.335 12.8267 205.189 12.48C205.055 12.1333 204.842 11.88 204.549 11.72C204.269 11.5467 203.915 11.46 203.489 11.46C203.155 11.46 202.835 11.52 202.529 11.64C202.235 11.7467 201.982 11.9067 201.769 12.12C201.555 12.32 201.389 12.5533 201.269 12.82C201.149 13.0867 201.089 13.38 201.089 13.7V20H198.609V13.78C198.609 13.2733 198.535 12.8533 198.389 12.52C198.242 12.1733 198.029 11.9133 197.749 11.74C197.469 11.5533 197.129 11.46 196.729 11.46C196.395 11.46 196.082 11.52 195.789 11.64C195.495 11.7467 195.242 11.9 195.029 12.1C194.815 12.3 194.649 12.5333 194.529 12.8C194.409 13.0667 194.349 13.36 194.349 13.68V20H191.869ZM215.56 20.2C214.453 20.2 213.473 19.9667 212.62 19.5C211.78 19.0333 211.12 18.4 210.64 17.6C210.173 16.7867 209.94 15.86 209.94 14.82C209.94 13.9933 210.073 13.24 210.34 12.56C210.606 11.88 210.973 11.2933 211.44 10.8C211.92 10.2933 212.486 9.90667 213.14 9.64C213.806 9.36 214.533 9.22 215.32 9.22C216.013 9.22 216.66 9.35333 217.26 9.62C217.86 9.88667 218.38 10.2533 218.82 10.72C219.26 11.1733 219.593 11.72 219.82 12.36C220.06 12.9867 220.173 13.6733 220.16 14.42L220.14 15.28H211.6L211.14 13.6H218.08L217.76 13.94V13.5C217.72 13.0867 217.586 12.7267 217.36 12.42C217.133 12.1 216.84 11.8533 216.48 11.68C216.133 11.4933 215.746 11.4 215.32 11.4C214.666 11.4 214.113 11.5267 213.66 11.78C213.22 12.0333 212.886 12.4 212.66 12.88C212.433 13.3467 212.32 13.9333 212.32 14.64C212.32 15.32 212.46 15.9133 212.74 16.42C213.033 16.9267 213.44 17.32 213.96 17.6C214.493 17.8667 215.113 18 215.82 18C216.313 18 216.766 17.92 217.18 17.76C217.593 17.6 218.04 17.3133 218.52 16.9L219.74 18.6C219.38 18.9333 218.966 19.22 218.5 19.46C218.046 19.6867 217.566 19.8667 217.06 20C216.553 20.1333 216.053 20.2 215.56 20.2ZM222.338 20V9.46H224.738L224.778 11.62L224.318 11.86C224.452 11.38 224.712 10.9467 225.098 10.56C225.485 10.16 225.945 9.84 226.478 9.6C227.012 9.36 227.558 9.24 228.118 9.24C228.918 9.24 229.585 9.4 230.118 9.72C230.665 10.04 231.072 10.52 231.338 11.16C231.618 11.8 231.758 12.6 231.758 13.56V20H229.298V13.74C229.298 13.2067 229.225 12.7667 229.078 12.42C228.932 12.06 228.705 11.8 228.398 11.64C228.092 11.4667 227.718 11.3867 227.278 11.4C226.918 11.4 226.585 11.46 226.278 11.58C225.985 11.6867 225.725 11.8467 225.498 12.06C225.285 12.26 225.112 12.4933 224.978 12.76C224.858 13.0267 224.798 13.32 224.798 13.64V20H223.578C223.338 20 223.112 20 222.898 20C222.698 20 222.512 20 222.338 20ZM235.666 20V6.78H238.126V20H235.666ZM233.586 11.74V9.46H240.426V11.74H233.586Z" fill="#292A2B" />
            </svg>


        </span>
    </div>
    <div class="headerbuttons">
        <span @onclick="async () => { Filter = new(); Paginate(1); await GetInvoiceAsync(); }">
            <svg width="89" height="38" viewBox="0 0 89 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="89" height="38" rx="4" fill="#292A2B" />
                <path d="M16.4396 24.4261C16.5371 24.4727 16.6439 24.4967 16.752 24.4964C16.9183 24.4964 17.0796 24.4398 17.2094 24.3359C17.3392 24.232 17.4298 24.0869 17.4662 23.9247C17.5026 23.7625 17.4827 23.5926 17.4097 23.4432C17.3367 23.2938 17.215 23.1737 17.0647 23.1027C16.1347 22.6641 15.3813 21.9225 14.928 20.9996C14.4747 20.0767 14.3484 19.0272 14.5698 18.023C14.7912 17.0189 15.3471 16.1198 16.1465 15.4731C16.616 15.0932 17.1531 14.8137 17.7247 14.6462L17.1747 15.7462C17.119 15.8577 17.0927 15.9816 17.0984 16.1061C17.104 16.2307 17.1413 16.3517 17.2068 16.4577C17.2723 16.5638 17.3638 16.6513 17.4726 16.7121C17.5815 16.7728 17.704 16.8048 17.8287 16.8049C17.9648 16.8055 18.0983 16.7678 18.2141 16.6963C18.3299 16.6247 18.4233 16.5222 18.4838 16.4002L19.6545 14.0587C19.7412 13.8851 19.7554 13.6842 19.694 13.5001C19.6326 13.3161 19.5005 13.1639 19.3269 13.0772C19.2197 13.0236 19.102 12.9977 18.9843 13.0003C18.9763 13 18.9683 13 18.9603 13C17.6004 13.009 16.2839 13.4797 15.2266 14.335C14.1693 15.1902 13.4338 16.3793 13.1409 17.7073C12.8479 19.0353 13.0148 20.4234 13.6141 21.6441C14.2135 22.8648 15.2097 23.8457 16.4396 24.4261Z" fill="white" />
                <path d="M19.0193 24.9998C19.0246 24.9999 19.0299 25 19.0352 25H19.0397C20.3996 24.991 21.7161 24.5203 22.7734 23.665C23.8307 22.8098 24.5662 21.6207 24.8591 20.2927C25.1521 18.9647 24.9852 17.5766 24.3859 16.3559C23.7865 15.1351 22.7903 14.1542 21.5604 13.5739C21.463 13.5272 21.3563 13.503 21.2483 13.503C21.1095 13.5036 20.9737 13.5433 20.8565 13.6176C20.7393 13.692 20.6454 13.7979 20.5858 13.9232C20.5448 14.0101 20.5213 14.1042 20.5167 14.2001C20.5121 14.2961 20.5265 14.392 20.5589 14.4824C20.5914 14.5728 20.6414 14.656 20.706 14.727C20.7707 14.7981 20.8487 14.8558 20.9356 14.8967C21.8655 15.3354 22.6188 16.0771 23.0721 17C23.5253 17.923 23.6516 18.9725 23.4302 19.9767C23.2088 20.9808 22.6528 21.8799 21.8535 22.5267C21.384 22.9066 20.847 23.1861 20.2753 23.3536L20.8252 22.2538C20.912 22.0802 20.9262 21.8793 20.8647 21.6952C20.8033 21.5112 20.6713 21.359 20.4977 21.2723C20.3241 21.1856 20.1232 21.1714 19.9391 21.2328C19.755 21.2943 19.6029 21.4263 19.5162 21.5999L18.3454 23.9413C18.2898 24.0528 18.2635 24.1767 18.2691 24.3013C18.2747 24.4258 18.312 24.5468 18.3775 24.6528C18.443 24.7589 18.5346 24.8464 18.6434 24.9072C18.7522 24.968 18.8748 24.9999 18.9994 25C19.006 25 19.0127 25 19.0193 24.9998Z" fill="white" />
                <path d="M35.128 24V15.6H38.776C39.28 15.6 39.74 15.72 40.156 15.96C40.572 16.192 40.9 16.512 41.14 16.92C41.388 17.32 41.512 17.772 41.512 18.276C41.512 18.756 41.388 19.2 41.14 19.608C40.9 20.008 40.572 20.328 40.156 20.568C39.748 20.8 39.288 20.916 38.776 20.916H36.652V24H35.128ZM40 24L37.864 20.208L39.472 19.908L41.848 24.012L40 24ZM36.652 19.56H38.788C39.02 19.56 39.22 19.508 39.388 19.404C39.564 19.292 39.7 19.14 39.796 18.948C39.892 18.756 39.94 18.544 39.94 18.312C39.94 18.048 39.88 17.82 39.76 17.628C39.64 17.436 39.472 17.284 39.256 17.172C39.04 17.06 38.792 17.004 38.512 17.004H36.652V19.56ZM45.9608 24.12C45.2968 24.12 44.7088 23.98 44.1968 23.7C43.6928 23.42 43.2968 23.04 43.0088 22.56C42.7288 22.072 42.5888 21.516 42.5888 20.892C42.5888 20.396 42.6688 19.944 42.8288 19.536C42.9888 19.128 43.2088 18.776 43.4888 18.48C43.7768 18.176 44.1168 17.944 44.5088 17.784C44.9088 17.616 45.3448 17.532 45.8168 17.532C46.2328 17.532 46.6208 17.612 46.9808 17.772C47.3408 17.932 47.6528 18.152 47.9168 18.432C48.1808 18.704 48.3808 19.032 48.5168 19.416C48.6608 19.792 48.7288 20.204 48.7208 20.652L48.7088 21.168H43.5848L43.3088 20.16H47.4728L47.2808 20.364V20.1C47.2568 19.852 47.1768 19.636 47.0408 19.452C46.9048 19.26 46.7288 19.112 46.5128 19.008C46.3048 18.896 46.0728 18.84 45.8168 18.84C45.4248 18.84 45.0928 18.916 44.8208 19.068C44.5568 19.22 44.3568 19.44 44.2208 19.728C44.0848 20.008 44.0168 20.36 44.0168 20.784C44.0168 21.192 44.1008 21.548 44.2688 21.852C44.4448 22.156 44.6888 22.392 45.0008 22.56C45.3208 22.72 45.6928 22.8 46.1168 22.8C46.4128 22.8 46.6848 22.752 46.9328 22.656C47.1808 22.56 47.4488 22.388 47.7368 22.14L48.4688 23.16C48.2528 23.36 48.0048 23.532 47.7248 23.676C47.4528 23.812 47.1648 23.92 46.8608 24C46.5568 24.08 46.2568 24.12 45.9608 24.12ZM50.6041 24V17.112C50.6041 16.728 50.6881 16.388 50.8561 16.092C51.0241 15.788 51.2561 15.552 51.5521 15.384C51.8481 15.208 52.1921 15.12 52.5841 15.12C52.8561 15.12 53.1081 15.168 53.3401 15.264C53.5721 15.352 53.7681 15.476 53.9281 15.636L53.4721 16.752C53.3681 16.664 53.2561 16.596 53.1361 16.548C53.0241 16.5 52.9161 16.476 52.8121 16.476C52.6521 16.476 52.5161 16.504 52.4041 16.56C52.3001 16.608 52.2201 16.684 52.1641 16.788C52.1161 16.892 52.0921 17.016 52.0921 17.16V24H51.3481C51.2041 24 51.0681 24 50.9401 24C50.8201 24 50.7081 24 50.6041 24ZM49.6201 19.104V17.808H53.5201V19.104H49.6201ZM54.739 24V17.676H56.191L56.215 19.692L56.011 19.236C56.099 18.916 56.251 18.628 56.467 18.372C56.683 18.116 56.931 17.916 57.211 17.772C57.499 17.62 57.799 17.544 58.111 17.544C58.247 17.544 58.375 17.556 58.495 17.58C58.623 17.604 58.727 17.632 58.807 17.664L58.411 19.284C58.323 19.236 58.215 19.196 58.087 19.164C57.959 19.132 57.831 19.116 57.703 19.116C57.503 19.116 57.311 19.156 57.127 19.236C56.951 19.308 56.795 19.412 56.659 19.548C56.523 19.684 56.415 19.844 56.335 20.028C56.263 20.204 56.227 20.404 56.227 20.628V24H54.739ZM62.4843 24.12C61.8203 24.12 61.2323 23.98 60.7203 23.7C60.2163 23.42 59.8203 23.04 59.5323 22.56C59.2523 22.072 59.1123 21.516 59.1123 20.892C59.1123 20.396 59.1923 19.944 59.3523 19.536C59.5123 19.128 59.7323 18.776 60.0123 18.48C60.3003 18.176 60.6403 17.944 61.0323 17.784C61.4323 17.616 61.8683 17.532 62.3403 17.532C62.7563 17.532 63.1443 17.612 63.5043 17.772C63.8643 17.932 64.1763 18.152 64.4403 18.432C64.7043 18.704 64.9043 19.032 65.0403 19.416C65.1843 19.792 65.2523 20.204 65.2443 20.652L65.2323 21.168H60.1083L59.8323 20.16H63.9963L63.8043 20.364V20.1C63.7803 19.852 63.7003 19.636 63.5643 19.452C63.4283 19.26 63.2523 19.112 63.0363 19.008C62.8283 18.896 62.5963 18.84 62.3403 18.84C61.9483 18.84 61.6163 18.916 61.3443 19.068C61.0803 19.22 60.8803 19.44 60.7443 19.728C60.6083 20.008 60.5403 20.36 60.5403 20.784C60.5403 21.192 60.6243 21.548 60.7923 21.852C60.9683 22.156 61.2123 22.392 61.5243 22.56C61.8443 22.72 62.2163 22.8 62.6403 22.8C62.9363 22.8 63.2083 22.752 63.4563 22.656C63.7043 22.56 63.9723 22.388 64.2603 22.14L64.9922 23.16C64.7763 23.36 64.5283 23.532 64.2483 23.676C63.9763 23.812 63.6883 23.92 63.3843 24C63.0803 24.08 62.7803 24.12 62.4843 24.12ZM68.6635 24.12C68.1035 24.12 67.5995 24.028 67.1515 23.844C66.7115 23.652 66.3515 23.38 66.0715 23.028L67.0315 22.2C67.2715 22.472 67.5395 22.668 67.8355 22.788C68.1315 22.908 68.4435 22.968 68.7715 22.968C68.9075 22.968 69.0275 22.952 69.1315 22.92C69.2435 22.888 69.3395 22.84 69.4195 22.776C69.4995 22.712 69.5595 22.64 69.5995 22.56C69.6475 22.472 69.6715 22.376 69.6715 22.272C69.6715 22.08 69.5995 21.928 69.4555 21.816C69.3755 21.76 69.2475 21.7 69.0715 21.636C68.9035 21.572 68.6835 21.508 68.4115 21.444C67.9795 21.332 67.6195 21.204 67.3315 21.06C67.0435 20.908 66.8195 20.74 66.6595 20.556C66.5235 20.404 66.4195 20.232 66.3475 20.04C66.2835 19.848 66.2515 19.64 66.2515 19.416C66.2515 19.136 66.3115 18.884 66.4315 18.66C66.5595 18.428 66.7315 18.228 66.9475 18.06C67.1635 17.892 67.4155 17.764 67.7035 17.676C67.9915 17.588 68.2915 17.544 68.6035 17.544C68.9235 17.544 69.2315 17.584 69.5275 17.664C69.8315 17.744 70.1115 17.86 70.3675 18.012C70.6315 18.156 70.8555 18.332 71.0395 18.54L70.2235 19.44C70.0715 19.296 69.9035 19.168 69.7195 19.056C69.5435 18.944 69.3635 18.856 69.1795 18.792C68.9955 18.72 68.8235 18.684 68.6635 18.684C68.5115 18.684 68.3755 18.7 68.2555 18.732C68.1355 18.756 68.0355 18.796 67.9555 18.852C67.8755 18.908 67.8115 18.98 67.7635 19.068C67.7235 19.148 67.7035 19.244 67.7035 19.356C67.7115 19.452 67.7355 19.544 67.7755 19.632C67.8235 19.712 67.8875 19.78 67.9675 19.836C68.0555 19.892 68.1875 19.956 68.3635 20.028C68.5395 20.1 68.7675 20.168 69.0475 20.232C69.4395 20.336 69.7675 20.452 70.0315 20.58C70.2955 20.708 70.5035 20.856 70.6555 21.024C70.8075 21.168 70.9155 21.336 70.9795 21.528C71.0435 21.72 71.0755 21.932 71.0755 22.164C71.0755 22.54 70.9675 22.876 70.7515 23.172C70.5435 23.468 70.2555 23.7 69.8875 23.868C69.5275 24.036 69.1195 24.12 68.6635 24.12ZM72.3757 24V15.12H73.8397V18.972L73.5637 19.116C73.6437 18.828 73.7997 18.568 74.0317 18.336C74.2637 18.096 74.5397 17.904 74.8597 17.76C75.1797 17.616 75.5077 17.544 75.8437 17.544C76.3237 17.544 76.7237 17.64 77.0437 17.832C77.3717 18.024 77.6157 18.312 77.7757 18.696C77.9437 19.08 78.0277 19.56 78.0277 20.136V24H76.5517V20.244C76.5517 19.924 76.5077 19.66 76.4197 19.452C76.3317 19.236 76.1957 19.08 76.0117 18.984C75.8277 18.88 75.6037 18.832 75.3397 18.84C75.1237 18.84 74.9237 18.876 74.7397 18.948C74.5637 19.012 74.4077 19.108 74.2717 19.236C74.1437 19.356 74.0397 19.496 73.9597 19.656C73.8877 19.816 73.8517 19.992 73.8517 20.184V24H73.1197C72.9757 24 72.8397 24 72.7117 24C72.5917 24 72.4797 24 72.3757 24Z" fill="white" />
            </svg>
        </span>
    </div>
</div>

<div class="TenantMasterTable">
    <div class="TenantMasterTableHeader">
        <div class="SearchBar">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="searchicon">
                <path d="M16.6673 16.6654L15.334 15.332M3.33398 9.66537C3.33398 6.16756 6.16951 3.33203 9.66732 3.33203C13.1651 3.33203 16.0006 6.16756 16.0006 9.66537C16.0006 13.1632 13.1651 15.9987 9.66732 15.9987C6.16951 15.9987 3.33398 13.1632 3.33398 9.66537Z" stroke="#BBBBBB" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <input type="text" placeholder="Press Enter To Search" class="Searchbarbar" @bind-value="Filter.search" @oninput="e => Filter.search = e.Value?.ToString()" @onkeydown="HandleKeyDownSearch">
            <span class="searchButton" @onclick="GetInvoiceAsync">
                Search
            </span>
        </div>
        <div class="buttons">
            <span class="buttonbutton" @onclick="@OpenFilter">Filter</span>
            <span class="buttonbutton" title="Page Size">
                <select id="pagesizeinput" @bind="PageSize" @bind:after="GetInvoiceAsync">
                    @foreach (var dv in DropdownValues)
                    {
                        <option value="@dv">@dv</option>
                    }
                </select>
            </span>
        </div>
    </div>
    @if (Collection == null)
    {
        <span>No Data Found</span>
    }
    else
    {
        @if (Collection.Any(i => i != null))
        {
            <div class="all-tables">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Sl.No</th>
                                <th>TenantId</th>
                                <th>TenantName</th>
                                <th>Invoice Number</th>
                                <th>Invoice Status</th>
                                <th>Transaction Date</th>
                                <th>Date Of OnBoard</th>
                                <th>Subscription Date</th>
                                <th>Licenses Count</th>
                                <th>License Validity</th>
                                <th>Vendors</th>
                                <th>GST Number</th>
                                <th>Total Package Amount</th>
                                <th>Net Amount Paid</th>
                                <th>Gst Amount Paid</th>
                                <th>Total Amount Paid</th>
                                <th>Due Amount</th>
                                <th>Due Date</th>
                                <th>TDS Deductions</th>
                                <th>TDS Amount</th>
                                <th>Address</th>
                                <th>Billing Type</th>
                                <th>Subscription Type</th>
                                <th>Payment Mode</th>
                                <th>Invoice Download</th>
                                <th>TDS Upload</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var items in Collection.Select((value, i) => new { value, i }))
                        {
                            <tr>
                                <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                                <td class="sort-link" @onclick="@(async () => await NavigateToComponent(items.value?.TenantId))" style="cursor :pointer"><u>@items.value?.TenantId</u></td>
                                <td>@items?.value?.TenantName</td>
                                <td>
                                    @if (editingItem != items.value)
                                    {
                                        <span>@items?.value?.InvoiceNumber</span>
                                        <span class="oi oi-pencil" style="cursor:pointer" title="Edit" @onclick="() => StartEditingInvoiceNo(items.value)"></span>
                                    }
                                    else
                                    {
                                        <select @bind="@items.value.InvoiceNumber">
                                            @foreach (var invoice in InvoiceNo)
                                            {
                                                <option value="@invoice">@invoice</option>
                                            }
                                        </select>
                                        <button @onclick="( async () => { await UpdateInvoiceNo(items.value.InvoiceNumber); await OnInitializedAsync();})">Save</button>
                                        <button @onclick="( async () => {editingItem = null;})">Cancel</button>

                                    }
                                </td>
                                <td>@items?.value?.InvoiceStatus</td>
                                <td>
                                    @if (editingItemtrdate != items.value)
                                    {
                                        <span>
                                            @items?.value?.TransactionDate?.ToLocalTime().ToString("dd/MM/yyy")
                                            <span class="oi oi-pencil" style="cursor:pointer;padding-left:4px" @onclick="() => EditDate(items.value)"></span>
                                        </span>
                                    }
                                    else
                                    {
                                        <input type="date" id="expiry-date" @bind-value="items.value.TransactionDate"></input>
                                        <button @onclick="( async () => { await EditTransactionDateAsync(items.value); await OnInitializedAsync();})">Save</button>
                                        <button @onclick="( async () => {editingItemtrdate = null;})">Cancel</button>
                                    }
                                    @* @if (EditExpiryDate)
                        {

                        } *@

                                </td>
                                <td>@items?.value?.DateofOnboard?.ToString("dd/MM/yyyy")</td>
                                <td>@items?.value?.SubscriptionDate?.ToString("dd/MM/yyyy")</td>
                                <td>@items?.value?.NoOfLicenses</td>
                                <td>@items?.value?.LicenseValidity?.ToString("dd/MM/yyyy")</td>
                                <td>@items?.value?.VendorNames</td>
                                <td>@items?.value?.GSTNumber</td>
                                <td>@items?.value?.TotalPackageAmount</td>
                                <td>@items?.value?.NetAmountPaid</td>
                                <td>@items?.value?.GSTAmount</td>
                                <td>@items?.value?.TotalAmountPaid</td>
                                <td>@items?.value?.DueAmount</td>
                                @if (items?.value?.DueAmount == 0)
                                {
                                    <td>Completed</td>
                                }
                                else
                                {
                                    <td>@items?.value?.DueDate?.ToString("dd/MM/yyyy")</td>
                                }
                                <td>
                                    @if (editingInvoiceTDS != items.value)
                                    {
                                        <span>@items?.value?.TDSDeductions%</span>
                                        <span class="oi oi-pencil" style="cursor:pointer" title="Edit" @onclick="() => StartEditingTDS(items.value)"></span>
                                    }
                                    else
                                    {
                                        <input type="text" @bind="NewTDS"
                                               @bind:event="oninput"
                                               @onkeydown="HandleKeyDownDownTDS"
                                               @onblur="StopEditingTDS" />
                                    }
                                </td>
                                <td>@(((items?.value?.TDSDeductions / 100) * items.value?.NetAmountPaid)?.ToString("F2"))</td>
                                <td>@items?.value?.Address</td>
                                <td>@items?.value?.BillingType</td>
                                <td>@items?.value?.SubscriptionType</td>
                                <td>@items?.value?.PaymentMode</td>
                                <td>
                                    <span class="sort-link" @onclick="@(async () => await UpdateAndDownloadPdf(items.value))" style="cursor :pointer"> DOWNLOAD </span>
                                </td>
                                <td>
                                    <InputFile id="TDS_File" OnChange="@(e => TDSFileUpload(e, items.value))" />
                                </td>
                            </tr>
                        }
                        </tbody>
                        
                    </table>
                </div>
                
            </div>
        }
        else
        {
            <span>No Data Found</span>
        }
    }

    <div class="tenantmasterfooter">
        <div class="pagesize">
            <div class="pagesizebox">
                
                <span> @ShowingCount of @TotalCount</span>
            </div>
        </div>
        <div class="pagenumber">
            @if (Collection.Count < PageSize)
            {
                <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()">Back</span>
            }
            else
            {
                @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                {
                    if (i < PageNumber || i == 0)
                    {
                        <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()"> &lt;&lt; </span>
                    }
                    else if (i == PageNumber)
                    {
                        <span id="pagenumbercircle">@i</span>
                    }
                    else if (i > PageNumber)
                    {
                        <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber()">&gt;&gt;</span>
                    }
                }
            }



        </div>
    </div>
</div>