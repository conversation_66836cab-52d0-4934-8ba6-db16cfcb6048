﻿using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using Lrb.Admin.Data.Utility;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Lrb.Admin.Data.Repos
{
    public class AuthenticationRepository : IAuthenticationRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        private readonly IConfiguration _Config;
        protected readonly string _schema = "tenantmaster";
        protected Query Query;
        private readonly IJSRuntime _runtime;
        private readonly IActivityRepository _activityRepository;

        public AuthenticationRepository(IDBConnectionFactory dbFactory, IConfiguration config, IJSRuntime runtime, IActivityRepository activityRepository)
        {
            _dbFactory = dbFactory;
            _Config = config;
            _runtime = runtime;
            _activityRepository = activityRepository;
        }
        public async Task<Response<string>> ValidateUser(UserLoginDto model)
        {
            try
            {
                bool isVerified;
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var user = await FindUserName(model.UserName);
                if (user == null)
                {
                    _runtime.InvokeVoidAsync("alert", "Invalid Usermame");
                    return new Response<string>(null, null);
                }
                /*var passwordSalt = JsonConvert.DeserializeObject<byte[]>(user.PasswordSalt);
                var passwordHash = JsonConvert.DeserializeObject<byte[]>(user.PasswordHash);
                using (var hmac = new HMACSHA512(passwordSalt)) 
                {
                    var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(model.Password)); 
                    isVerified = hash.SequenceEqual(passwordHash);
                }*/
                var password = await FindUserPassword(model.Password);
                if (password == null)
                {
                    _runtime.InvokeVoidAsync("alert", "Wrong Password");
                    return new Response<string>(null, null);
                }
                string token = GenerateJwtTokken(user);

                AdminUserActivity userActivity = new()
                {
                    AdminUserId = user.Id,
                    TenantId = user.TenantId,
                    Token = token,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.Login
                };
                var result = await _activityRepository.AddAdminUserActity(userActivity);
                connection.Close();
                return new Response<string>(token, null);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> RegisterUser(UserModelDto model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var user = FindUserName(model.UserName);
                if (user == null)
                {
                    throw new Exception("Authentication Failed");
                }
                model.Id = Guid.NewGuid();
                model.IsActive = true;
                model.IsDeleted = false;
                model.CreatedOn = DateTime.Now;
                model.Modified = model.CreatedOn;
                CreatePasswordHash(model.Password, out byte[] passwordHash, out byte[] passwordSalt);
                model.PasswordHash = JsonConvert.SerializeObject(passwordHash);
                model.PasswordSalt = JsonConvert.SerializeObject(passwordSalt);
                var affectedRow = db.Query($"{_schema}.Users").InsertAsync(model);
                // connection.Close();
                return await affectedRow > 0;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        private void CreatePasswordHash(string password, out byte[] passwordHash, out byte[] passwordSalt)
        {
            using (var hmac = new HMACSHA512())
            {
                passwordSalt = hmac.Key;
                passwordHash = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
            }
        }

        private string GenerateJwtTokken(UserModelDto model)
        {
            var role = EnumHelper.GetEnumDescription(model.Role);
            List<Claim> claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, model.UserName),
                new Claim(ClaimTypes.Name,model.Password),
                new Claim(ClaimTypes.Role, role)
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_Config.GetSection("AppSettings:Token").Value!));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                claims: claims,
                expires: DateTime.UtcNow.AddDays(1),
                signingCredentials: creds
                );

            var tokenHandler = new JwtSecurityTokenHandler();
            var serializedToken = tokenHandler.WriteToken(token);

            return serializedToken;
        }

        public async Task<UserModelDto> FindUserName(string userName)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{_schema}.Users").Where("UserName", "=", userName).Where("IsDeleted", "=", false);
                var user = query.FirstOrDefault<UserModelDto>();
                connection.Close();
                return user;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserModelDto> FindUserPassword(string password)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{_schema}.Users").Where("Password", "=", password).Where("IsDeleted", "=", false);
                var result = query.FirstOrDefault<UserModelDto>();
                connection.Close();
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateUserAsync(Guid id, UserModelDto model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var user = await GetUserByIdAsync(id);
                if (user == null)
                {
                    throw new Exception("User Not Found");
                }
                var query = await db.Query($"{_schema}.Users").Where("Id", "=", id)
                    .UpdateAsync(new { FirstName = model.FirstName, LastName = model.LastName, UserName = model.UserName, Password = model.Password });
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserModelDto> GetUserByIdAsync(Guid id)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{_schema}.Users").Where("Id", "=", id).Select("Id", "FirstName", "LastName", "UserName", "Password", "PhoneNumber", "Role", "TenantId");
                connection.Close();
                return await query.FirstOrDefaultAsync<UserModelDto>();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
