﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class Vendor
    {
        public Guid Id{ get; set; }
        public string? TenantId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set;}
        public string? PhoneNumber {  get; set; }
        public string? ReportsTo {  get; set; }
        public string? ReferralCode {  get; set; }
        public DateTime? ModifiedOn {  get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? LastModifiedOn { get;set; }
        public string? TenantIdList {  get; set; }
    }
}
