﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Analytics;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.Analytic
{
    public partial class Analytics
    {
        [Inject]
        public IAnalyticsService AnalyticsService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }

        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public SpinnerService SpinnerService { get; set; }
        public int PageSize { get; set; } = 10;
        public int PageNumber { get; set; } = 1;
        public double MaxPageNumber { get; set; }
        public int TotalCount { get; set; }
        public int ShowingCount { get; set; }
        public List<string> Records { get; set; } = new();
        public AnalyticsFilter Filter { get; set; } = new();
        public ObservableCollection<AnalyticsRecord> Collection { get; set; } = new();
        List<AnalyticsRecord> List { get; set; } = new();
        public string? Token { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await GetAnalyticsRecord();
        }

        protected void GetTrueFilter()
        {
            var tenants = Filter.GetType().GetProperties();
            foreach (var tenant in tenants)
            {
                if (tenant.GetValue(Filter) != null && tenant.GetValue(Filter).ToString() == "Select")
                {
                    tenant.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }
        private async Task GetAnalyticsRecord()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<AnalyticsRecord, string> response = null;
                if (Filter.TenantId != null)
                {
                    var fetchTask = InvokeAsync(async () =>
                    {
                        response = await AnalyticsService.GetAnalyticsRecordAsync(Filter);
                    });
                    while (!fetchTask.IsCompleted)
                    {
                        SpinnerService.Show();
                        await fetchTask;
                        fetchTask.Dispose();
                        SpinnerService.Hide();
                    }
                    if (response != null && response.Items != null)
                    {
                        Collection = new ObservableCollection<AnalyticsRecord>(response.Items);
                        Collection.Skip(PageNumber - 1).Take(PageSize);
                        TotalCount = (int)response.TotalCount;
                        MaxPageNumber = (double)TotalCount / (double)PageSize;
                        ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                        Token = await LocalStorage.GetItemAsync<string>("token");
                        var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                        AdminUserActivity userActivity = new()
                        {
                            AdminUserId = userActivityHistory.AdminUserId,
                            TenantId = userActivityHistory.TenantId,
                            ActivityTime = DateTime.UtcNow,
                            Activity = ActivityConst.AnalyticsTable,
                            Token = Token
                        };
                        await ActivityService.UserActivityAsync(userActivity);
                    }
                    else
                    {
                        await JSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        public async Task GetTodayRecord()
        {
            PagedResponse<AnalyticsRecord, string> response = null;
            var fetchTask = InvokeAsync(async () =>
            {
                response = await AnalyticsService.GetTodayRecordAsync(Filter);
            });
            while (!fetchTask.IsCompleted)
            {
                SpinnerService.Show();
                await fetchTask;
                fetchTask.Dispose();
                SpinnerService.Hide();
            }
            if (response != null && response.Items != null)
            {
                Collection = new ObservableCollection<AnalyticsRecord>(response.Items);
                TotalCount = (int)response.TotalCount;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
            }

        }

        public async Task GetYesterdayRecord()
        {
            PagedResponse<AnalyticsRecord, string> response = null;
            var fetchTask = InvokeAsync(async () =>
            {
                response = await AnalyticsService.GetYesterdayRecordAsync(Filter);
            });
            while (!fetchTask.IsCompleted)
            {
                SpinnerService.Show();
                await fetchTask;
                fetchTask.Dispose();
                SpinnerService.Hide();
            }
            if (response != null && response.Items != null)
            {
                Collection = new ObservableCollection<AnalyticsRecord>(response.Items);
                TotalCount = (int)response.TotalCount;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
            }

        }

        public async Task GetLastWeekRecord()
        {
            PagedResponse<AnalyticsRecord, string> response = null;
            var fetchTask = InvokeAsync(async () =>
            {
                response = await AnalyticsService.GetLastWeekRecordAsync(Filter);
            });
            while (!fetchTask.IsCompleted)
            {
                SpinnerService.Show();
                await fetchTask;
                fetchTask.Dispose();
                SpinnerService.Hide();
            }
            if (response != null && response.Items != null)
            {
                Collection = new ObservableCollection<AnalyticsRecord>(response.Items);
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                Collection.Skip(PageNumber - 1).Take(PageSize);
                TotalCount = (int)response.TotalCount;
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
            }

        }

        public async Task GetLastMonthRecord()
        {
            PagedResponse<AnalyticsRecord, string> response = null;
            var fetchTask = InvokeAsync(async () =>
            {
                response = await AnalyticsService.GetLastMonthRecordAsync(Filter);
            });
            while (!fetchTask.IsCompleted)
            {
                SpinnerService.Show();
                await fetchTask;
                fetchTask.Dispose();
                SpinnerService.Hide();
            }
            if (response != null && response.Items != null)
            {
                Collection = new ObservableCollection<AnalyticsRecord>(response.Items);
                TotalCount = (int)response.TotalCount;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
            }

        }

        public async Task GetTenantIdAsync()
        {
            Records = await TenantService.GetAnalyticsTenantId();
        }
        public async Task Paginate(int page)
        {
            PageNumber = page;
        }

        private async Task IncreasePage()
        {

            Collection.Clear();
            if (PageNumber < (Math.Ceiling(MaxPageNumber)))
            {
                PageNumber++;
            }
            await GetAnalyticsRecord();
        }
        private async Task DecreasePage()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetAnalyticsRecord();
        }
    }
}
