﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class UpdateTenantPaymentDto
    {
        private double _soldPrice;
        private double _dueAmount;
        private double _gstamt;
        public Guid Id { get; set; }
        public PaymentType Type {  get; set; }
        public int PartNumber {  get; set; }
        public double Amount
        {
            get => _soldPrice;
            set => _soldPrice = value;
        }
        public double GSTAmount
        {
            get => _gstamt;
            set => _gstamt= value;
        }
        public double TotalAmount
        {
            get => _soldPrice + _gstamt;
            set => _soldPrice = value;
        }
        public DateTime? PartPaymentDate { get; set; } = DateTime.UtcNow;
        public Guid SubscriptionId { get; set; }
        public string? TenantId {  get; set; }
        public int PartPaid {  get; set; }
        
        public PaymentMode Mode {  get; set; }
        public DateTime? NextDueDate { get; set; } = DateTime.UtcNow;
        public double NextDueAmount {  get; set; }
        public string? Description {  get; set; }
        public bool IsDeleted {  get; set; }
        public DateTime LastModifiedOn { get; set; }
    }
}
