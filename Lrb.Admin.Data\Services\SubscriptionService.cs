﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Humanizer;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly ISubscritionRepository _subscritionRepository;

        public SubscriptionService(ISubscritionRepository subscritionRepository)
        {
            _subscritionRepository = subscritionRepository;
        }

        public async Task<Response<bool>> CreateNewSubscriptionAsync(LRBSubscription model)
        {
            var response = await _subscritionRepository.CreateNewSubscriptionAsync(model);
            return response;
        }

        public async Task<int> GetNoOfLicenseAsync(string tenantId)
        {
            var response = await _subscritionRepository.GetNoOfLicense(tenantId);
            return response;
        }

        public async Task<SubscriptionViewModel> GetSubscriptionAsync(string tenantId)
        {
            var response = await _subscritionRepository.GetTenantSubscriptionAsync(tenantId);
            return response;
        }

        public async Task<PagedResponse<LRBSubscription, string>> GetSubscriptionDetailsAsync(string tenantId)
        {
            var response = await _subscritionRepository.GetSubscriptionDetails(tenantId);
            return response;
        }

        public async Task<UpdateSubscriptionDto> GetSubscriptionToUpdateAsync(string tenantId)
        {
            var response = await _subscritionRepository.GetSubscriptionToUpdateAsync(tenantId);
            return response;
        }

        public async Task<IEnumerable<LRBPayments>> GetTenantAllPaymentAsync(Guid? subscriptionId)
        {
            var response = await _subscritionRepository.GetTenantAllPaymentAsync(subscriptionId);
            return response;
        }

        public async Task<LRBSubscription> GetTenantSubscriptionById(Guid? subscriptionId,bool isAddOn)
        {
            var response = await _subscritionRepository.GetTenantSubscriptionByIdAsync(subscriptionId,isAddOn);
            return response;
        }

        public async Task<Response<bool>> UpdatePartPaymentAsync(string tenantId, LRBPayments dto)
        {
            var response = await _subscritionRepository.UpdatePartPaymentAsync(tenantId , dto);
            return response;
        }

        public async Task<int> UpdateSubscriptionData(LRBSubscription dto)
        {
            var response = await _subscritionRepository.UpdateSubscription(dto);
            return response;
        }
        public async Task<TenantsDto> GetAllLicencesValidityAsync(string TenantId)
        {
            var response = await _subscritionRepository.GetAllLicencesValidity(TenantId);
            return response;
        }

        public async Task<Response<bool>> CreateAddOnSubscription(LRBSubscriptionAddOns model)
        {
            var response = await _subscritionRepository.CreateAddOnSubscriptionAsync(model);
            return response;
        }

        public async Task<IEnumerable<LRBSubscriptionAddOns>> GetAllAddOnSubscriptionsAsync(Guid? subscriptionId)
        {
            var response = await _subscritionRepository.GetAllAddOnSubscriptions(subscriptionId);
            return response;
        }

        public async Task<LRBSubscriptionAddOns> GetAddOnSubscriptionById(Guid? subscriptionId)
        {
            var response = await _subscritionRepository.GetAddOnSubscriptionByIdAsync(subscriptionId);
            return response;
        }

        public async Task<Response<bool>> DeleteSubscriptionByIdAsync(Guid? Id, string? entityName)
        {
            var response = await _subscritionRepository.DeleteSubscriptionById(Id,entityName);
            return response;
        }

        public async Task<bool> DeleteAddonSubscriptionAsync(Guid? AddonId)
        {
            var response = await _subscritionRepository.DeleteAddonSubscription(AddonId);
            return response;
        }

        public async Task<bool> DeleteSubscriptionAsync(Guid? subscriptionId)
        {
            var response = await _subscritionRepository.DeleteSubscription(subscriptionId);
            return response;
        }

        public async Task<int> GetLatestInvoiceNumberAsync()
        {
            var response = await _subscritionRepository.GetLatestInvoiceNumber();
            return response;
        }

        public async Task<Response<bool>> DeleteSubscriptionPayment(LRBPayments payments)
        {
            var response = await _subscritionRepository.DeleteSubscriptionPayment(payments);
            return response;
        }

        public async Task<int> GetSubscriptionCountAsync(string tenantId)
        {
            var response = await _subscritionRepository.GetSubscriptionCount(tenantId);
            return response;
        }
    }
}
