﻿using Lrb.Admin.Data.Models.Dtos;

namespace Lrb.Admin.Data.Models.Wrappers
{
    public class PagedResponse<T, T1> : Response<T1>
    {

        public IEnumerable<T> Items { get; set; }
        public int ItemsCount => Items?.Count() ?? 0;
        public double TotalCount { get; set; }

        public PagedResponse()
        {
            Message = null;
            Succeeded = true;
            Errors = null;
        }
        public PagedResponse(IEnumerable<T> items, int count)
        {
            Items = items;
            TotalCount = count;
            Data = default;
            Message = null;
            Succeeded = true;
            Errors = null;
        }
        public PagedResponse(IEnumerable<T> items, int count, T1 data)
        {
            Items = items;
            TotalCount = count;
            Data = data;
            Message = null;
            Succeeded = true;
            Errors = null;
        }
        public PagedResponse(T1 data)
        {
            Data = data;
            Message = null;
            Succeeded = true;
            Errors = null;
        }
    }
}
