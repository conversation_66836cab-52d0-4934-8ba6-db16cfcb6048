﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Notification
{
    public class NotificationViewModel
    {
        public DateTime? Date { get; set; }
        public string? UserName { get; set; }
        public string? Title { get; set; }   
        public string? MessageBody { get; set; }    
        public bool IsSuccess { get; set; }
    }
}
