﻿using Blazored.LocalStorage;
using Blazored.Modal;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.Notification
{
    public partial class NotifySingleUserModal
    {
        [Inject]
        public INotificationService NotificationService { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        private ModalParameters Parameter { get; set; }
        [CascadingParameter]
        public GetTenantUserModel userModel { get; set; } = new();
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public string? Id { get; set; }
        [Parameter]
        public string? UserName { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        public string? Token { get; set; }
        public NotificationModelDto Model { get; set; } = new();
        public Notifications Notifications { get; set; } = new();
        public async Task SendNotificationToUser()
        {
            Guid id = Guid.Parse(Id);
            Model.UserIds.Add(id);
            var status = await NotificationService.CheckDeviceTokenExpireAsync(TenantId, id);
            if (status.IsDeleted == true)
            {
                throw new Exception("Device Token Expire.... Login Again !");
            }
            var user = await TenantService.GetUserfromIdAsync(Id);
            var response = await NotificationService.SendNotificationAsync(TenantId, Model);
            if (response != null)
            {
                Notifications notifications = new()
                {
                    Title = Model.Title,
                    MessageBody = Model.Body,
                    TenantId = TenantId,
                    UserId = id,
                    IsSuccess = response.Data,
                    UserName = user.UserName,
                };
                await NotificationService.CaptureNotificationOfUserAsync(notifications);
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity userActivity = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.SentNotification + " " + user.UserName,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(userActivity);
            }
        }
    }
}
