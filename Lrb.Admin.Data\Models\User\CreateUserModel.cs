﻿using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Roles;
using System.ComponentModel.DataAnnotations;

namespace Lrb.Admin.Data.Models.User
{
    public class CreateUserModel
    {
        private string phoneNumber = default!;
        private string password = default!;
        private string confirmPassword = default!;

        [Required(ErrorMessage = "FirstName Cannot be Empty")]
        public string FirstName { get; set; } = default!;
        [Required(ErrorMessage = "LastName Cannot be Empty")]
        public string LastName { get; set; } = default!;
        [Required(ErrorMessage = "Email Connot be Empty")]
        public string Email { get; set; } = default!;
        [Required(ErrorMessage = "UserName Connot be Empty")]
        public string UserName { get; set; } = default!;
        [Required(ErrorMessage = "Password Atleast 8 Character")]
        public string? Password { get => password; set => password = string.IsNullOrWhiteSpace(value) ? "123Pa$$word!" : value; }
        [Required(ErrorMessage = "Password and Confirm Password should be same")]
        public string ConfirmPassword { get => confirmPassword; set => confirmPassword = string.IsNullOrWhiteSpace(value) ? "123Pa$$word!" : value; }
        public string? PhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Address { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public List<UserRoleModel>? UserRoles { get; set; }
        public Guid? ReportsTo { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? DesignationId { get; set; }
        public string? Description { get; set; }
        [Required(ErrorMessage = "Please Enter Tenant Id")]
        public string? TenantId { get; set; }
        public UserRole UserRole { get; set; }
    }
}
