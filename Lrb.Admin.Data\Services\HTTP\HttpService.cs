﻿using Lrb.Admin.Data.Models.Wrappers;
using Mapster;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;

namespace Lrb.Admin.Data.Services.HTTP
{
    public class HttpService : IHttpService
    {
        private readonly SpinnerService _spinnerService;
        public IJSRuntime _jsRuntime { get; set; }

        public Uri BaseUri { get; set; }
        public HttpClient Client { get; set; }

        public HttpService(SpinnerService spinnerService, IJSRuntime jsRuntime)
        {
            _spinnerService = spinnerService;
            _jsRuntime = jsRuntime;
        }
        /// <summary>
        /// Executes Http GET Request
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        public async Task<TResponse> GetAsync<TResponse>(string baseUrl, string resource, Dictionary<string, string> headers = null)
        {
            object a = 10;
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, resource);
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.Headers.Add(header.Key, header.Value);
                }
            }
            var response = await ExcuteAsync<TResponse>(request, CancellationToken.None);
            return response;
        }
        /// <summary>
        /// Executes Http POST Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<TResponse> PostAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : class
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, resource);
            if (DTO != null)
            {
                var json = JsonConvert.SerializeObject(DTO);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }
            else
            {
                throw new ArgumentNullException("Request body can not be null.");
            }
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            return await ExcuteAsync<TResponse>(request, CancellationToken.None);
        }
        /// <summary>
        /// Executes Http PUT Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        public async Task<TResponse> PutAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO = null, Dictionary<string, string> headers = null) where TBody : class
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Put, resource);
            if (DTO != null)
            {
                StringContent content = new(JsonConvert.SerializeObject(DTO), Encoding.UTF8, "application/json"); ;
                request.Content = content;
            }
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            return await ExcuteAsync<TResponse>(request, CancellationToken.None);
        }
        /// <summary>
        /// Executes Http POST Request with filebytes
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="headers"></param>
        /// <param name="fileBytes"></param>
        /// <returns></returns>
        public async Task<TResponse?> PostFileAsync<TResponse>(string baseUrl, string resource, IBrowserFile file, Dictionary<string, string> headers = null)
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, resource);
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            if (file != null)
            {
                using var content = new MultipartFormDataContent();
                {
                    using MemoryStream ms = new();
                    {
                        await file.OpenReadStream(file.Size).CopyToAsync(ms, (int)file.Size);
                        var fileContent = new ByteArrayContent(ms.ToArray());
                        fileContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);
                        fileContent.Headers.ContentLength = file.Size;
                        content.Add(content: fileContent, name: "\"file\"", fileName: file.Name);
                        request.Content = content;
                        return await ExcuteAsync<TResponse>(request, CancellationToken.None);
                    }
                }
            }
            else
            {
                throw new ArgumentNullException("Request fileStream can not be null.");
            }

        }
        /// <summary>
        /// Executes Http DELETE Request
        /// </summary>
        /// <typeparam name="TBody"></typeparam>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="resource"></param>
        /// <param name="DTO"></param>
        /// <param name="headers"></param>
        /// <returns name="TResponse"></returns>
        public async Task<TResponse?> DeleteAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO = null, Dictionary<string, string> headers = null) where TBody : class
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Delete, resource);
            if (DTO != null)
            {
                var json = JsonConvert.SerializeObject(DTO);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            return await ExcuteAsync<TResponse>(request, CancellationToken.None);
        }
        /// <summary>
        /// Executes Any Http Resquest
        /// </summary>
        /// <typeparam name="Response"></typeparam>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns name="TResponse"></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<TResponse?> ExcuteAsync<TResponse>(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                _spinnerService.Show();
                var cts = new CancellationTokenSource(new TimeSpan(0, 5, 0));
                HttpResponseMessage response = await Client.SendAsync(request, cts.Token);
                if(response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return JsonConvert.DeserializeObject<TResponse>("null");
                }
                response = response.EnsureSuccessStatusCode();
                _spinnerService.Hide();
                TResponse? content = await response.Content.ReadFromJsonAsync<TResponse>();
                if(content is Models.Wrappers.Response<string> Get)
                {
                    _spinnerService.Show();
                    _spinnerService.Hide();
                }
                else if(content is Models.Wrappers.Response<string> stringRes)
                {
                    var alertMessage = string.IsNullOrWhiteSpace(stringRes.Message) ? stringRes.Data : stringRes.Message;
                    _jsRuntime.InvokeVoidAsync("alert", $"{alertMessage}");

                }
                else if (content is Models.Wrappers.Response res)
                {
                    _jsRuntime.InvokeVoidAsync("alert", $"{res.Message}");
                }
                return content;
            }
            catch (Exception e)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"{e.Message}");
                await _jsRuntime.InvokeVoidAsync("console.log", $"{e.Message}");
                _spinnerService.Hide();
                return JsonConvert.DeserializeObject<TResponse>("null");
            }
        }

        public async Task<TResponse> PatchAsync<TBody, TResponse>(string baseUrl, string resource, Dictionary<string, string> headers = null)
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Patch, resource);

            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            return await ExcuteAsync<TResponse>(request, CancellationToken.None);
        }

        public async Task<TResponse> PostSmsAsync<TBody, TResponse>(string baseUrl, string resource, TBody DTO, Dictionary<string, string> headers = null) where TBody : Dictionary<string, string>
        {
            BaseUri = new Uri(baseUrl);
            Client = new()
            {
                BaseAddress = BaseUri
            };
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, resource);
            if (DTO != null)
            {
                request.Content = new FormUrlEncodedContent(DTO);
            }
            else
            {
                throw new ArgumentNullException("Request body can not be null.");
            }
            if (headers != null && headers.Any())
            {
                foreach (var item in headers)
                {
                    request.Headers.Add(item.Key, item.Value);
                }
            }
            return await ExcuteAsync<TResponse>(request, CancellationToken.None);
        }


        public async Task<bool> PostAsync<T1, T2>(string url, T1 formContent, Dictionary<string, string> headers)
        {
            try
            {
                using var client = new HttpClient();

                if (headers != null && headers.Any())
                {
                    foreach (var header in headers)
                    {
                        client.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                }

                HttpResponseMessage response;
                if (formContent is MultipartFormDataContent)
                {
                    response = await client.PostAsync(url, formContent as MultipartFormDataContent);
                }
                else if (formContent is Dictionary<string, string>)
                {
                    var content = new FormUrlEncodedContent(formContent as Dictionary<string, string>);
                    response = await client.PostAsync(url, content);
                }
                else
                {
                    var jsonContent = new StringContent(JsonConvert.SerializeObject(formContent), Encoding.UTF8, "application/json");
                    response = await client.PostAsync(url, jsonContent);
                }

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var deserializedResult = JsonConvert.DeserializeObject<T2>(result);
                    return true;
                }
                else
                {
                    Console.WriteLine($"Failed with status code: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during HTTP request: {ex.Message}");
                return false;
            }
        }

    }
}
