﻿using Dapper;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Humanizer;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using Mapster;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Lrb.Admin.Data.Repos
{
    public class SubscritionRepository : ISubscritionRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string Schema = "tenantmaster.";

        public SubscritionRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        public async Task<Response<bool>> CreateNewSubscriptionAsync(LRBSubscription model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                if (model.IsActive == true)
                {
                    var SubExpire = await db.Query("LeadratBlack.Subscriptions")
                                            .Where("TenantId", model.TenantId)
                                            .Where("IsActive", true)
                                            .UpdateAsync(new
                                            {
                                                IsActive = false,
                                                IsExpired = true
                                            });
                    var AddonSubExpire = await db.Query("LeadratBlack.SubscriptionAddOns")
                                                 .Where("TenantId", model.TenantId)
                                                 .Where("IsActive", true)
                                                 .UpdateAsync(new
                                                 {
                                                     IsActive = false,
                                                     IsExpired = true
                                                 });
                }
                model.IsDeleted = false;
                //var query = await db.Query("LeadratBlack." + typeof(LRBSubscription).Name).InsertAsync(model);
                var query = await db.Query("LeadratBlack.Subscriptions").InsertAsync(model);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> CreateAddOnSubscriptionAsync(LRBSubscriptionAddOns model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = await db.Query("LeadratBlack.SubscriptionAddOns").InsertAsync(model);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<SubscriptionViewModel> GetTenantSubscriptionAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"Select s.\"Id\", s.\"Name\" , s.\"CreatedOn\", s.\"LicenseValidity\", s.\"NetAmount\", " +
                    $"s.\"SoldLicenses\",\r\ns.\"PaidAmount\", s.\"DueAmount\", p.\"NetAmount\", p.\"NextDueDate\", " +
                    $" p.\"CreatedOn\"\r\n\r\nfrom \"LeadratBlack\".\"Subscriptions\" s" +
                    $"\r\nLeft Join ( Select \"Payments\".\"SubscriptionId\",\r\n\t\t   " +
                    $"\"Payments\".\"NetAmount\",\"Payments\".\"CreatedOn\"\r\n\t\t" +
                    $",\"Payments\".\"Type\",\"Payments\".\"NextDueDate\" from \"LeadratBlack\".\"Payments\") p on p.\"SubscriptionId\" = s.\"Id\"\r\n" +
                    $"where s.\"TenantId\" = '{tenantId}' ";
                var query = (connection.Query<SubscriptionViewModel>(newQuery)).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<TenantsDto> GetAllLicencesValidity(string TenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"SELECT * FROM \"MultiTenancy\".\"Tenants\"\r\nWHERE \"Id\"='{TenantId}'";
                var result = (await connection.QueryAsync<TenantsDto>(newQuery)).FirstOrDefault();
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<LRBSubscription> GetTenantSubscriptionByIdAsync(Guid? subscriptionId, bool isAddOn)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                string? newQuery = null;
                if (isAddOn)
                {
                    newQuery = $"select * from \"LeadratBlack\".\"SubscriptionAddOns\" where \"Id\"='{subscriptionId}'";
                }
                else
                {
                newQuery = $"select * from \"LeadratBlack\".\"Subscriptions\" where \"Id\"='{subscriptionId}'";
                }
                var query = connection.Query<LRBSubscription>(newQuery).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }



        public async Task<LRBSubscriptionAddOns> GetAddOnSubscriptionByIdAsync(Guid? subscriptionId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"select * from \"LeadratBlack\".\"SubscriptionAddOns\" where \"Id\"='{subscriptionId}'";
                var query = connection.Query<LRBSubscriptionAddOns>(newQuery).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<LRBSubscriptionAddOns> GetAddOnSubscriptionByIdAsync(string? tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var newQuery = $"SELECT * FROM \"LeadratBlack\".\"SubscriptionAddOns\" where \"TenantId\" = '{tenantId}'";
                var query = connection.Query<LRBSubscriptionAddOns>(newQuery).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdatePartPaymentAsync(string tenantId, LRBPayments model)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    model.Id = Guid.NewGuid();
                    model.IsDeleted = false;
                    var query = db.Query($"LeadratBlack.Payments").Insert(model);
                    
                    if (query > 0) 
                    {
                        if (model.SubscriptionAddOnId != null)
                        {
                            await UpdateTenantDueAmountAsync(model.SubscriptionAddOnId, model.TotalAmount,model.NextDueDate , "SubscriptionAddOns");
                        }
                        else
                        {
                            await UpdateTenantDueAmountAsync(model.SubscriptionId, model.TotalAmount, model.NextDueDate, "Subscriptions");
                        }
                        return new Response<bool>(true);
                    }
                    else
                    {
                        return new Response<bool>(false);
                    }
                }
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }


        public async Task UpdateTenantDueAmountAsync(Guid? Id, double paidAmount,DateTime? NextDueDate,string name)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                if(name == "SubscriptionAddOns")
                {
                    var res = await GetAddOnSubscriptionByIdAsync(Id);
                    var totalDueAmount = res.DueAmount - paidAmount;
                    var totalPaidAmount = res.PaidAmount + paidAmount;
                    var totalAmount = res.PaidAmount + paidAmount + totalDueAmount;
                    var query = db.Query($"LeadratBlack.{name}").Where("Id", "=", res.Id).Update(new { DueAmount = totalDueAmount, DueDate = totalDueAmount == 0 ? (DateTime?)null : NextDueDate, PaidAmount = totalPaidAmount, TotalAmount = totalAmount, LastModifiedOn = DateTime.UtcNow});
                }
                else
                {
                    var res = await GetTenantSubscriptionByIdAsync(Id,false);
                    var totalDueAmount = res.DueAmount - paidAmount;
                    var totalPaidAmount = res.PaidAmount + paidAmount;
                    var totalAmount = res.PaidAmount + paidAmount + totalDueAmount;
                    var query = db.Query($"LeadratBlack.{name}").Where("Id", "=", res.Id).Update(new { DueAmount = totalDueAmount, DueDate = totalDueAmount == 0 ? (DateTime?)null : NextDueDate, PaidAmount = totalPaidAmount, TotalAmount = totalAmount, LastModifiedOn = DateTime.UtcNow });
                }
                
                connection.Close();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<LRBPayments>> GetTenantAllPaymentAsync(Guid? subscriptionId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var innerquery = $"SELECT * FROM \"LeadratBlack\".\"Payments\" WHERE \"SubscriptionId\" = '{subscriptionId}' or \"SubscriptionAddOnId\" = '{subscriptionId}'\r\nORDER BY \"IsDeleted\";";
                var query = connection.Query<LRBPayments>(innerquery);
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<LRBSubscriptionAddOns>> GetAllAddOnSubscriptions(Guid? subscriptionId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var innerQuery = $"Select * from \"LeadratBlack\".\"SubscriptionAddOns\" where \"SubscriptionId\"='{subscriptionId}' ORDER BY \"IsDeleted\" ";
                var query = connection.Query<LRBSubscriptionAddOns>(innerQuery);
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<SubscriptionViewModel> GetTenantPaymentDetailAsync(string tenantId, DateTime dueDate)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $" Select s.\"Id\", s.\"Name\" , s.\"LicenseCreated\", s.\"LicenseValidity\", s.\"NetAmount\",\r\n      " +
                    $" s.\"SoldLicenses\", s.\"PaidAmount\", s.\"DueAmount\", p.\"NetAmount\",\r\n\t   p.\"NextDueDate\",  " +
                    $"p.\"CreatedOn\"\r\nfrom \"LeadratBlack\".\"Subscriptions\" s\r\n" +
                    $"Left Join ( Select \"Payments\".\"SubscriptionId\",\r\n\t\t   \"Payments\".\"NetAmount\"," +
                    $"\"Payments\".\"CreatedOn\",\r\n\t\t \"Payments\".\"Type\"," +
                    $"\"Payments\".\"Mode\", \"Payments\".\"NextDueDate\"  \r\nfrom LeadratBlack.\"Payments\") p " +
                    $"on p.\"SubscriptionId\" = s.\"Id\"\r\nwhere s.\"TenantId\" = '{tenantId}' and p.\"NextDueDate\" >= {dueDate} ";
                var query = (await connection.QueryAsync<SubscriptionViewModel>(newQuery)).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateTenantLicenseAsync(string tenantId, UpdateSubscriptionDto model)
        {
            var connection = await _dbFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var res = await GetTenantSubscriptionAsync(tenantId);
            var licenses = res.SoldLicenses + model.SoldLicenses;
            var amount = res.NetAmount / res.SoldLicenses * model.SoldLicenses;
            var actualAmount = res.NetAmount + amount;
            var actualDueAmount = res.DueAmount + amount;
            var query = await db.Query("LeadratBlack.Subscriptions").Where("TenantId", "=", tenantId).UpdateAsync(new {  NetAmount = actualAmount, DueAmount = actualDueAmount });
            connection.Close();
            return new Response<bool>(query > 0);
        }

        public async Task<UpdateSubscriptionDto> GetSubscriptionToUpdateAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"Select s.\"Id\", s.\"SoldLicenses\",s.\"DueAmount\",p.\"Mode\",p.\"NextDueDate\"\r\nfrom LeadratBlack.\"Subscriptions\" s" +
                    $"\r\nleft Join (Select  \"Payments\".\"SubscriptionId\" , \"Payments\".\"Mode\",\"Payments\".\"NextDueDate\"" +
                    $"\r\n\t\t  from \"LeadratBlack\".\"Payments\") p on p.\"SubscriptionId\" = s.\"Id\"\r\nwhere s.\"TenantId\" = '{tenantId}'";
                var query = (await connection.QueryAsync<UpdateSubscriptionDto>(newQuery)).FirstOrDefault();
                connection.Close();
                return query;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);

            }
        }

        public async Task<int> GetNoOfLicense(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = $"SELECT \r\n    COALESCE(s.TotalSoldLicenses, 0) + COALESCE(sa.TotalSoldLicenses, 0) AS \"TotalSoldLicenses\"\r\nFROM (\r\n    SELECT SUM(\"SoldLicenses\") AS TotalSoldLicenses\r\n    FROM \"LeadratBlack\".\"Subscriptions\"\r\n    WHERE \"IsDeleted\" = false\r\n      AND \"IsExpired\" = false\r\n      AND \"IsActive\" = true\r\n      AND \"TenantId\" = '{tenantId}'\r\n) s,\r\n(\r\n    SELECT SUM(\"SoldLicenses\") AS TotalSoldLicenses\r\n    FROM \"LeadratBlack\".\"SubscriptionAddOns\"\r\n    WHERE \"SubscriptionId\" IN (\r\n        SELECT \"Id\"\r\n        FROM \"LeadratBlack\".\"Subscriptions\"\r\n        WHERE \"IsDeleted\" = false\r\n          AND \"IsExpired\" = false\r\n          AND \"IsActive\" = true\r\n          AND \"TenantId\" = '{tenantId}'\r\n    ) and \"IsExpired\" = false \r\n\tand \"IsActive\" = true \r\n\tand \"IsDeleted\" = false\r\n) sa;";
                var result = await connection.QueryAsync<int>(query);
                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool>DeleteSubscription(Guid? subscriptionId)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    var SubscriptionResult = await db.Query("LeadratBlack.Subscriptions").Where("Id", "=" , subscriptionId).UpdateAsync(new { IsDeleted = true, IsActive = false, IsExpired = true, DeletedOn = DateTime.UtcNow });
                    var AddonSubscriptionResult = await db.Query("LeadratBlack.SubscriptionAddOns").Where("SubscriptionId", "=", subscriptionId).UpdateAsync(new { IsDeleted = true, DeletedOn = DateTime.UtcNow });
                    var PaymentResult = await db.Query("LeadratBlack.Payments").Where("SubscriptionId", "=", subscriptionId).UpdateAsync(new { IsDeleted = true, DeletedOn = DateTime.UtcNow });
                    if (SubscriptionResult > 0)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            catch (Exception ex) 
            {
                throw new Exception($"Error Deleting Subscription : {ex.Message}");
            }
        }

        public async Task<bool> DeleteAddonSubscription(Guid? AddonId)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    var AddonSubscriptionResult = await db.Query("LeadratBlack.SubscriptionAddOns").Where("Id", "=", AddonId).UpdateAsync(new { IsDeleted = true, DeletedOn = DateTime.UtcNow });
                    var PaymentResult = await db.Query("LeadratBlack.Payments").Where("SubscriptionAddOnId", "=", AddonId).UpdateAsync(new { IsDeleted = true, DeletedOn = DateTime.UtcNow });
                    return (AddonSubscriptionResult > 0);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error Deleting Subscription : {ex.Message}");
            }
        }
        public async Task<Response<bool>> DeleteSubscriptionById(Guid? Id,string? entityName)
        {
            try
            {
                
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    if (entityName == "SubscriptionAddOns")
                    {
                        var addOnSubscriptionData = await GetAddOnSubscriptionByIdAsync(Id);
                        var subscriptionData = await GetTenantSubscriptionByIdAsync(addOnSubscriptionData.SubscriptionId,false);
                        var queryDate = await db.Query("LeadratBlack.Subscriptions")
                        .Where("Id", "=", addOnSubscriptionData.SubscriptionId)
                        .UpdateAsync(new
                        {
                            SoldLicenses = subscriptionData.SoldLicenses - addOnSubscriptionData.SoldLicenses,
                        });
                    }
                    
                    var result = await db.Query($"LeadratBlack.{entityName}")
                        .Where("Id", "=", Id)
                        .UpdateAsync(new
                        {
                            //DeletedOn = DateTime.UtcNow, -- This column is commented out because this column does not exist in the production table
                            IsDeleted = true
                        }) ;
                    return new Response<bool>(result > 0);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating {entityName}: {ex.Message}");
            }
        }

        public async Task<Response<bool>> DeleteSubscriptionPayment(LRBPayments payments)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    if(payments.SubscriptionAddOnId != null)
                    {
                        var db = new QueryFactory(connection, new PostgresCompiler());
                        var resultData = await GetTenantSubscriptionByIdAsync(payments.SubscriptionAddOnId, true);
                        var queryDate = await db.Query("LeadratBlack.SubscriptionAddOns")
                            .Where("Id", "=", payments.SubscriptionAddOnId)
                            .UpdateAsync(new
                            {
                                PaidAmount = resultData.PaidAmount - payments.TotalAmount,
                                DueAmount = resultData.DueAmount + payments.TotalAmount
                            });
                        return new Response<bool>(queryDate > 0);
                    }
                    else
                    {
                        var db = new QueryFactory(connection, new PostgresCompiler());
                        var resultData = await GetTenantSubscriptionByIdAsync(payments.SubscriptionId, false);
                        var queryDate = await db.Query("LeadratBlack.Subscriptions")
                            .Where("Id", "=", payments.SubscriptionId)
                            .UpdateAsync(new
                            {
                                PaidAmount = resultData.PaidAmount - payments.TotalAmount,
                                DueAmount = resultData.DueAmount + payments.TotalAmount
                            });
                        return new Response<bool>(queryDate > 0);
                    }
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"Error updating subscription: {ex.Message}");
            }
        }

        public async Task<PagedResponse<LRBSubscription, string>> GetSubscriptionDetails(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                //var query = $"Select s.\"Id\", s.\"Name\", s.\"TenantId\", s.\"NetAmount\", s.\"SoldLicenses\", s.\"NetAmount\", s.\"DueAmount\", s.\"TotalAmount\"," +
                //    $"\r\ns.\"GSTAmount\", s.\"BillingType\", s.\"PaymentDate\",pa.\"NextDueDate\", pa.\"Mode\"\r\nFrom tenantmaster.\"Subscriptions\" s\r\n" +
                //    $"Left Join (Select p.\"SubscriptionId\", p.\"NextDueDate\", p.\"Mode\"\r\n\t\t   from tenantmaster.\"Payments\" p) " +
                //    $"pa on pa.\"SubscriptionId\" = s.\"Id\"\r\nwhere s.\"IsDeleted\" = 'false' and s.\"TenantId\" = '{tenantId}'";
                var innerquery = $"select * from \"LeadratBlack\".\"Subscriptions\" where \"TenantId\" = '{tenantId}' order by \"IsDeleted\" ,\"CreatedOn\" desc";
                var result = await connection.QueryAsync<LRBSubscription>(innerquery);
                connection.Close();
                return new PagedResponse<LRBSubscription, string>(result, result.Count());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetSubscriptionCount(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var innerquery = $"select count(\"Id\") from \"LeadratBlack\".\"Subscriptions\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false'";
                var result = connection.Query<int>(innerquery).FirstOrDefault();
                connection.Close();
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> UpdateSubscription(LRBSubscription dto)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());

                    var result = await db.Query("LeadratBlack.Subscriptions")
                        .Where("Id", "=", dto.Id)
                        .UpdateAsync(new
                        {
                            SoldLicenses = dto.SoldLicenses,
                            NetAmount = dto.NetAmount,
                            GSTAmount = dto.GSTAmount,
                            TotalAmount = dto.TotalAmount,
                            PaidAmount = dto.PaidAmount,
                            DueAmount = dto.DueAmount,
                            BillingType = dto.BillingType,
                            DueDate = dto.DueDate,
                            LastModifiedOn = DateTime.UtcNow,
                            LicenseValidity = dto.LicenseValidity,
                            PaymentDate = dto.PaymentDate,
                        });

                    return result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating subscription: {ex.Message}");
            }
        }

        public async Task<int> UpdateAddOnSubscription(LRBSubscriptionAddOns dto)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    var result = await db.Query("LeadratBlack.SubscriptionAddOns")
                        .Where("Id", "=", dto.Id)
                        .UpdateAsync(new
                        {
                            SoldLicenses = dto.SoldLicenses,
                            NetAmount = dto.NetAmount,
                            GSTAmount = dto.GSTAmount,
                            TotalAmount = dto.TotalAmount,
                            PaidAmount = dto.PaidAmount,
                            DueAmount = dto.DueAmount,
                            LastModifiedOn = DateTime.UtcNow,
                            PaymentDate = dto.PaymentDate,
                            DueDate = dto.DueDate
                        });

                    return result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating AddOnSubscription: {ex.Message}");
            }
        }

        public async Task<int> GetLatestInvoiceNumber()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"SELECT MAX(\"InvoiceNumber\")FROM \"LeadratBlack\".\"Payments\"\r\n";
                var result = (await connection.QueryAsync<string>(newQuery)).FirstOrDefault();
                connection.Close();
                int intValue = 0;
                if (result != null)
                {
                    result = result.Substring(result.Length - 3);
                    bool success = int.TryParse(result, out intValue);
                }
                return intValue;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
