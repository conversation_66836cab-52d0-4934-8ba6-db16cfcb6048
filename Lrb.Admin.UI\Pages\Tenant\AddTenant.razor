﻿@page "/add-tenant"
<div class="Header">
    <span class="HeaderText">
        <svg width="149" height="25" viewBox="0 0 149 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.23687 12.2657L12.635 17.2137C12.8794 17.4886 13.334 17.3157 13.334 16.9479L13.334 7.05208C13.334 6.68427 12.8794 6.51143 12.635 6.78633L8.23687 11.7343C8.10215 11.8858 8.10215 12.1142 8.23687 12.2657Z" fill="#292A2B" />
            <path d="M37.92 20.2C36.92 20.2 35.9933 20.02 35.14 19.66C34.2867 19.3 33.54 18.8 32.9 18.16C32.2733 17.5067 31.7867 16.74 31.44 15.86C31.0933 14.98 30.92 14.02 30.92 12.98C30.92 11.94 31.0933 10.98 31.44 10.1C31.7867 9.22 32.2733 8.46 32.9 7.82C33.54 7.16667 34.2867 6.66 35.14 6.3C35.9933 5.94 36.92 5.76 37.92 5.76C38.9333 5.76 39.8667 5.94 40.72 6.3C41.5733 6.66 42.3133 7.16667 42.94 7.82C43.5667 8.47333 44.0533 9.24 44.4 10.12C44.7467 10.9867 44.92 11.94 44.92 12.98C44.92 14.0067 44.7467 14.96 44.4 15.84C44.0533 16.72 43.5667 17.4867 42.94 18.14C42.3133 18.7933 41.5733 19.3 40.72 19.66C39.8667 20.02 38.9333 20.2 37.92 20.2ZM37.92 17.72C38.56 17.72 39.14 17.6067 39.66 17.38C40.1933 17.14 40.6533 16.8067 41.04 16.38C41.4267 15.94 41.7267 15.4333 41.94 14.86C42.1667 14.2867 42.28 13.66 42.28 12.98C42.28 12.3 42.1667 11.6733 41.94 11.1C41.7267 10.5267 41.4267 10.0267 41.04 9.6C40.6533 9.16 40.1933 8.82667 39.66 8.6C39.14 8.36 38.56 8.24 37.92 8.24C37.2933 8.24 36.7133 8.36 36.18 8.6C35.6467 8.82667 35.1867 9.15333 34.8 9.58C34.4133 10.0067 34.1133 10.5067 33.9 11.08C33.6867 11.6533 33.58 12.2867 33.58 12.98C33.58 13.66 33.6867 14.2933 33.9 14.88C34.1133 15.4533 34.4133 15.9533 34.8 16.38C35.1867 16.8067 35.6467 17.14 36.18 17.38C36.7133 17.6067 37.2933 17.72 37.92 17.72ZM47.2994 20V9.46H49.6994L49.7394 11.62L49.2794 11.86C49.4127 11.38 49.6727 10.9467 50.0594 10.56C50.446 10.16 50.906 9.84 51.4394 9.6C51.9727 9.36 52.5194 9.24 53.0794 9.24C53.8794 9.24 54.546 9.4 55.0794 9.72C55.626 10.04 56.0327 10.52 56.2994 11.16C56.5794 11.8 56.7194 12.6 56.7194 13.56V20H54.2594V13.74C54.2594 13.2067 54.186 12.7667 54.0394 12.42C53.8927 12.06 53.666 11.8 53.3594 11.64C53.0527 11.4667 52.6794 11.3867 52.2394 11.4C51.8794 11.4 51.546 11.46 51.2394 11.58C50.946 11.6867 50.686 11.8467 50.4594 12.06C50.246 12.26 50.0727 12.4933 49.9394 12.76C49.8194 13.0267 49.7594 13.32 49.7594 13.64V20H48.5394C48.2994 20 48.0727 20 47.8594 20C47.6594 20 47.4727 20 47.2994 20ZM65.3069 20.2C64.8402 20.2 64.3802 20.1333 63.9269 20C63.4869 19.8533 63.0869 19.66 62.7269 19.42C62.3669 19.18 62.0669 18.9133 61.8269 18.62C61.5869 18.3133 61.4269 18.0067 61.3469 17.7L61.9269 17.44L61.8669 19.96H59.4869V5.2H61.9469V11.9L61.5069 11.7C61.5735 11.38 61.7202 11.08 61.9469 10.8C62.1869 10.5067 62.4802 10.2467 62.8269 10.02C63.1735 9.78 63.5535 9.59333 63.9669 9.46C64.3802 9.31333 64.8002 9.24 65.2269 9.24C66.1735 9.24 67.0069 9.47333 67.7269 9.94C68.4602 10.4067 69.0335 11.0533 69.4469 11.88C69.8735 12.7067 70.0869 13.6467 70.0869 14.7C70.0869 15.7667 69.8802 16.7133 69.4669 17.54C69.0535 18.3667 68.4802 19.02 67.7469 19.5C67.0269 19.9667 66.2135 20.2 65.3069 20.2ZM64.7869 18.04C65.3469 18.04 65.8469 17.9 66.2869 17.62C66.7269 17.3267 67.0735 16.9333 67.3269 16.44C67.5802 15.9333 67.7069 15.3533 67.7069 14.7C67.7069 14.06 67.5802 13.4933 67.3269 13C67.0869 12.5067 66.7469 12.12 66.3069 11.84C65.8669 11.56 65.3602 11.42 64.7869 11.42C64.2135 11.42 63.7069 11.56 63.2669 11.84C62.8269 12.12 62.4802 12.5067 62.2269 13C61.9735 13.4933 61.8469 14.06 61.8469 14.7C61.8469 15.3533 61.9735 15.9333 62.2269 16.44C62.4802 16.9333 62.8269 17.3267 63.2669 17.62C63.7069 17.9 64.2135 18.04 64.7869 18.04ZM77.0998 20.2C76.0465 20.2 75.1065 19.9667 74.2798 19.5C73.4532 19.02 72.7998 18.3667 72.3198 17.54C71.8398 16.7133 71.5998 15.7733 71.5998 14.72C71.5998 13.6667 71.8398 12.7267 72.3198 11.9C72.7998 11.0733 73.4532 10.4267 74.2798 9.96C75.1065 9.48 76.0465 9.24 77.0998 9.24C78.1398 9.24 79.0732 9.48 79.8998 9.96C80.7265 10.4267 81.3732 11.0733 81.8398 11.9C82.3198 12.7267 82.5598 13.6667 82.5598 14.72C82.5598 15.7733 82.3198 16.7133 81.8398 17.54C81.3732 18.3667 80.7265 19.02 79.8998 19.5C79.0732 19.9667 78.1398 20.2 77.0998 20.2ZM77.0998 18.02C77.6732 18.02 78.1798 17.8733 78.6198 17.58C79.0732 17.2867 79.4265 16.8933 79.6798 16.4C79.9465 15.9067 80.0732 15.3467 80.0598 14.72C80.0732 14.08 79.9465 13.5133 79.6798 13.02C79.4265 12.5267 79.0732 12.14 78.6198 11.86C78.1798 11.5667 77.6732 11.42 77.0998 11.42C76.5265 11.42 76.0065 11.5667 75.5398 11.86C75.0865 12.14 74.7332 12.5333 74.4798 13.04C74.2265 13.5333 74.0998 14.0933 74.0998 14.72C74.0998 15.3467 74.2265 15.9067 74.4798 16.4C74.7332 16.8933 75.0865 17.2867 75.5398 17.58C76.0065 17.8733 76.5265 18.02 77.0998 18.02ZM88.7403 20.2C87.8736 20.2 87.087 19.96 86.3803 19.48C85.6736 19 85.107 18.3467 84.6803 17.52C84.2536 16.6933 84.0403 15.7533 84.0403 14.7C84.0403 13.6467 84.2536 12.7067 84.6803 11.88C85.107 11.0533 85.687 10.4067 86.4203 9.94C87.1536 9.47333 87.9803 9.24 88.9003 9.24C89.4336 9.24 89.9203 9.32 90.3603 9.48C90.8003 9.62667 91.187 9.84 91.5203 10.12C91.8536 10.4 92.127 10.72 92.3403 11.08C92.567 11.44 92.7203 11.8267 92.8003 12.24L92.2603 12.1V9.46H94.7403V20H92.2403V17.48L92.8203 17.38C92.727 17.74 92.5536 18.0933 92.3003 18.44C92.0603 18.7733 91.7536 19.0733 91.3803 19.34C91.0203 19.5933 90.6136 19.8 90.1603 19.96C89.7203 20.12 89.247 20.2 88.7403 20.2ZM89.4203 18.02C89.9936 18.02 90.5003 17.88 90.9403 17.6C91.3803 17.32 91.7203 16.9333 91.9603 16.44C92.2136 15.9333 92.3403 15.3533 92.3403 14.7C92.3403 14.06 92.2136 13.4933 91.9603 13C91.7203 12.5067 91.3803 12.12 90.9403 11.84C90.5003 11.56 89.9936 11.42 89.4203 11.42C88.847 11.42 88.3403 11.56 87.9003 11.84C87.4736 12.12 87.1403 12.5067 86.9003 13C86.6603 13.4933 86.5403 14.06 86.5403 14.7C86.5403 15.3533 86.6603 15.9333 86.9003 16.44C87.1403 16.9333 87.4736 17.32 87.9003 17.6C88.3403 17.88 88.847 18.02 89.4203 18.02ZM97.5923 20V9.46H100.012L100.052 12.82L99.7123 12.06C99.859 11.5267 100.112 11.0467 100.472 10.62C100.832 10.1933 101.246 9.86 101.712 9.62C102.192 9.36667 102.692 9.24 103.212 9.24C103.439 9.24 103.652 9.26 103.852 9.3C104.066 9.34 104.239 9.38667 104.372 9.44L103.712 12.14C103.566 12.06 103.386 11.9933 103.172 11.94C102.959 11.8867 102.746 11.86 102.532 11.86C102.199 11.86 101.879 11.9267 101.572 12.06C101.279 12.18 101.019 12.3533 100.792 12.58C100.566 12.8067 100.386 13.0733 100.252 13.38C100.132 13.6733 100.072 14.0067 100.072 14.38V20H97.5923ZM109.801 20.2C108.868 20.2 108.028 19.9667 107.281 19.5C106.548 19.02 105.961 18.3733 105.521 17.56C105.094 16.7333 104.881 15.7867 104.881 14.72C104.881 13.6667 105.094 12.7267 105.521 11.9C105.948 11.0733 106.528 10.4267 107.261 9.96C108.008 9.48 108.848 9.24 109.781 9.24C110.288 9.24 110.774 9.32 111.241 9.48C111.721 9.64 112.148 9.86 112.521 10.14C112.894 10.4067 113.188 10.7067 113.401 11.04C113.628 11.3733 113.754 11.72 113.781 12.08L113.121 12.16V5.2H115.581V20H113.201L113.141 17.52L113.621 17.56C113.608 17.8933 113.488 18.2133 113.261 18.52C113.048 18.8267 112.768 19.1067 112.421 19.36C112.074 19.6133 111.668 19.82 111.201 19.98C110.748 20.1267 110.281 20.2 109.801 20.2ZM110.281 18.12C110.854 18.12 111.361 17.9733 111.801 17.68C112.241 17.3867 112.581 16.9867 112.821 16.48C113.074 15.9733 113.201 15.3867 113.201 14.72C113.201 14.0667 113.074 13.4867 112.821 12.98C112.581 12.46 112.241 12.06 111.801 11.78C111.361 11.4867 110.854 11.34 110.281 11.34C109.708 11.34 109.201 11.4867 108.761 11.78C108.321 12.06 107.974 12.46 107.721 12.98C107.481 13.4867 107.361 14.0667 107.361 14.72C107.361 15.3867 107.481 15.9733 107.721 16.48C107.974 16.9867 108.321 17.3867 108.761 17.68C109.201 17.9733 109.708 18.12 110.281 18.12ZM118.654 20V9.46H121.114V20H118.654ZM119.854 7.22C119.36 7.22 118.974 7.1 118.694 6.86C118.427 6.60667 118.294 6.25333 118.294 5.8C118.294 5.37333 118.434 5.02667 118.714 4.76C118.994 4.49333 119.374 4.36 119.854 4.36C120.36 4.36 120.747 4.48667 121.014 4.74C121.294 4.98 121.434 5.33333 121.434 5.8C121.434 6.21333 121.294 6.55333 121.014 6.82C120.734 7.08667 120.347 7.22 119.854 7.22ZM124.233 20V9.46H126.633L126.673 11.62L126.213 11.86C126.346 11.38 126.606 10.9467 126.993 10.56C127.38 10.16 127.84 9.84 128.373 9.6C128.906 9.36 129.453 9.24 130.013 9.24C130.813 9.24 131.48 9.4 132.013 9.72C132.56 10.04 132.966 10.52 133.233 11.16C133.513 11.8 133.653 12.6 133.653 13.56V20H131.193V13.74C131.193 13.2067 131.12 12.7667 130.973 12.42C130.826 12.06 130.6 11.8 130.293 11.64C129.986 11.4667 129.613 11.3867 129.173 11.4C128.813 11.4 128.48 11.46 128.173 11.58C127.88 11.6867 127.62 11.8467 127.393 12.06C127.18 12.26 127.006 12.4933 126.873 12.76C126.753 13.0267 126.693 13.32 126.693 13.64V20H125.473C125.233 20 125.006 20 124.793 20C124.593 20 124.406 20 124.233 20ZM141.16 24.6C140.374 24.6 139.587 24.48 138.8 24.24C138.027 24 137.4 23.6867 136.92 23.3L137.8 21.48C138.08 21.68 138.394 21.8533 138.74 22C139.087 22.1467 139.454 22.26 139.84 22.34C140.24 22.42 140.634 22.46 141.02 22.46C141.727 22.46 142.314 22.3467 142.78 22.12C143.26 21.9067 143.62 21.5733 143.86 21.12C144.1 20.68 144.22 20.1267 144.22 19.46V17.52L144.58 17.66C144.5 18.0733 144.28 18.4667 143.92 18.84C143.56 19.2133 143.107 19.52 142.56 19.76C142.014 19.9867 141.454 20.1 140.88 20.1C139.88 20.1 138.994 19.8667 138.22 19.4C137.46 18.9333 136.854 18.3 136.4 17.5C135.96 16.6867 135.74 15.7533 135.74 14.7C135.74 13.6467 135.96 12.7133 136.4 11.9C136.84 11.0733 137.44 10.4267 138.2 9.96C138.974 9.48 139.84 9.24 140.8 9.24C141.2 9.24 141.587 9.28667 141.96 9.38C142.334 9.47333 142.674 9.60667 142.98 9.78C143.3 9.95333 143.587 10.1467 143.84 10.36C144.094 10.5733 144.3 10.8 144.46 11.04C144.62 11.28 144.72 11.5133 144.76 11.74L144.24 11.9L144.28 9.46H146.7V19.28C146.7 20.1333 146.574 20.8867 146.32 21.54C146.067 22.1933 145.7 22.7467 145.22 23.2C144.74 23.6667 144.16 24.0133 143.48 24.24C142.8 24.48 142.027 24.6 141.16 24.6ZM141.26 18C141.86 18 142.387 17.86 142.84 17.58C143.307 17.3 143.667 16.9133 143.92 16.42C144.174 15.9267 144.3 15.36 144.3 14.72C144.3 14.0667 144.167 13.4933 143.9 13C143.647 12.4933 143.294 12.1 142.84 11.82C142.387 11.54 141.86 11.4 141.26 11.4C140.66 11.4 140.134 11.5467 139.68 11.84C139.227 12.12 138.867 12.5133 138.6 13.02C138.347 13.5133 138.22 14.08 138.22 14.72C138.22 15.3467 138.347 15.9133 138.6 16.42C138.867 16.9133 139.227 17.3 139.68 17.58C140.134 17.86 140.66 18 141.26 18Z" fill="#292A2B" />
        </svg>
    </span>
</div>
<div class="Body">
    <div class="DomainInfo">
        <div class="DomainName">
            <span class="DomainNameHeadertext">Domain Name</span> <br />
            <div class="DomainNameHeaderinput">
                <input type="text" placeholder="TenantId" @bind-value="Model.Id" />
                <span>.leadrat.com</span>
            </div>
        </div>
        <div class="DomainName">
            <span class="DomainNameHeadertext">Tenant Origin</span> <br />
            <div class="DomainNameHeaderinput">
                <select @onchange="OnCountryChanged">
                    @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.TenantCountryType)))
                    {
                        <option value="@item">@item</option>
                    }
                </select>
            </div>
        </div>
    </div>
    <div class="@(IsTenantOriginAvailable ? "":"disabled")"
        <div class="OrganizationInfo">
            <span class="OrganizationInfoHeaderText">Organization Info</span> <br />
            <div class="form-container">
                <div class="form-group">
                    <label for="companyName">Company Name</label>
                    <input type="text" id="companyName" placeholder="Ex: Leadrat" @bind-value="Model.Name">
                </div>
                <div class="form-group">
                    <label for="gstNumber">GST Number</label>
                    <input type="text" id="gstNumber" placeholder="Ex: 24ABCDE1234F1Z5" @bind-value="GSTNumber">
                </div>@*
                <div class="form-group">
                <label for="registeredName">Registered Name</label>
                <input type="text" id="registeredName" placeholder="Ex: Dhinwa Solutions" @bind-value="tenantsInfo.RegisteredName">
                </div>
                <div class="form-group">
                <label for="registeredAddress">Registered Address</label>
                <input type="text" id="registeredAddress" placeholder="Ex: 1596, level, 3, 20th Main Rd, Agara Village, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102" @bind-value="tenantsInfo.RegisteredAddress">
                </div> *@
                <div class="form-group">
                    <label for="contactNumber">Company Contact Number</label>
                    <input type="text" id="contactNumber" placeholder="Ex: +919740730358" @bind-value="Model.OrganizationInfo.PhoneNumber">
                </div>
                <div class="form-group">
                    <label for="emailId">Company Email ID</label>
                    <input type="email" id="emailId" placeholder="Ex: <EMAIL>" @bind-value="Model.AdminEmail">
                </div>
                <div class="form-group">
                    <label for="companyAddress">Company Address</label>
                    <input type="text" id="companyAddress" placeholder="Ex: 1596, level, 3, 20th Main Rd, Agara Village, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102" @bind-value="Model.OrganizationInfo.Address.SubLocality" />
                </div>
                <div class="form-group">
                    <label for="city">City</label>
                    <input type="text" id="city" placeholder="Ex: Bengaluru" @bind-value="Model.OrganizationInfo.Address.City">
                </div>
                <div class="form-group">
                    <label for="state">State</label>
                    <input type="text" id="state" placeholder="Ex: Karnataka" @bind-value="Model.OrganizationInfo.Address.State">
                </div>
                <div class="form-group">
                    <label for="Country">Country</label>
                    <input type="text" id="Country" placeholder="Ex: Karnataka" @bind-value="Model.OrganizationInfo.Address.Country">
                </div>
                <div class="form-group">
                    <label for="pinCode">PIN Code</label>
                    <input type="text" id="pinCode" placeholder="Ex: 560102" @bind-value="Model.OrganizationInfo.Address.PostalCode">
                </div>
                <div class="form-group">
                    <label for="reraNumber">RERA Number</label>
                    <input type="text" id="reraNumber" placeholder="Ex: MAHARERA/P52000012345/2023" @bind-value="Model.OrganizationInfo.RERANumber">
                </div>
            </div>
        </div>

        <div class="AdminInfo">
            <span class="OrganizationInfoHeaderText">Admin Info</span> <br />
            <div class="form-container">
                <div class="form-group">
                    <label for="companyName">Admin First Name</label>
                    <input type="text" id="companyName" placeholder="Ex: Purvesh" @bind-value="Model.AdminUser.FirstName">
                </div>
                <div class="form-group">
                    <label for="gstNumber">Admin Last Name</label>
                    <input type="text" id="gstNumber" placeholder="Ex: Kotecha" @bind-value="Model.AdminUser.LastName">
                </div>
                <div class="form-group">
                    <label for="registeredName">Admin Contact no.</label>
                    <input type="text" id="registeredName" placeholder="Ex: +919426780095" @bind-value="Model.AdminUser.PhoneNumber">
                </div>
                <div class="form-group">
                    <label for="registeredAddress">Admin EmailId</label>
                    <input type="email" id="registeredAddress" placeholder="Ex: <EMAIL>" @bind-value="Model.AdminUser.Email">
                </div>
                <div class="form-group">
                    <label for="contactNumber">Admin User Name</label>
                    <input type="text" id="contactNumber" placeholder="Ex: purvesh@leadrat" @bind-value="Model.AdminUser.UserName">
                </div>
            </div>
        </div>

        <div class="PlanFinance">
            <span class="OrganizationInfoHeaderText">Plan & Finance</span> <br />
            <div class="form-container">
                <div class="form-group">
                    <label for="NoOfLicence">No. of Sold Licences</label>
                    <input type="number" id="NoOfLicence" placeholder="Ex: 100"
                           @bind="SubscriptionsModel.SoldLicences" @bind:event="oninput" />
                </div>

                <div class="form-group">
                    <label for="BillingType">Billing Type</label>
                    <select id="BillingType" @bind="SubscriptionsModel.BillingType" @bind:event="onchange">
                        @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.BillingType)))
                        {
                            <option value="@item">@item</option>
                        }
                    </select>
                </div>

                <div class="form-group rupeessymbol">
                    <span>₹</span>
                    <label for="perUserMonth" class="perusermonth">
                        Price User/Month
                        <input type="checkbox" id="includeGST" @bind="SubscriptionsModel.GstInclude" @bind:event="onchange" />
                    </label>
                    <input type="number" id="perUserMonth" placeholder="0"
                           @bind="SubscriptionsModel.PerUserCost" @bind:event="oninput" />
                </div>




                <div class="form-group">
                    <label for="gstNumber">Payment Date</label>
                    <input type="date" id="PaymentDate" @bind-value="SubscriptionsModel.PaymentDate">
                </div>
                <div class="form-group">
                    <label for="gstNumber">Payment Mode</label>
                    <select id="PaymentMode" @bind="SubscriptionsModel.PaymentMode">
                        @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.PaymentMode)))
                        {
                            <option value="@item">@item</option>
                        }
                    </select>
                </div>
                <div class="form-group rupeessymbol">
                    <label for="planNetAmount">Plan Net Amount</label>
                    <span>₹</span>
                    <input type="number" id="planNetAmount" placeholder="Ex: 100000"
                           value="@SubscriptionsModel.SoldPrice" disabled="true">
                </div>

                <div class="form-group rupeessymbol">
                    <label for="planGstAmount">Plan Gst Amount</label>
                    <span>₹</span>
                    <input type="number" id="planGstAmount" placeholder="Ex: 18000"
                           value="@SubscriptionsModel.GstAmount" disabled="true">
                </div>

                <div class="form-group rupeessymbol">
                    <label for="PlanTotalAmount">Plan Total Amount</label>
                    <span>₹</span>
                    <input type="number" id="PlanTotalAmount" placeholder="Ex: 118000"
                           value="@SubscriptionsModel.TotalAmount" disabled="true">
                </div>
                <div class="form-group">
                    <label for="contactNumber">Payment Type</label>
                    <select id="PaymentType" @bind="SubscriptionsModel.Type">
                        @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.PaymentType)))
                        {
                            <option value="@item">@item</option>
                        }
                    </select>
                </div>
                @if (SubscriptionsModel.Type == Lrb.Admin.Data.Enums.PaymentType.Installment)
                {
                    <div class="form-group rupeessymbol">
                        <label for="contactNumber">Paid Net Amount</label>
                        <span>₹</span>
                        <input type="number" id="PaidNetAmount" placeholder="Ex: 50000" @bind-value="SubscriptionsModel.PaidNetAmount">
                    </div>
                    <div class="form-group rupeessymbol">
                        <label for="contactNumber">Paid GST Amount</label>
                        <span>₹</span>
                        <input type="number" id="PaidGstAmount" placeholder="Ex: 9000" @bind-value="SubscriptionsModel.PaidGSTAmount">
                    </div>
                    <div class="form-group rupeessymbol">
                        <label for="contactNumber">Paid Total Amount</label>
                        <span>₹</span>
                        <input type="number" id="PaidTotalAmount" placeholder="Ex: 59000" @bind-value="SubscriptionsModel.PaidTotalAmount">
                    </div>
                    <div class="form-group rupeessymbol">
                        <label for="contactNumber">Total Pending Amount</label>
                        <span>₹</span>
                        <input type="number" id="TotalPendingAmount" placeholder="Ex: 59000" @bind-value="SubscriptionsModel.PendingAmount">
                    </div>
                    if (SubscriptionsModel.PendingAmount != 0)
                    {
                        <div class="form-group">
                            <label for="payment-date">Next Due Date</label>
                            <input type="date" id="NextDueDate" @bind-value="SubscriptionsModel.NextDueDate">
                        </div>
                    }
                }

                <div class="form-group">
                    <label for="contactNumber">TDS</label>
                    <select id="TDSOption" @bind="IsTDSApplicable">
                        <option value="NotApplicable"> Not Applicable </option>
                        <option value="Applicable">Applicable</option>
                    </select>
                </div>
                @if (IsTDSApplicable == "Applicable")
                {
                    <div class="form-group percentagesymbol">
                        <label for="contactNumber">TDS%</label>
                        <span>%</span>
                        <input type="number" id="TDSPercentage" placeholder="Ex: 5" @bind-value="TDSNumber">
                    </div>
                    @if (SubscriptionsModel.Type == Lrb.Admin.Data.Enums.PaymentType.Installment)
                    {
                        <div class="form-group rupeessymbol">
                            <label for="tdsAmount">TDS Amount</label>
                            <span>₹</span>
                            <input type="number" id="tdsAmount"
                                   value="@(((TDSNumber / 100) * SubscriptionsModel.PaidNetAmount)?.ToString("F2"))"
                                   disabled />
                        </div>

                    }
                    else
                    {
                        <div class="form-group rupeessymbol">
                            <label for="tdsAmount">TDS Amount</label>
                            <span>₹</span>
                            <input type="number" id="tdsAmount"
                                   value="@(((TDSNumber / 100) * SubscriptionsModel.SoldPrice)?.ToString("F2"))"
                                   disabled />
                        </div>
                    }
                    <div class="form-group">
                        <label for="contactNumber">TDS File</label>
                        <InputFile id="TDS_File" OnChange="@(e => TDSFileUpload(e))" />
                    </div>
                }
            </div>
        </div>

        <div class="Miscellaneous">
            <span class="OrganizationInfoHeaderText">Miscellaneous</span> <br />
            <div class="form-container">
                <div class="form-group">
                    <label for="companyName">Notes/Comments</label>
                    <input type="text" id="Notes" placeholder="Ex: Notes" @bind-value="SubscriptionsModel.Description">
                </div>
                <div class="form-group">
                    <label for="gstNumber">Vendors</label>
                    <select id="Vendors" @onchange="AddMultipleVendors">
                        <option value="Select">Select</option>
                        @foreach (var vendorName in VendorNames)
                        {
                            <option value="@vendorName">@vendorName</option>
                        }
                    </select>
                </div>
                <div>
                    <span class="vendor-list">
                        @foreach (var vendorName in Vendors)
                        {
                            <button class="vendor-button">
                                @vendorName
                                <span @onclick="() => RemoveVendor(vendorName)" class="remove-icon">
                                    &times;
                                </span>
                            </button>
                        }
                    </span>
                </div>


            </div>
        </div>
    </div>
</div>

<div class="footer @(IsTenantOriginAvailable ? "":"disabled")">

    <span @onclick="@Reset">
        <svg width="45" height="34" viewBox="0 0 45 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.128 22V13.6H10.776C11.28 13.6 11.74 13.72 12.156 13.96C12.572 14.192 12.9 14.512 13.14 14.92C13.388 15.32 13.512 15.772 13.512 16.276C13.512 16.756 13.388 17.2 13.14 17.608C12.9 18.008 12.572 18.328 12.156 18.568C11.748 18.8 11.288 18.916 10.776 18.916H8.652V22H7.128ZM12 22L9.864 18.208L11.472 17.908L13.848 22.012L12 22ZM8.652 17.56H10.788C11.02 17.56 11.22 17.508 11.388 17.404C11.564 17.292 11.7 17.14 11.796 16.948C11.892 16.756 11.94 16.544 11.94 16.312C11.94 16.048 11.88 15.82 11.76 15.628C11.64 15.436 11.472 15.284 11.256 15.172C11.04 15.06 10.792 15.004 10.512 15.004H8.652V17.56ZM17.9608 22.12C17.2968 22.12 16.7088 21.98 16.1968 21.7C15.6928 21.42 15.2968 21.04 15.0088 20.56C14.7288 20.072 14.5888 19.516 14.5888 18.892C14.5888 18.396 14.6688 17.944 14.8288 17.536C14.9888 17.128 15.2088 16.776 15.4888 16.48C15.7768 16.176 16.1168 15.944 16.5088 15.784C16.9088 15.616 17.3448 15.532 17.8168 15.532C18.2328 15.532 18.6208 15.612 18.9808 15.772C19.3408 15.932 19.6528 16.152 19.9168 16.432C20.1808 16.704 20.3808 17.032 20.5168 17.416C20.6608 17.792 20.7288 18.204 20.7208 18.652L20.7088 19.168H15.5848L15.3088 18.16H19.4728L19.2808 18.364V18.1C19.2568 17.852 19.1768 17.636 19.0408 17.452C18.9048 17.26 18.7288 17.112 18.5128 17.008C18.3048 16.896 18.0728 16.84 17.8168 16.84C17.4248 16.84 17.0928 16.916 16.8208 17.068C16.5568 17.22 16.3568 17.44 16.2208 17.728C16.0848 18.008 16.0168 18.36 16.0168 18.784C16.0168 19.192 16.1008 19.548 16.2688 19.852C16.4448 20.156 16.6888 20.392 17.0008 20.56C17.3208 20.72 17.6928 20.8 18.1168 20.8C18.4128 20.8 18.6848 20.752 18.9328 20.656C19.1808 20.56 19.4488 20.388 19.7368 20.14L20.4688 21.16C20.2528 21.36 20.0048 21.532 19.7248 21.676C19.4528 21.812 19.1648 21.92 18.8608 22C18.5568 22.08 18.2568 22.12 17.9608 22.12ZM24.1401 22.12C23.5801 22.12 23.0761 22.028 22.6281 21.844C22.1881 21.652 21.8281 21.38 21.5481 21.028L22.5081 20.2C22.7481 20.472 23.0161 20.668 23.3121 20.788C23.6081 20.908 23.9201 20.968 24.2481 20.968C24.3841 20.968 24.5041 20.952 24.6081 20.92C24.7201 20.888 24.8161 20.84 24.8961 20.776C24.9761 20.712 25.0361 20.64 25.0761 20.56C25.1241 20.472 25.1481 20.376 25.1481 20.272C25.1481 20.08 25.0761 19.928 24.9321 19.816C24.8521 19.76 24.7241 19.7 24.5481 19.636C24.3801 19.572 24.1601 19.508 23.8881 19.444C23.4561 19.332 23.0961 19.204 22.8081 19.06C22.5201 18.908 22.2961 18.74 22.1361 18.556C22.0001 18.404 21.8961 18.232 21.8241 18.04C21.7601 17.848 21.7281 17.64 21.7281 17.416C21.7281 17.136 21.7881 16.884 21.9081 16.66C22.0361 16.428 22.2081 16.228 22.4241 16.06C22.6401 15.892 22.8921 15.764 23.1801 15.676C23.4681 15.588 23.7681 15.544 24.0801 15.544C24.4001 15.544 24.7081 15.584 25.0041 15.664C25.3081 15.744 25.5881 15.86 25.8441 16.012C26.1081 16.156 26.3321 16.332 26.5161 16.54L25.7001 17.44C25.5481 17.296 25.3801 17.168 25.1961 17.056C25.0201 16.944 24.8401 16.856 24.6561 16.792C24.4721 16.72 24.3001 16.684 24.1401 16.684C23.9881 16.684 23.8521 16.7 23.7321 16.732C23.6121 16.756 23.5121 16.796 23.4321 16.852C23.3521 16.908 23.2881 16.98 23.2401 17.068C23.2001 17.148 23.1801 17.244 23.1801 17.356C23.1881 17.452 23.2121 17.544 23.2521 17.632C23.3001 17.712 23.3641 17.78 23.4441 17.836C23.5321 17.892 23.6641 17.956 23.8401 18.028C24.0161 18.1 24.2441 18.168 24.5241 18.232C24.9161 18.336 25.2441 18.452 25.5081 18.58C25.7721 18.708 25.9801 18.856 26.1321 19.024C26.2841 19.168 26.3921 19.336 26.4561 19.528C26.5201 19.72 26.5521 19.932 26.5521 20.164C26.5521 20.54 26.4441 20.876 26.2281 21.172C26.0201 21.468 25.7321 21.7 25.3641 21.868C25.0041 22.036 24.5961 22.12 24.1401 22.12ZM30.8163 22.12C30.1523 22.12 29.5643 21.98 29.0523 21.7C28.5483 21.42 28.1523 21.04 27.8643 20.56C27.5843 20.072 27.4443 19.516 27.4443 18.892C27.4443 18.396 27.5243 17.944 27.6843 17.536C27.8443 17.128 28.0643 16.776 28.3443 16.48C28.6323 16.176 28.9723 15.944 29.3643 15.784C29.7643 15.616 30.2003 15.532 30.6723 15.532C31.0883 15.532 31.4763 15.612 31.8363 15.772C32.1963 15.932 32.5083 16.152 32.7723 16.432C33.0363 16.704 33.2363 17.032 33.3723 17.416C33.5163 17.792 33.5843 18.204 33.5763 18.652L33.5643 19.168H28.4403L28.1643 18.16H32.3283L32.1363 18.364V18.1C32.1123 17.852 32.0323 17.636 31.8963 17.452C31.7603 17.26 31.5843 17.112 31.3683 17.008C31.1603 16.896 30.9283 16.84 30.6723 16.84C30.2803 16.84 29.9483 16.916 29.6763 17.068C29.4123 17.22 29.2123 17.44 29.0763 17.728C28.9403 18.008 28.8723 18.36 28.8723 18.784C28.8723 19.192 28.9563 19.548 29.1243 19.852C29.3003 20.156 29.5443 20.392 29.8563 20.56C30.1763 20.72 30.5483 20.8 30.9723 20.8C31.2683 20.8 31.5403 20.752 31.7883 20.656C32.0363 20.56 32.3043 20.388 32.5923 20.14L33.3243 21.16C33.1083 21.36 32.8603 21.532 32.5803 21.676C32.3083 21.812 32.0203 21.92 31.7163 22C31.4123 22.08 31.1123 22.12 30.8163 22.12ZM35.5675 22V14.068H37.0435V22H35.5675ZM34.3195 17.044V15.676H38.4235V17.044H34.3195Z" fill="#494F56" />
            <path d="M6 23.5H38.6875V24.1H6V23.5Z" fill="#494F56" />
        </svg>
    </span>
    <svg width="2" height="16" viewBox="0 0 2 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1V15" stroke="#DDDDDD" stroke-linecap="round" />
    </svg>

    <span @onclick="@AddTenantAsync">
        <svg width="87" height="34" viewBox="0 0 87 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="87" height="34" rx="4" fill="#121212" />
            <path d="M25.792 22.12C25.304 22.12 24.856 22.06 24.448 21.94C24.04 21.812 23.672 21.624 23.344 21.376C23.016 21.128 22.724 20.828 22.468 20.476L23.488 19.324C23.88 19.868 24.264 20.244 24.64 20.452C25.016 20.66 25.44 20.764 25.912 20.764C26.184 20.764 26.432 20.724 26.656 20.644C26.88 20.556 27.056 20.44 27.184 20.296C27.312 20.144 27.376 19.972 27.376 19.78C27.376 19.644 27.348 19.52 27.292 19.408C27.244 19.288 27.168 19.184 27.064 19.096C26.96 19 26.832 18.912 26.68 18.832C26.528 18.752 26.356 18.684 26.164 18.628C25.972 18.572 25.76 18.52 25.528 18.472C25.088 18.384 24.704 18.268 24.376 18.124C24.048 17.972 23.772 17.788 23.548 17.572C23.324 17.348 23.16 17.1 23.056 16.828C22.952 16.548 22.9 16.232 22.9 15.88C22.9 15.528 22.976 15.204 23.128 14.908C23.288 14.612 23.504 14.356 23.776 14.14C24.048 13.924 24.364 13.756 24.724 13.636C25.084 13.516 25.472 13.456 25.888 13.456C26.36 13.456 26.78 13.512 27.148 13.624C27.524 13.736 27.852 13.904 28.132 14.128C28.42 14.344 28.656 14.608 28.84 14.92L27.808 15.94C27.648 15.692 27.468 15.488 27.268 15.328C27.068 15.16 26.852 15.036 26.62 14.956C26.388 14.868 26.144 14.824 25.888 14.824C25.6 14.824 25.348 14.864 25.132 14.944C24.924 15.024 24.76 15.14 24.64 15.292C24.52 15.436 24.46 15.612 24.46 15.82C24.46 15.98 24.496 16.124 24.568 16.252C24.64 16.372 24.74 16.48 24.868 16.576C25.004 16.672 25.176 16.756 25.384 16.828C25.592 16.9 25.828 16.964 26.092 17.02C26.532 17.108 26.928 17.228 27.28 17.38C27.632 17.524 27.932 17.7 28.18 17.908C28.428 18.108 28.616 18.34 28.744 18.604C28.872 18.86 28.936 19.148 28.936 19.468C28.936 20.02 28.804 20.496 28.54 20.896C28.284 21.288 27.92 21.592 27.448 21.808C26.976 22.016 26.424 22.12 25.792 22.12ZM32.5834 22.12C32.1274 22.12 31.7314 22.02 31.3954 21.82C31.0594 21.612 30.8034 21.32 30.6274 20.944C30.4514 20.56 30.3634 20.104 30.3634 19.576V15.676H31.8394V19.264C31.8394 19.6 31.8914 19.888 31.9954 20.128C32.0994 20.36 32.2514 20.54 32.4514 20.668C32.6514 20.796 32.8914 20.86 33.1714 20.86C33.3794 20.86 33.5674 20.828 33.7354 20.764C33.9114 20.692 34.0634 20.596 34.1914 20.476C34.3194 20.348 34.4194 20.2 34.4914 20.032C34.5634 19.856 34.5994 19.668 34.5994 19.468V15.676H36.0754V22H34.6354L34.6114 20.68L34.8754 20.536C34.7794 20.84 34.6154 21.112 34.3834 21.352C34.1594 21.584 33.8914 21.772 33.5794 21.916C33.2674 22.052 32.9354 22.12 32.5834 22.12ZM41.2974 22.12C41.0174 22.12 40.7414 22.08 40.4694 22C40.2054 21.912 39.9654 21.796 39.7494 21.652C39.5334 21.508 39.3534 21.348 39.2094 21.172C39.0654 20.988 38.9694 20.804 38.9214 20.62L39.2694 20.464L39.2334 21.976H37.8054V13.12H39.2814V17.14L39.0174 17.02C39.0574 16.828 39.1454 16.648 39.2814 16.48C39.4254 16.304 39.6014 16.148 39.8094 16.012C40.0174 15.868 40.2454 15.756 40.4934 15.676C40.7414 15.588 40.9934 15.544 41.2494 15.544C41.8174 15.544 42.3174 15.684 42.7494 15.964C43.1894 16.244 43.5334 16.632 43.7814 17.128C44.0374 17.624 44.1654 18.188 44.1654 18.82C44.1654 19.46 44.0414 20.028 43.7934 20.524C43.5454 21.02 43.2014 21.412 42.7614 21.7C42.3294 21.98 41.8414 22.12 41.2974 22.12ZM40.9854 20.824C41.3214 20.824 41.6214 20.74 41.8854 20.572C42.1494 20.396 42.3574 20.16 42.5094 19.864C42.6614 19.56 42.7374 19.212 42.7374 18.82C42.7374 18.436 42.6614 18.096 42.5094 17.8C42.3654 17.504 42.1614 17.272 41.8974 17.104C41.6334 16.936 41.3294 16.852 40.9854 16.852C40.6414 16.852 40.3374 16.936 40.0734 17.104C39.8094 17.272 39.6014 17.504 39.4494 17.8C39.2974 18.096 39.2214 18.436 39.2214 18.82C39.2214 19.212 39.2974 19.56 39.4494 19.864C39.6014 20.16 39.8094 20.396 40.0734 20.572C40.3374 20.74 40.6414 20.824 40.9854 20.824ZM45.4572 22V15.676H46.9092L46.9332 16.948L46.6932 17.044C46.7652 16.836 46.8732 16.644 47.0172 16.468C47.1612 16.284 47.3332 16.128 47.5332 16C47.7332 15.864 47.9452 15.76 48.1692 15.688C48.3932 15.608 48.6212 15.568 48.8532 15.568C49.1972 15.568 49.5012 15.624 49.7652 15.736C50.0372 15.84 50.2612 16.008 50.4372 16.24C50.6212 16.472 50.7572 16.768 50.8452 17.128L50.6172 17.08L50.7132 16.888C50.8012 16.696 50.9252 16.524 51.0852 16.372C51.2452 16.212 51.4252 16.072 51.6252 15.952C51.8252 15.824 52.0332 15.728 52.2492 15.664C52.4732 15.6 52.6932 15.568 52.9092 15.568C53.3892 15.568 53.7892 15.664 54.1092 15.856C54.4292 16.048 54.6692 16.34 54.8292 16.732C54.9892 17.124 55.0692 17.608 55.0692 18.184V22H53.5812V18.28C53.5812 17.96 53.5372 17.696 53.4492 17.488C53.3692 17.28 53.2412 17.128 53.0652 17.032C52.8972 16.928 52.6852 16.876 52.4292 16.876C52.2292 16.876 52.0372 16.912 51.8532 16.984C51.6772 17.048 51.5252 17.144 51.3972 17.272C51.2692 17.392 51.1692 17.532 51.0972 17.692C51.0252 17.852 50.9892 18.028 50.9892 18.22V22H49.5012V18.268C49.5012 17.964 49.4572 17.712 49.3692 17.512C49.2812 17.304 49.1532 17.148 48.9852 17.044C48.8172 16.932 48.6132 16.876 48.3732 16.876C48.1732 16.876 47.9852 16.912 47.8092 16.984C47.6332 17.048 47.4812 17.14 47.3532 17.26C47.2252 17.38 47.1252 17.52 47.0532 17.68C46.9812 17.84 46.9452 18.016 46.9452 18.208V22H45.4572ZM56.8758 22V15.676H58.3518V22H56.8758ZM57.5958 14.332C57.2998 14.332 57.0678 14.26 56.8998 14.116C56.7398 13.964 56.6598 13.752 56.6598 13.48C56.6598 13.224 56.7438 13.016 56.9118 12.856C57.0798 12.696 57.3078 12.616 57.5958 12.616C57.8998 12.616 58.1318 12.692 58.2918 12.844C58.4598 12.988 58.5438 13.2 58.5438 13.48C58.5438 13.728 58.4598 13.932 58.2918 14.092C58.1238 14.252 57.8918 14.332 57.5958 14.332ZM60.9074 22V14.068H62.3834V22H60.9074ZM59.6594 17.044V15.676H63.7634V17.044H59.6594Z" fill="white" />
        </svg>
    </span>
</div>

