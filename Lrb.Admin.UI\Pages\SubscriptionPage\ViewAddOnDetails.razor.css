﻿* {
    font-family: 'Lexend Deca', sans-serif;
}

.ActiveInactive-button {
    color: black;
    border-block-start-color: black;
    border-block-end-color: black;
    background-color: white;
    border-radius: 20px;
}

.Isdeleted{
    color:red;
}

.AddonButton {
    margin-left: 88%;
    margin-bottom: 1%;
    cursor: pointer;
}


.all-tables {
    padding-left: 15px;
    overflow-y: auto;
    white-space: nowrap;
}

    .all-tables table {
        width: 100%;
        border-collapse: collapse;
    }

    .all-tables th {
        font-weight: 600 !important;
        background-color: #f2f2f2;
        font-size: 12px;
    }

    .all-tables th, .all-tables td {
        padding: 10px 50px 10px 20px;
        font-weight: 400;
        font-size: 12px;
        text-align: left;
    }

    .all-tables td {
        border-bottom: 1px solid #ddd;
    }

@media only screen and (max-width: 700px){
    .AddonButton {
        margin-left: 60%;
        margin-bottom: 1%;
        cursor: pointer;
    }
}