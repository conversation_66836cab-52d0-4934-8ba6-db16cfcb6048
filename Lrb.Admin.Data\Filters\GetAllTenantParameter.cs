﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Filters 
{
    public class GetAllTenantParameter : RequestParameter
    {
        public string? TenantId { get; set; }
        public string? Search { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? NoOfUsers { get; set; }
        public int? NoOfProperties { get; set; }
        public int? NoOfAssignedLeads { get; set; }
        public int? NoOfUnassignedLeads { get; set; }
        public string? City { get; set; }
        public bool? IsTestingAccount { get; set; }
        public bool? IntegrationEnabled { get; set; }
        public bool? IVREnabled { get; set; }
        public bool IsDecending { get; set; }
        public string? Sort { get; set; }
        public int? NoOfAdmin { get; set; }
        public string? Name { get; set; }
        public bool? Status { get; set; }
        public string? Source { get; set; }
        public BillingType? BillingType { get; set; }
        public int? LicenseMonitor { get; set; }
        public DateTime? LicenseFromDate { get; set; }
        public DateTime? LicenseToDate { get; set;}
        public DateTime? PaymentFromDate { get; set; }
        public DateTime? PaymentToDate { get; set; }
        public bool? IsPaid {  get; set; }
        public string? IsDormented {  get; set; }
        public bool? CreatedOnSorting { get; set; }



        /* public GetAllTenantParameter(DateTime? fromDate, DateTime? toDate, int noOfUsers, int propertyCount, int assignedLeads, int unassignedLeads,
             string city, bool gharofficeTenant, bool integrationEnabled, bool ivrEnabled, string tenantId)
         {
             FromDate = fromDate;
             if (toDate != null)
                 ToDate = toDate;
             else
                 ToDate = DateTime.Now;
             NoOfUsers = noOfUsers;
             NoOfProperties = propertyCount;
             NoOfAssignedLeads = assignedLeads;
             NoOfUnassignedLeads = unassignedLeads;
             City = city;
             IsTestingAccount = gharofficeTenant;
             IntegrationEnabled = integrationEnabled;
             IVREnabled = ivrEnabled;
            */ /*TenantId = tenantId;
         }*/
    }

    public enum DateType
    {
        //Select = 0,
        OnBoardDate=0,
        LicenseDate,
        PaymentDate
    }

}
