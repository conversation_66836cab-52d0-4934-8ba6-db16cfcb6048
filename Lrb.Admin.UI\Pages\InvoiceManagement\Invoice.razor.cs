﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Pages.Login;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Radzen.Blazor;
using Radzen.Blazor.Rendering;
using System.Collections.ObjectModel;
using System.Text;
using System.Security.Cryptography;
using Org.BouncyCastle.Security;
using System.Net.Http.Headers;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.Forms;
using Azure;
using Lrb.Admin.UI.Pages.Tenant;
using DocumentFormat.OpenXml.EMMA;
using Blazored.Modal;
using Blazored.Modal.Services;
using DocumentFormat.OpenXml.Wordprocessing;
using iText.Forms.Fields;
using iText.Forms;
using iText.IO.Font.Constants;
using iText.Kernel.Font;
using iText.Kernel.Pdf;
using Lrb.Admin.Data.Models.Common;
using Microsoft.AspNetCore.Authorization;


namespace Lrb.Admin.UI.Pages.InvoiceManagement
{
    [Authorize(Roles = "Admin, Manager")]
    public partial class Invoice
    {
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public IModalService Modal { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        //[Inject]
        //public IVendorService VendorService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public SpinnerService SpinnerService { get; set; }
        public InvoiceFilter Filter { get; set; } = new();
        public int PageSize { get; set; } = 10;
        public int PageNumber { get; set; } = 1;
        public double MaxPageNumber { get; set; }
        public int? TotalCount { get; set; }
        //public int UserLimit { get; set; }

        public List<int> Count = new() { 10, 20, 30, 40, 50, 100 };
        //public ObservableCollection<GetTenantModel> Cities { get; set; } = new();
        public int ShowingCount { get; set; }
        public ObservableCollection<InvoiceDto> Collection { get; set; } = new();
        //public ObservableCollection<InvoiceDto> InvoiceCollection { get; set; } = new();
        public List<GetTenantModel> List { get; set; } = new();
        public List<string> LicenseMonitor { get; set; } = new() { "Licenses > Users", "Licenses < Users", "Licenses = Users" };
        public string? Token { get; set; }
        public LogoutButton ForceLogout { get; set; } = new();
        public RadzenButton? button { get; set; }
        public Popup? popup { get; set; }
        public List<int> DropdownValues { get; set; } = new List<int> { 10, 25, 50, 100 };
        public string? NewInvoiceNumber { get; set; }
        public string? OldInvoiceNumber { get; set; }

        public float? NewTDS { get; set; }
        //public UpdateInvoiceDto? editingInvoice { get; set; }
        //private ElementReference fileInput;
        private bool isEditing = false;
        private InvoiceDto? editingItem;
        private InvoiceDto? editingItemtrdate;
        private InvoiceDto? editingInvoiceTDS;
        List<string> fileData = new List<string>();
        private InputFile? fileInput;
        private List<string> InvoiceNo = new List<string>();


        protected override async Task OnInitializedAsync()
        {
            try
            {
                await GetInvoiceAsync();
                InvoiceNo = (List<string>)await TenantService.GetInvoiceNo();
            }
            catch (Exception ex)
            {

            }
        }
        private async Task HandleKeyDownSearch(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                await GetInvoiceAsync();
            }
        }

        public async Task OpenFilter()
        {
            var parameter = new ModalParameters
            {
                {nameof(InvoiceFilterOptions.PageSize),PageSize },
                {nameof(InvoiceFilterOptions.PageNumber),PageNumber },
                {nameof(InvoiceFilterOptions.Filter),Filter },
                {nameof(InvoiceFilterOptions.Count),Count },
                {nameof(InvoiceFilterOptions.GetInvoiceAsync),GetInvoiceAsync}
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var model = Modal.Show<InvoiceFilterOptions>("Invoice Filter", parameter, options);
        }

        public async Task GetInvoiceAsync()
        {
            try
            {
                if (PageSize > Filter.PageSize)
                {
                    PageNumber = 1;
                }
                await GetTrueFilter();
                PagedResponse<InvoiceDto, string> response = null;
                if (Filter == null)
                {
                    TotalCount = 0;
                }
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await TenantService.GetInvoiceDetails(Filter);
                });
                while (!fetchTask.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<InvoiceDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = Collection.Count;
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    if (userActivityHistory == null)
                    {
                        await ForceLogout.LogoutTenant();
                    }
                    StateHasChanged();
                    AdminUserActivity userActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = userActivityHistory.TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.InvoiceProfile,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(userActivity);
                    //await popup.CloseAsync();

                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }


            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        #region InvoiceNoEdit

        private async void StartEditingInvoiceNo(InvoiceDto item)
        {

            editingItem = item;
            OldInvoiceNumber = item.InvoiceNumber;
        }
        //private void StopEditingInvoiceNo()
        //{
        //    editingItem = null;
        //    GetInvoiceAsync();
        //}



        //private void HandleInvoiceChange(ChangeEventArgs e)
        //{
        //    if (editingItem != null)
        //    {
        //        NewInvoiceNumber = e.Value.ToString();
        //        //editingItem.InvoiceNumber = NewInvoiceNumber;
        //        UpdateInvoiceNo();
        //    }
        //}



        public async Task UpdateInvoiceNo(string NInvoiceNo)
        {
            try
            {
                var response = await TenantService.UpdateInvoiceNo(OldInvoiceNumber, NInvoiceNo, editingItem.PaymentId);
                if (response)
                {
                    GetInvoiceAsync();
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        #endregion

        #region TransectionDateEdit
        private async Task EditDate(InvoiceDto model)
        {
            editingItemtrdate = model;
            //EditExpiryDate = true;
        }

        private async Task EditTransactionDateAsync(InvoiceDto model)
        {
            var response = await TenantService.UpdateTransactionDate(model);
            if (response)
            {
                await JsRuntime.InvokeVoidAsync("alert", "Updated Successfully");
            }
            //EditExpiryDate = false;
            editingItemtrdate = null;
        }


        #endregion

        #region EditTDS

        private void StartEditingTDS(InvoiceDto item)
        {
            editingInvoiceTDS = item;
            NewTDS = item.TDSDeductions;
        }
        private void StopEditingTDS()
        {
            editingInvoiceTDS = null;
            GetInvoiceAsync();
        }

        private void HandleKeyDownDownTDS(KeyboardEventArgs e)
        {
            if (e.Key == "Enter")
            {
                if (editingInvoiceTDS != null)
                {
                    editingInvoiceTDS.TDSDeductions = NewTDS.Value;
                    UpdateTDS();
                }
                StopEditingTDS();
            }
        }

        public async Task UpdateTDS()
        {
            try
            {
                var response = await TenantService.UpdateTDSPercentage(editingInvoiceTDS);
                if (response)
                {
                    GetInvoiceAsync();
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        #endregion

        #region TDSUpload

        private async Task TDSFileUpload(InputFileChangeEventArgs files, InvoiceDto invoiceDto)
        {
            foreach (var file in files.GetMultipleFiles())
            {
                var data = await MyCSharpFunction(file);
                if (data != null)
                {
                    var base64Data = Convert.ToBase64String(data);
                    fileData.Add(base64Data);
                }
            }

            string fileName = $"{invoiceDto.TenantId}_{invoiceDto.TransactionDate?.ToString("ddMMyyHHmm")}";
            var tdsresponse = await TenantService.UploadTDSCertificate(fileData, fileName);
            var TDSUrl = tdsresponse;
        }

        public async Task<byte[]> MyCSharpFunction(IBrowserFile file)
        {
            // Process the uploaded file
            // You can access file name, size, and stream
            string fileName = file.Name;
            long fileSize = file.Size;
            Stream fileStream = file.OpenReadStream(maxAllowedSize: 1024 * 1024 * 15);
            byte[] fileBytes;

            using (var memoryStream = new MemoryStream())
            {
                // Read data from the stream in chunks and write to memory stream
                byte[] buffer = new byte[4096]; // Adjust buffer size as needed
                int bytesRead;
                while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    memoryStream.Write(buffer, 0, bytesRead);
                }

                // Convert memory stream to byte array
                fileBytes = memoryStream.ToArray();
            }

            // Compress the byte array using BZip2
            // byte[] compressedBytes = CompressBytesBZip2(fileBytes);
            return fileBytes;
        }


        #endregion

        protected async Task GetTrueFilter()
        {
            var tenants = Filter.GetType().GetProperties();
            foreach (var tenant in tenants)
            {
                if (tenant.GetValue(Filter) != null && tenant.GetValue(Filter).ToString() == "Select")
                {
                    tenant.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }

        public void Paginate(int page)
        {
            PageNumber = page;
        }

        #region GSTVerification
        public async Task<string> GenerateEncryptedSignature(string clientIdWithEpochTimestamp = "CF602894CPQ0VR2MM5F00SIANUNG")
        {
            string encryptedSignature = "";

            try
            {
                #region PublicKey
                string publicKeyContent = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAusPdINRlgztVmIWIfmeGzOLf/wzj6NbbJB6HOgO3B+/d5HGqM40wTlLkH4gxO8sf+gahLFoZBVZ3IHcidxyGuedz5fhbw8QYZyL2ca78z0fKqkvqK8GxlS+Dysdr75+dLCtmhH8TjC8AKN6eQ0tnJGFacY+QM62wuWpH7jjoJ1RAbwjGiPQ2MiQ83aMcE2B0nP0oGjBzAgOVSTqIbv6Kynb1NCnYaeWIJUKalLDJD2gdahv+7MCqqOAfTD4FAfqsbBb4jJmXhrhEkL0g1rxqc34V5LHWc4UDsBQfW4D8AXUbvZak8TBOk5dYPpnHZfaI9Sz2FwY1FDfePpDWFzeG1QIDAQAB";
                #endregion

                publicKeyContent = publicKeyContent
                    .Replace("-----BEGIN PUBLIC KEY-----", "")
                    .Replace("-----END PUBLIC KEY-----", "")
                    .Replace("\n", "")
                    .Replace("\r", "");

                // Convert PEM content to byte array
                byte[] publicKeyBytes = Convert.FromBase64String(publicKeyContent);

                // Create an RSA object
                using (RSA rsa = RSA.Create())
                {
                    rsa.ImportSubjectPublicKeyInfo(publicKeyBytes, out _);

                    // Encrypt the data with the public key using OAEP SHA-1 padding
                    byte[] dataToEncrypt = Encoding.UTF8.GetBytes(clientIdWithEpochTimestamp);
                    byte[] encryptedData = rsa.Encrypt(dataToEncrypt, RSAEncryptionPadding.OaepSHA1);

                    // Convert the encrypted data to a base64 string
                    encryptedSignature = Convert.ToBase64String(encryptedData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message); // Log the exception for debugging
            }
            await PerformApiCallAsync(clientIdWithEpochTimestamp, "https://api.cashfree.com/verification/gstin", encryptedSignature);
            return encryptedSignature;
        }

        private static readonly HttpClient client = new HttpClient();

        public static async Task<string> PerformApiCallAsync(string clientId, string apiUrl, string encryptedSignature)
        {
            string responseContent = string.Empty;

            try
            {
                // Create a new HttpRequestMessage
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                // Add the encrypted signature to the request headers
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", encryptedSignature);
                InvoiceVerification invoiceVerification = new()
                {
                    GSTIN = "29AAJCD9183B1ZE",
                    Businessname = "dhinwa solutions private limited."
                };
                request.Content = JsonContent.Create<InvoiceVerification>(invoiceVerification);
                // Optionally, add other required headers
                request.Headers.Add("Client-Id", clientId);

                // Send the request
                HttpResponseMessage response = await client.SendAsync(request);

                // Ensure the response is successful
                response.EnsureSuccessStatusCode();

                // Read the response content
                responseContent = await response.Content.ReadAsStringAsync();
                var data = responseContent.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            return responseContent;
        }
        #endregion

        #region InvoiceDownload
        public async Task UpdateAndDownloadPdf(InvoiceDto invoiceDto)
        {
            try
            {
                string inputFile = "https://leadrat-black.s3.ap-south-1.amazonaws.com/Documents/Tenant_TDSCertificates/InvoiceTemplate.pdf";
                string outputFile = "updated_document.pdf";
                string filename = invoiceDto.TenantId + "_" + invoiceDto.InvoiceNumber + "_Leadrat.pdf";

                using (PdfReader reader = new PdfReader(inputFile))
                {
                    using (PdfWriter writer = new PdfWriter(outputFile))
                    {
                        using (PdfDocument pdfDoc = new PdfDocument(reader, writer))
                        {
                            PdfAcroForm form = PdfAcroForm.GetAcroForm(pdfDoc, true);
                            PdfFormField companyNameField = form.GetField("CompanyName");
                            companyNameField.SetValue(invoiceDto.TenantName).SetReadOnly(true);
                            PdfFormField CompanyAddress = form.GetField("CompanyAddress");
                            CompanyAddress.SetValue(invoiceDto.Address).SetReadOnly(true);
                            PdfFormField GST = form.GetField("GSTNo").SetReadOnly(true);
                            if (invoiceDto?.GSTNumber?.ToUpper().Trim() != "NA")
                            {
                                GST.SetValue(invoiceDto?.GSTNumber);
                            }
                            PdfFormField InvoiceNo = form.GetField("InvoiceNo").SetReadOnly(true);
                            InvoiceNo.SetValue(invoiceDto.InvoiceNumber);
                            PdfFormField InvoiceDate = form.GetField("InvoiceDate").SetReadOnly(true);
                            InvoiceDate.SetValue(invoiceDto.TransactionDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                            PdfFormField AmountDue = form.GetField("AmountDue").SetReadOnly(true);
                            if (invoiceDto.TotalPackageAmount == invoiceDto.TotalAmountPaid)
                            {
                                AmountDue.SetValue(("0/-").ToString());
                            }
                            else
                            {
                                AmountDue.SetValue((invoiceDto.TotalAmountPaid + invoiceDto.DueAmount + "/-").ToString());
                            }
                            PdfFormField DueDate = form.GetField("DueDate").SetReadOnly(true);
                            DueDate.SetValue(invoiceDto.DueDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                            if (invoiceDto.SubscriptionType == "AddOnSubscription")
                            {
                                PdfFormField Description = form.GetField("Description").SetReadOnly(true);
                                Description.SetValue("CRM Addon");
                            }
                            else if (invoiceDto.SubscriptionType == "Subscription")
                            {
                                PdfFormField Description = form.GetField("Description").SetReadOnly(true);
                                Description.SetValue("CRM Application");
                            }
                            PdfFormField PaymentDate = form.GetField("PaymentDate").SetReadOnly(true);
                            PaymentDate.SetValue(invoiceDto.TransactionDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                            PdfFormField NoofLicense = form.GetField("NoOfLicence").SetReadOnly(true);
                            NoofLicense.SetValue(invoiceDto.NoOfLicenses.ToString());
                            PdfFormField Validity = form.GetField("Validity").SetReadOnly(true);
                            Validity.SetValue(invoiceDto.LicenseValidity?.ToLocalTime().ToString("dd/MM/yyyy"));
                            PdfFormField GSTAmount = form.GetField("GSTAmount").SetReadOnly(true);
                            GSTAmount.SetValue(invoiceDto.GSTAmount.ToString() + "/-");
                            PdfFormField NetAmount = form.GetField("NetAmount").SetReadOnly(true);
                            NetAmount.SetValue(invoiceDto.NetAmountPaid.ToString() + "/-");
                            PdfFormField TotalPaid = form.GetField("TotalPaid").SetReadOnly(true);
                            TotalPaid.SetValue(invoiceDto.TotalAmountPaid.ToString() + "/-");
                            PdfFormField AmountDueafterPayment = form.GetField("AmountDueafterPayment").SetReadOnly(true);
                            AmountDueafterPayment.SetValue(invoiceDto.DueAmount.ToString() + "/-");
                            PdfFormField PaymentMode = form.GetField("PaymentMode").SetReadOnly(true);
                            PaymentMode.SetValue(invoiceDto.PaymentMode.ToString());
                        }
                    }
                }
                await DownloadUpdatedPdfAsync(outputFile, filename);
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        private async Task DownloadUpdatedPdfAsync(string updatedPdfPath, string filename)
        {
            try
            {
                byte[] data = await File.ReadAllBytesAsync(updatedPdfPath);
                string contentType = "application/pdf";
                await JsRuntime.InvokeVoidAsync("saveAsFile", filename, data, contentType);
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        #endregion

        public async Task SortColumnAsync()
        {
            PagedResponse<InvoiceDto, string> response = await TenantService.GetInvoiceDetails(Filter);
            if (response != null && response.Items != null)
            {
                Collection.Clear();
                Collection = new ObservableCollection<InvoiceDto>(response.Items);
                TotalCount = (int)response.TotalCount;
                MaxPageNumber = (double)TotalCount / (double)PageSize;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
        }

        private string GetSortStyle(string columnName)
        {
            if (Filter.Sort != columnName)
            {
                return string.Empty;
            }
            if (Filter.IsDecending)
            {
                return "sort up";
            }
            else
            {
                return "sort down";
            }
        }

        private async Task NavigateToComponent(string tenantId)
        {
            JsRuntime.InvokeVoidAsync("open", $"/tenant/{tenantId}", "_blank");
        }

        private async Task DecreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetInvoiceAsync();
        }

        private async Task IncreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber >= (Math.Ceiling(MaxPageNumber)))
            {
                PageNumber++;
            }
            await GetInvoiceAsync();
        }

    }
}
