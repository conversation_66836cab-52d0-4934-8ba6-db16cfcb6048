﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Pages.Login
{
    public partial class LoginPage
    {
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public CustomAuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        public UserLoginDto UserLogin { get; set; } = new();

        public async Task HandleLogin()
        {
            var token = await AuthService.LoginUser(UserLogin);
            if (token.Data == null)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Authentication Failed");
            }
            else
            {
                AuthenticationStateProvider.ValidateToken(token.Data);
            }
        }
        public async Task NavigateToNewComponent()
        {
            Navigation.NavigateTo("/");
        }
    }
}
