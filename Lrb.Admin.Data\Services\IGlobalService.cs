﻿using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IGlobalService
    {
        Task<GlobalSettingViewModel> GetSettingByTenantAsync(string tenantId, string? readConnectionString);
        Task<Response<bool>> UpdateInternationalNumberAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateDailyStatusAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadStatusPendingAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadExportAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateLeadSourceAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateDuplicateLeadAsync(string tenantId, string? connectionString);
        Task<DuplicateLeadViewModel> GetDuplicateLead(string tenantId, string? readConnectionString);
        Task<Response<bool>> UpdateMicrositeFeatureAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateZoneLocationFeatureAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateShouldEnablePropertyListingAsync(string tenantId, string? connectionString);
        Task<Response<bool>> UpdateEnableCustomLeadFormAsync(string tenantId, string? connectionString);
        Task<Response<bool?>> DNSEnableDisableAsync(string tenantId, bool? status);
        Task<bool?> GetIsDomainEnabledAsync(string tenantId);
        Task<bool?> GetShouldHideSubscriptionAsync(string tenantId, string? readConnectionString);
        Task<Response<bool>> UpdateShouldHideSubscriptionAsync(string tenantId, string? connectionString);
        Task<Response<bool>> ToggleCallRecordingAsync(string tenantId, string? connectionString);
        Task<Response<bool?>> GetIsCallRecordingEnabledAsync(string tenantId, string? connectionString);

    }
}
