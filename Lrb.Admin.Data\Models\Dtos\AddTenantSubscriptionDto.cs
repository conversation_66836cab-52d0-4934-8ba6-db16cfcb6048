﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class AddTenantSubscriptionDto
    {
        private double _soldPrice;
        private double _dueAmount;
        private double _gstamt;
        private double _paidNetAmount;
        private double _paidGSTAmount;
        private double _perUserCost;
        private bool _gstinclude = true;

        public double PerUserCost
        {
            get => _perUserCost;
            set
            {
                _perUserCost = value;
            }
        }

        public bool GstInclude
        {
            get => _gstinclude;
            set
            {
                _gstinclude = value;
            }
        }

        public double SoldPrice
        {
            get 
            {
                _soldPrice = GstInclude ? Math.Round((TotalAmount / 1.18), 2) : TotalAmount;
                return _soldPrice;
            }
            set => _soldPrice = value;
        }

        public int SoldLicences { get; set; } = 1;

        public double PendingAmount
        {
            get => _soldPrice + _gstamt - _paidNetAmount - _paidGSTAmount;
            set => _dueAmount = value;
        }

        public double TotalAmount
        {
            get
            {
                double baseAmount = PerUserCost * SoldLicences;

                double total = BillingType switch
                {
                    BillingType.Quarterly => baseAmount * 3,
                    BillingType.Halfyearly => baseAmount * 6,
                    BillingType.Yearly => baseAmount * 12,
                    _ => baseAmount
                };

                return Math.Round(total, 2);
            }
            set { }
        }
        public DateTime? LicenseValidity { get; set; }
        public PaymentMode PaymentMode { get; set; }
        public PaymentType Type { get; set; }
        public BillingType BillingType { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;

        public double GstAmount
        {
            get 
            {
                _gstamt = GstInclude ? Math.Round((TotalAmount - SoldPrice), 2) : 0;
                return _gstamt;
            }
            set => _gstamt = value;
        }
        public string? Description { get; set; }
        public double PaidNetAmount
        {
            get => _paidNetAmount;
            set => _paidNetAmount = value;
        }
        public double PaidGSTAmount
        {
            get => _paidGSTAmount;
            set => _paidGSTAmount = value;
        }
        public double PaidTotalAmount
        {
            get => _paidNetAmount + _paidGSTAmount;
            set => _soldPrice = value;
        }
        public DateTime NextDueDate { get; set; } = DateTime.UtcNow.AddMonths(1);
        public string? GSTNumber { get; set; } = "na";
    }

}
