﻿@page "/generate-invoice"
@page "/generate-invoice/{Tenantid}"

<div id="downloadPdf">
    <h3 style="text-align:center; font-weight:bolder">Tax Invoice</h3>
    <div class="invoice-design">
        <div class="billing-details">
            <div class="billing-from">
                <label for="billin_form" style="font-weight:bold">BILL FROM:</label>
                <label>DHINWA SOLUTIONS PVT. LTD. </label>
                <label>Bengaluru: #1596, 3rd Floor, HSR Layout 1st Sector, </label>
                <label>Beside Highlander Motor Garage,</label>
                <label>Agara Village, Bengaluru – 560102.</label>
                <div class="gst-in">
                    <label style="font-weight:bold">GST IN:</label>
                    <label>@FromGSTIN</label>
                </div>
                <div>
                    <label style="font-weight:bold">CIN:</label>
                    <label>@CIN_No</label>
                    @*<input id="billin_form" @bind="TaxInvoiceDto.FromGSTIN" class="form-control"></input>*@
                </div>
            </div>
            <div class="billing-to">
                <label for="billin_to" style="font-weight:bold">BILL TO:</label>
                <label>@SubscriptionModel.Name</label>
                @if (@TenantAddress?.Locality != null || @TenantAddress?.SubLocality != null || @TenantAddress?.District!=null)
                {
                    <label>@TenantAddress?.Locality, @TenantAddress?.SubLocality, @TenantAddress?.District</label>
                }
                <text><span style="font-weight:bold">City</span>: @string.Join(" ", TenantAddress?.City, ", ")</text>
                <text><span style="font-weight:bold">State</span>: @string.Join(" ", TenantAddress?.State, ", ")</text>
                <text><span style="font-weight:bold">PINCODE</span>: @string.Join(" ", TenantAddress?.PostalCode)</text>
                <div class="gst-in">
                    @*<label style="font-weight:bold">GST IN:</label>
                    <label>@GenerateInvoiceDto?.ToGSTIN</label>*@
                    @if (SubscriptionModel.GSTNumber != null)
                    {
                        <text>GST IN: @string.Join(" ", SubscriptionModel.GSTNumber)</text>
                    }
                    @*<input id="billin_to" @bind="TaxInvoiceDto.ToGSTIN" class="form-control">@TaxInvoiceDto.ToGSTIN</input>*@
                </div>
            </div>
        </div>
        <div class="billing-details-right">
            <div class="invoice-logo">
                <img src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/icon.svg"style="margin-left:-10px" height="50" />
                <img src="https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/text.svg" height="50" />
            </div>
            <div class="invoice-no">
                <label style="font-weight:bold" for="invoice_no">Invoice No: </label>
                <label id="invoice_no">@InvoiceNo</label>
            </div>
            <div class="invoice-date">
                <label style="font-weight:bold" for="invoice_date">Invoice Date :</label>
                <label>@InvoiceDate.ToString("dd/MM/yyyy")</label>
                
                @*<input id="invoice_date" @bind="TaxInvoiceDto.InvoiceDate"></input>*@
            </div>

            <div class="amount-due">
                <label style="font-weight:bold" for="invoice_date">HSN /SAC:</label>
                <label>997331</label>
            </div>
            
            <div class="amount-due">
                <label style="font-weight:bold" for="invoice_date">Amount Due :</label>
                @if (PaymentModel.PendingAmount == 0)
                {
                   @* <label>@TenantDetails?.Select(i => i.DueAmount).FirstOrDefault()</label>*@
                    <label style="font-weight:bold">Payment Cleared</label>
                }
                else
                {
                    <label>₹@PaymentModel.PendingAmount.ToString("n0")</label>
                }
                @*<input id="invoice_date" @bind="TaxInvoiceDto.DueAmount" style=""></input>*@
            </div>
            <div class="due-date">
                <label style="font-weight:bold" for="due_date">Due Date :</label>
                <label>@PaymentModel.CreatedOn.ToString("dd/MM/yyyy")</label>
                @*<input id="due_date" @bind="TaxInvoiceDto.DueDate"></input>*@
            </div>
        </div>
    </div>
    <div class="tenant-subs">
        <div class="subs-details">
            <div class="description">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">Description</label>
                <label style="padding:7%;">@Description</label>
            </div>
            <div class="payment-date">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">Payment Date</label>
                <label style="padding:13px;text-align:center;">@PaymentModel.NextDueDate?.ToString("dd/MM/yyyy")</label>
            </div>
            <div class="license-details">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">No of License</label>
                <label style="padding:13px;text-align:center;">@SubscriptionModel.SoldLicenses</label>
            </div>
            <div class="license-details">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">Validity</label>
                <label style="padding:11%;text-align:center;">@SubscriptionModel.BillingType</label>
            </div>
            <div class="license-validity">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">Net Amount</label>
                <label style="padding:8%;text-align:center;">₹@PaymentModel.NetAmount.ToString("n0")/-</label>
            </div>
            <div class="license-validity">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">GST/IGST</label>
                <label style="padding:8%;text-align:center;">₹@PaymentModel.GSTAmount.ToString("n0")/-</label>
            </div>
            <div class="subs-price">
                <label style="font-weight:bold; border-bottom:1px solid black;height:61px;display:flex;justify-content:center;align-items:center;">Total Amount</label>
                <label style="padding:11%;text-align:center;">₹@PaymentModel.TotalAmount.ToString("n0")/-</label>
            </div>
        </div>
    </div>
    <div class="payment-details">
        <div class="banking-details">
            <label style="font-weight:bold"><u>Banking Information</u></label>
            <div class="bank-name">
                <label style="font-weight:bold">Bank Name:</label>
                <label>ICICI BANK </label>
            </div>
            <div class="account-name">
                <label style="font-weight:bold">A/C Name:</label>
                <label>DHINWA SOLUTIONS PRIVATE LIMITED </label>
            </div>
            <div class="acc-no">
                <label style="font-weight:bold">Account No:</label>
                <label>************</label>
            </div>
            <div class="ifcs-code">
                <label style="font-weight:bold">IFCS Code:</label>
                <label>ICIC0007295</label>
            </div>
            <div class="acc-type">
                <label style="font-weight:bold">Account Type:</label>
                <label>Current</label>
            </div>
            
        </div>
        <div class="payment-history">
            <div class="payment-type">
                <label style="font-weight:bold;width:100%; border-bottom:1px solid black;height:72px;display:flex;justify-content:center;align-items:center;">Total Amount Paid</label>
                <label style="font-weight:bold;height:72px;display:flex;justify-content:center;align-items:center;">Amount Due</label>
            </div>
            <div class="payment">
                <label style="width:100%;border-bottom:1px solid black;height:72px;display:flex;justify-content:center;align-items:center;">₹@PaymentModel.TotalAmount.ToString("n0")/-</label>
                <label style="width:100%;height:72px;display:flex;justify-content:center;align-items:center;">₹@PaymentModel.PendingAmount.ToString("n0")/-</label>
            </div>
        </div>

    </div>
    <div style="display:flex;align-items:center;" class="amount-word">
        <label style="font-weight:bold">Amount In Words:</label>
        <label>@ConvertNumberToWords(PaymentModel.TotalAmount) Only</label>
    </div>
    <div style="display:flex;align-items:center;" class="payment-mode">
        <label style="font-weight:bold">Payment Mode:</label>
        <label>@PaymentModel.Mode</label>
    </div>
    <div style="display:flex;align-items:center;" class="payment-status">
        <label style="font-weight:bold">Payment Status:</label>
       
        @if (PaymentModel.PendingAmount == 0)
        {
           
            <label >Payment Cleared</label>
        }
        else
        {
            <label>Due</label>
        }
        
    </div>
    <div style="display:flex;align-items:center;" class="note">
        <label style="font-weight:bold">Note:</label>
        <label>Electronically Generated Invoice No Signature Necessary</label>
    </div>
    <div style="display:flex;align-items:center;" class="terms-condotion">
        <label>
            <span style="font-weight:bold">Terms & Conditions :</span>
            Clear payment within 15 days of receiving this invoice. There will be a 1.5% interest
            charge per month on late invoices. (Ignore If invoice is already cleared)</label>
    </div>
</div>
@*<textarea @bind="PdfDocument"></textarea>*@

<div class="download-pdf">
    <div class="btn btn-primary btn-sm" @onclick="GeneratePdf">Download</div>
</div>




