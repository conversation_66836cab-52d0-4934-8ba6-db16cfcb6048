﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class GetCustomListingSourcesDto
    {
        public Guid Id { get; set; }
        public string? DisplayName { get; set; }
        public int Value { get; set; }
        public int OrderRank { get; set; }
        public string? ImageURL { get; set; }
        public string? ProgressColor { get; set; }
        public string? BackgroundColor { get; set; }
        public bool IsDefault { get; set; }
        public string TenantId { get; set; }
        public bool IsDeleted { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public bool? ShouldIncludeInRefrenceId { get; set; }
    }
}
