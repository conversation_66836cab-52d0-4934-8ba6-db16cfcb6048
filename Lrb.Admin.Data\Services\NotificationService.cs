﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    internal class NotificationService : INotificationService
    {
        private readonly INotificationRepository _notificationRepository;
        private readonly IHttpService _httpService;
        private readonly IDapperRepositoryAsync _dapperRepository;
        private readonly IConfiguration _config;
        private readonly ITenantRepository _tenantRepository;
        protected string _baseUri = "https://localhost:7159/";
        private readonly LrbApiEndPoints _apiEndPoints;
        public NotificationService(IHttpService httpService,IDapperRepositoryAsync dapperRepository, IConfiguration config, ITenantRepository tenantRepository, IOptions<LrbApiEndPoints> apiEndPoints, INotificationRepository notificationRepository)
        {
            _httpService = httpService;
            _dapperRepository = dapperRepository;
            _config = config;
            _tenantRepository = tenantRepository;
            _apiEndPoints = apiEndPoints.Value;
            _notificationRepository = notificationRepository;
        }

        public async Task<List<Guid>> GetAllUserIdsAsync(string tenantId)
        {
            List<Guid> guidList = new List<Guid>();
            var response = await _tenantRepository.GetAllUserId(tenantId);
            foreach (string str in response)
            {
                Guid guid;
                if (Guid.TryParse(str, out guid))
                {
                    guidList.Add(guid);
                }
            }
            return guidList;
        }

        public async Task<Response<bool>> SendNotificationAsync(string tenantId,NotificationModelDto Model)
        {
            try
            { 
                string resource = $"api/v1/utility/notification/send";
                var response = await _httpService.PostAsync<NotificationModelDto, Response<bool>>(_apiEndPoints.WebApi, resource, Model);
                return response;
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }   
        }

        public async Task<Response<bool>> CaptureNotificationOfUserAsync(Notifications model)
        {
            try
            {
                var response = await _notificationRepository.CaptureNotificationDetailOfUser(model);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<NotificationDto> CheckDeviceTokenExpireAsync(string tenantId, Guid id)
        {
            try
            {
                var response = await _notificationRepository.CheckNotificationTokenOfUser(tenantId, id);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<NotificationViewModel, string>> GetNotificationDetailsAsync(string tenantId, NotificationFilter filter)
        {
            try
            {
                var response = await _notificationRepository.GetAllNotificationDetailsAsync(tenantId, filter.PageNumber, filter.PageSize, filter);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
