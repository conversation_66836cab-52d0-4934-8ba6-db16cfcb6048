﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Pages.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace Lrb.Admin.UI.Pages.SubscriptionPage
{
    [Authorize(Roles = "Admin")]
    public partial class UpdateAddOnSubscription
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public ISubscritionRepository SubscritionService { get; set; }
        public string? Token { get; set; }
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public Guid? SubscriptionId { get; set; }
        [Parameter]
        public LRBSubscriptionAddOns SubscriptionModel { get; set; }
        public GetTenantModel TenantModel { get; set; } = new();
        public CollectionType CollectionType { get; set; }
        public async Task UpdateSubscription()
        {
            int result = await SubscritionService.UpdateAddOnSubscription(SubscriptionModel);
            if (result > 0)
            {
                Token = await LocalStorage.GetItemAsync<string>("token");
            }
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            AdminUserActivity userActivity = new()
            {
                AdminUserId = userActivityHistory.AdminUserId,
                TenantId = TenantId,
                ActivityTime = DateTime.UtcNow,
                Activity = ActivityConst.UpdateSubscription + " " + TenantModel?.TenantName,
                Token = Token
            };
            await ActivityService.UserActivityAsync(userActivity);
            if (result > 0)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"Subscription for {TenantModel?.TenantName} Successfully Created");
                await jSRuntime.InvokeVoidAsync("location.reload");
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await jSRuntime.InvokeAsync<bool>("confirm", $"Are you sure?");
            if (isConfirmed)
            {
                SubscriptionModel = new();
            }
        }
    }

}
