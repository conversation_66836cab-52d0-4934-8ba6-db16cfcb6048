﻿<div class="update-subs">
    <svg width="99" height="33" viewBox="0 0 99 33" fill="none" xmlns="http://www.w3.org/2000/svg" class="AddonButton" @onclick=" async () => { await AddPayments();}">
            <rect width="99" height="33" rx="4" fill="#343739" />
            <path d="M13.176 20.5L16.179 12.8H17.521L20.502 20.5H19.017L17.367 16.122C17.3303 16.034 17.279 15.8947 17.213 15.704C17.1543 15.5133 17.0883 15.308 17.015 15.088C16.9417 14.8607 16.8757 14.6517 16.817 14.461C16.7583 14.263 16.7143 14.12 16.685 14.032L16.96 14.021C16.916 14.1677 16.8647 14.3327 16.806 14.516C16.7473 14.6993 16.685 14.89 16.619 15.088C16.553 15.286 16.487 15.4767 16.421 15.66C16.3623 15.8433 16.3073 16.0083 16.256 16.155L14.606 20.5H13.176ZM14.54 18.74L15.035 17.497H18.544L19.072 18.74H14.54ZM23.5898 20.61C23.0765 20.61 22.6145 20.4817 22.2038 20.225C21.8005 19.961 21.4778 19.6053 21.2358 19.158C21.0012 18.7033 20.8838 18.1827 20.8838 17.596C20.8838 17.0167 21.0012 16.4997 21.2358 16.045C21.4705 15.5903 21.7895 15.2347 22.1928 14.978C22.6035 14.714 23.0655 14.582 23.5788 14.582C23.8575 14.582 24.1252 14.626 24.3818 14.714C24.6458 14.802 24.8805 14.923 25.0858 15.077C25.2912 15.2237 25.4525 15.3887 25.5698 15.572C25.6945 15.7553 25.7642 15.946 25.7788 16.144L25.4158 16.188V12.36H26.7688V20.5H25.4598L25.4268 19.136L25.6908 19.158C25.6835 19.3413 25.6175 19.5173 25.4928 19.686C25.3755 19.8547 25.2215 20.0087 25.0308 20.148C24.8402 20.2873 24.6165 20.401 24.3598 20.489C24.1105 20.5697 23.8538 20.61 23.5898 20.61ZM23.8538 19.466C24.1692 19.466 24.4478 19.3853 24.6898 19.224C24.9318 19.0627 25.1188 18.8427 25.2508 18.564C25.3902 18.2853 25.4598 17.9627 25.4598 17.596C25.4598 17.2367 25.3902 16.9177 25.2508 16.639C25.1188 16.353 24.9318 16.133 24.6898 15.979C24.4478 15.8177 24.1692 15.737 23.8538 15.737C23.5385 15.737 23.2598 15.8177 23.0178 15.979C22.7758 16.133 22.5852 16.353 22.4458 16.639C22.3138 16.9177 22.2478 17.2367 22.2478 17.596C22.2478 17.9627 22.3138 18.2853 22.4458 18.564C22.5852 18.8427 22.7758 19.0627 23.0178 19.224C23.2598 19.3853 23.5385 19.466 23.8538 19.466ZM30.6367 20.61C30.1234 20.61 29.6614 20.4817 29.2507 20.225C28.8474 19.961 28.5247 19.6053 28.2827 19.158C28.048 18.7033 27.9307 18.1827 27.9307 17.596C27.9307 17.0167 28.048 16.4997 28.2827 16.045C28.5174 15.5903 28.8364 15.2347 29.2397 14.978C29.6504 14.714 30.1124 14.582 30.6257 14.582C30.9044 14.582 31.172 14.626 31.4287 14.714C31.6927 14.802 31.9274 14.923 32.1327 15.077C32.338 15.2237 32.4994 15.3887 32.6167 15.572C32.7414 15.7553 32.811 15.946 32.8257 16.144L32.4627 16.188V12.36H33.8157V20.5H32.5067L32.4737 19.136L32.7377 19.158C32.7304 19.3413 32.6644 19.5173 32.5397 19.686C32.4224 19.8547 32.2684 20.0087 32.0777 20.148C31.887 20.2873 31.6634 20.401 31.4067 20.489C31.1574 20.5697 30.9007 20.61 30.6367 20.61ZM30.9007 19.466C31.216 19.466 31.4947 19.3853 31.7367 19.224C31.9787 19.0627 32.1657 18.8427 32.2977 18.564C32.437 18.2853 32.5067 17.9627 32.5067 17.596C32.5067 17.2367 32.437 16.9177 32.2977 16.639C32.1657 16.353 31.9787 16.133 31.7367 15.979C31.4947 15.8177 31.216 15.737 30.9007 15.737C30.5854 15.737 30.3067 15.8177 30.0647 15.979C29.8227 16.133 29.632 16.353 29.4927 16.639C29.3607 16.9177 29.2947 17.2367 29.2947 17.596C29.2947 17.9627 29.3607 18.2853 29.4927 18.564C29.632 18.8427 29.8227 19.0627 30.0647 19.224C30.3067 19.3853 30.5854 19.466 30.9007 19.466ZM38.827 20.5V12.8H42.028C42.468 12.8 42.864 12.9063 43.216 13.119C43.5753 13.3317 43.8613 13.6213 44.074 13.988C44.2866 14.3547 44.393 14.7653 44.393 15.22C44.393 15.682 44.2866 16.1 44.074 16.474C43.8613 16.8407 43.5753 17.134 43.216 17.354C42.864 17.574 42.468 17.684 42.028 17.684H40.257V20.5H38.827ZM40.257 16.364H41.929C42.1123 16.364 42.2773 16.3127 42.424 16.21C42.578 16.1073 42.699 15.9717 42.787 15.803C42.8823 15.6343 42.93 15.4437 42.93 15.231C42.93 15.0183 42.8823 14.8313 42.787 14.67C42.699 14.5013 42.578 14.3693 42.424 14.274C42.2773 14.1713 42.1123 14.12 41.929 14.12H40.257V16.364ZM47.7456 20.61C47.269 20.61 46.8363 20.478 46.4476 20.214C46.059 19.95 45.7473 19.5907 45.5126 19.136C45.278 18.6813 45.1606 18.1643 45.1606 17.585C45.1606 17.0057 45.278 16.4887 45.5126 16.034C45.7473 15.5793 46.0663 15.2237 46.4696 14.967C46.873 14.7103 47.3276 14.582 47.8336 14.582C48.127 14.582 48.3946 14.626 48.6366 14.714C48.8786 14.7947 49.0913 14.912 49.2746 15.066C49.458 15.22 49.6083 15.396 49.7256 15.594C49.8503 15.792 49.9346 16.0047 49.9786 16.232L49.6816 16.155V14.703H51.0456V20.5H49.6706V19.114L49.9896 19.059C49.9383 19.257 49.843 19.4513 49.7036 19.642C49.5716 19.8253 49.403 19.9903 49.1976 20.137C48.9996 20.2763 48.776 20.39 48.5266 20.478C48.2846 20.566 48.0243 20.61 47.7456 20.61ZM48.1196 19.411C48.435 19.411 48.7136 19.334 48.9556 19.18C49.1976 19.026 49.3846 18.8133 49.5166 18.542C49.656 18.2633 49.7256 17.9443 49.7256 17.585C49.7256 17.233 49.656 16.9213 49.5166 16.65C49.3846 16.3787 49.1976 16.166 48.9556 16.012C48.7136 15.858 48.435 15.781 48.1196 15.781C47.8043 15.781 47.5256 15.858 47.2836 16.012C47.049 16.166 46.8656 16.3787 46.7336 16.65C46.6016 16.9213 46.5356 17.233 46.5356 17.585C46.5356 17.9443 46.6016 18.2633 46.7336 18.542C46.8656 18.8133 47.049 19.026 47.2836 19.18C47.5256 19.334 47.8043 19.411 48.1196 19.411ZM53.3654 23.03L54.5974 20.181L54.6084 21.083L51.7484 14.703H53.3104L54.9054 18.487C54.9567 18.5823 55.008 18.7107 55.0594 18.872C55.1107 19.026 55.1547 19.18 55.1914 19.334L54.9164 19.422C54.9677 19.2827 55.0227 19.136 55.0814 18.982C55.14 18.8207 55.195 18.6557 55.2464 18.487L56.6104 14.703H58.1834L55.7634 20.5L54.7734 23.03H53.3654ZM59.0054 20.5V14.703H60.3364L60.3584 15.869L60.1384 15.957C60.2044 15.7663 60.3034 15.5903 60.4354 15.429C60.5674 15.2603 60.725 15.1173 60.9084 15C61.0917 14.8753 61.286 14.78 61.4914 14.714C61.6967 14.6407 61.9057 14.604 62.1184 14.604C62.4337 14.604 62.7124 14.6553 62.9544 14.758C63.2037 14.8533 63.409 15.0073 63.5704 15.22C63.739 15.4327 63.8637 15.704 63.9444 16.034L63.7354 15.99L63.8234 15.814C63.904 15.638 64.0177 15.4803 64.1644 15.341C64.311 15.1943 64.476 15.066 64.6594 14.956C64.8427 14.8387 65.0334 14.7507 65.2314 14.692C65.4367 14.6333 65.6384 14.604 65.8364 14.604C66.2764 14.604 66.643 14.692 66.9364 14.868C67.2297 15.044 67.4497 15.3117 67.5964 15.671C67.743 16.0303 67.8164 16.474 67.8164 17.002V20.5H66.4524V17.09C66.4524 16.7967 66.412 16.5547 66.3314 16.364C66.258 16.1733 66.1407 16.034 65.9794 15.946C65.8254 15.8507 65.631 15.803 65.3964 15.803C65.213 15.803 65.037 15.836 64.8684 15.902C64.707 15.9607 64.5677 16.0487 64.4504 16.166C64.333 16.276 64.2414 16.4043 64.1754 16.551C64.1094 16.6977 64.0764 16.859 64.0764 17.035V20.5H62.7124V17.079C62.7124 16.8003 62.672 16.5693 62.5914 16.386C62.5107 16.1953 62.3934 16.0523 62.2394 15.957C62.0854 15.8543 61.8984 15.803 61.6784 15.803C61.495 15.803 61.3227 15.836 61.1614 15.902C61 15.9607 60.8607 16.045 60.7434 16.155C60.626 16.265 60.5344 16.3933 60.4684 16.54C60.4024 16.6867 60.3694 16.848 60.3694 17.024V20.5H59.0054ZM72.0354 20.61C71.4267 20.61 70.8877 20.4817 70.4184 20.225C69.9564 19.9683 69.5934 19.62 69.3294 19.18C69.0727 18.7327 68.9444 18.223 68.9444 17.651C68.9444 17.1963 69.0177 16.782 69.1644 16.408C69.311 16.034 69.5127 15.7113 69.7694 15.44C70.0334 15.1613 70.345 14.9487 70.7044 14.802C71.071 14.648 71.4707 14.571 71.9034 14.571C72.2847 14.571 72.6404 14.6443 72.9704 14.791C73.3004 14.9377 73.5864 15.1393 73.8284 15.396C74.0704 15.6453 74.2537 15.946 74.3784 16.298C74.5104 16.6427 74.5727 17.0203 74.5654 17.431L74.5544 17.904H69.8574L69.6044 16.98H73.4214L73.2454 17.167V16.925C73.2234 16.6977 73.15 16.4997 73.0254 16.331C72.9007 16.155 72.7394 16.0193 72.5414 15.924C72.3507 15.8213 72.138 15.77 71.9034 15.77C71.544 15.77 71.2397 15.8397 70.9904 15.979C70.7484 16.1183 70.565 16.32 70.4404 16.584C70.3157 16.8407 70.2534 17.1633 70.2534 17.552C70.2534 17.926 70.3304 18.2523 70.4844 18.531C70.6457 18.8097 70.8694 19.026 71.1554 19.18C71.4487 19.3267 71.7897 19.4 72.1784 19.4C72.4497 19.4 72.699 19.356 72.9264 19.268C73.1537 19.18 73.3994 19.0223 73.6634 18.795L74.3344 19.73C74.1364 19.9133 73.909 20.071 73.6524 20.203C73.403 20.3277 73.139 20.4267 72.8604 20.5C72.5817 20.5733 72.3067 20.61 72.0354 20.61ZM75.7637 20.5V14.703H77.0837L77.1057 15.891L76.8527 16.023C76.926 15.759 77.069 15.5207 77.2817 15.308C77.4943 15.088 77.7473 14.912 78.0407 14.78C78.334 14.648 78.6347 14.582 78.9427 14.582C79.3827 14.582 79.7493 14.67 80.0427 14.846C80.3433 15.022 80.567 15.286 80.7137 15.638C80.8677 15.99 80.9447 16.43 80.9447 16.958V20.5H79.5917V17.057C79.5917 16.7637 79.5513 16.5217 79.4707 16.331C79.39 16.133 79.2653 15.99 79.0967 15.902C78.928 15.8067 78.7227 15.7627 78.4807 15.77C78.2827 15.77 78.0993 15.803 77.9307 15.869C77.7693 15.9277 77.6263 16.0157 77.5017 16.133C77.3843 16.243 77.289 16.3713 77.2157 16.518C77.1497 16.6647 77.1167 16.826 77.1167 17.002V20.5H76.4457C76.3137 20.5 76.189 20.5 76.0717 20.5C75.9617 20.5 75.859 20.5 75.7637 20.5ZM83.0938 20.5V13.229H84.4468V20.5H83.0938ZM81.9498 15.957V14.703H85.7118V15.957H81.9498Z" fill="white" />
        </svg>
</div>
<div class="all-tables">
    <table>
            <tr>
                <th>Sl No</th>
                <th>Description</th>
                <th>Payment Date</th>
                <th>Net Amount</th>
                <th>GST Amount</th>
                <th>Total Amount</th>
                <th>Payment Mode</th>
                <th>Pending Amount</th>
                <th>Next DueDate</th>
                <th>Action</th>
            </tr>
        @foreach (var items in Payments.Select((value, i) => new { value, i }))
        {
            <tr class=@(items.value.IsDeleted ? "Isdeleted" : "")>
                <td>@(items.i + 1)</td>
                <td>@items.value?.Description</td>
                <td>@items.value?.CreatedOn.ToString("dd/MM/yyyy")</td>
                <td>@items.value?.NetAmount</td>
                <td>@items.value?.GSTAmount</td>
                <td>@items.value?.TotalAmount</td>
                <td>@items.value?.Mode</td>
                <td>@items.value?.PendingAmount</td>
                <td>
                    @if (items.value?.PendingAmount == 0)
                    {
                        <span>Completed</span>
                    }
                    else
                    {
                        @items.value?.NextDueDate?.ToString("dd/MM/yyyy")
                    }
                </td>
                <td>
                     <span onclick="@(async () => @UpdatePayments(items.value))">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="20" height="20" rx="4" fill="#78AECC" />
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9889 5.68311C11.4543 5.215 12.2089 5.215 12.6743 5.68311L13.5171 6.5307C13.9825 6.9988 13.9825 7.75776 13.5171 8.22586L12.7699 8.97736C12.7112 8.94815 12.6495 8.91656 12.5855 8.88268C12.1548 8.65462 11.6437 8.33455 11.2623 7.95091C10.8808 7.56727 10.5626 7.05323 10.3358 6.62005C10.3021 6.55568 10.2707 6.4936 10.2417 6.4346L10.9889 5.68311ZM10.7154 8.50088C11.1668 8.95485 11.7408 9.31215 12.1955 9.55506L9.2571 12.5103C9.07476 12.6937 8.83819 12.8127 8.58291 12.8494L6.81315 13.1036C6.41987 13.1601 6.08278 12.8211 6.13896 12.4256L6.39178 10.6456C6.42825 10.3889 6.54654 10.151 6.72888 9.96757L9.66725 7.0123C9.90878 7.46954 10.264 8.0469 10.7154 8.50088ZM8.96837 13.8876C8.75482 13.8876 8.5817 14.0617 8.5817 14.2765C8.5817 14.4913 8.75482 14.6654 8.96837 14.6654H13.4795C13.693 14.6654 13.8661 14.4913 13.8661 14.2765C13.8661 14.0617 13.693 13.8876 13.4795 13.8876H8.96837Z" fill="white" />
                            <title>Update Payment Details</title>
                        </svg>

                    </span>
                    <span onclick="@(async () => DeleteSubscription(items.value))">
                    @if (LoginAdminUserInfo.Role == Admin.Data.Models.Identity.Role.Admin)
                    {
                            
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="20" height="20" rx="4" fill="#EF595C" />
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0502 14.6654C12.0188 14.6654 12.8227 13.9085 12.8917 12.9315L13.2093 8.43469C12.2321 8.09011 11.1452 7.89804 10 7.89804C8.85484 7.89804 7.76794 8.09011 6.79073 8.43469L7.10837 12.9315C7.17737 13.9085 7.98125 14.6654 8.9498 14.6654H11.0502ZM9.07696 9.1815C9.26813 9.1815 9.42311 9.33821 9.42311 9.53153V12.3318C9.42311 12.5251 9.26813 12.6818 9.07696 12.6818C8.88579 12.6818 8.73081 12.5251 8.73081 12.3318V9.53153C8.73081 9.33821 8.88579 9.1815 9.07696 9.1815ZM11.2692 9.53153C11.2692 9.33821 11.1143 9.1815 10.9231 9.1815C10.7319 9.1815 10.5769 9.33821 10.5769 9.53153V12.3318C10.5769 12.5251 10.7319 12.6818 10.9231 12.6818C11.1143 12.6818 11.2692 12.5251 11.2692 12.3318V9.53153Z" fill="white" />
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10 5.33203C9.29904 5.33203 8.7308 5.90666 8.7308 6.61549V6.6928C7.63006 6.82826 6.60172 7.13726 5.69459 7.58429C5.52274 7.66898 5.45131 7.87851 5.53506 8.05229C5.61881 8.22607 5.82601 8.2983 5.99786 8.21361C7.14783 7.64691 8.52114 7.31556 10 7.31556C11.4789 7.31556 12.8522 7.64691 14.0021 8.21361C14.174 8.2983 14.3812 8.22607 14.4649 8.05229C14.5487 7.87851 14.4773 7.66898 14.3054 7.58429C13.3983 7.13726 12.37 6.82826 11.2692 6.6928V6.61549C11.2692 5.90666 10.701 5.33203 10 5.33203Z" fill="white" />
                                <title>Delete Payment</title>
                            </svg>
                            
                    }
                    </span>
                </td>
            </tr>
        }
    </table>
</div>

