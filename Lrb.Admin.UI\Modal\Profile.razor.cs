﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace Lrb.Admin.UI.Modal
{
    public partial class Profile
    {

        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public AuthenticationStateProvider Auth { get; set; }
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorageService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        public UserModelDto User { get; set; } = new();
        public AdminUserActivity AdminUser { get; set; } = new();
        public string? Token { get; set; }

        protected override async Task OnInitializedAsync()
        {
            Token = await LocalStorageService.GetItemAsync<string>("token");
            AdminUser = await ActivityService.GetUserByTokenAsync(Token);
            User = await AuthService.GetUser(AdminUser.AdminUserId);
        }
        private async Task NavigateToRegisterPage()
        {
            Navigation.NavigateTo("/register");
        }
        public async Task LogoutTenant()
        {
            Token = await LocalStorage.GetItemAsync<string>("token");
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            AdminUserActivity activityDto = new()
            {
                ActivityTime = DateTime.UtcNow,
                Activity = ActivityConst.Logout,
                AdminUserId = userActivityHistory.AdminUserId,
                TenantId = userActivityHistory.TenantId,
                Token = Token
            };
            await ActivityService.UserActivityAsync(activityDto);
            await LocalStorage.RemoveItemAsync("token");
            Navigation.NavigateTo("");
            await Auth.GetAuthenticationStateAsync();
        }
    }
}
