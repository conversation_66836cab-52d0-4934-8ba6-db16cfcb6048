﻿@page "/vendor"
<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
</head>

<div class="tenantmasterheader">
    <div class="headerText">
        <span class="headername">
            <svg width="160" height="24" viewBox="0 0 160 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.23687 12.2657L12.635 17.2137C12.8794 17.4886 13.334 17.3157 13.334 16.9479L13.334 7.05208C13.334 6.68427 12.8794 6.51143 12.635 6.78633L8.23687 11.7343C8.10215 11.8858 8.10215 12.1142 8.23687 12.2657Z" fill="#292A2B" />
                <path d="M35.94 20L30.34 6H33.18L36.3 14.02C36.46 14.4333 36.6 14.8067 36.72 15.14C36.84 15.46 36.9467 15.7533 37.04 16.02C37.1333 16.2867 37.2133 16.5467 37.28 16.8C37.36 17.0533 37.4467 17.32 37.54 17.6H36.98C37.0733 17.24 37.1733 16.8867 37.28 16.54C37.3867 16.18 37.5067 15.8 37.64 15.4C37.7867 15 37.96 14.54 38.16 14.02L41.08 6H43.94L38.3 20H35.94ZM49.4659 20.2C48.3593 20.2 47.3793 19.9667 46.5259 19.5C45.6859 19.0333 45.0259 18.4 44.5459 17.6C44.0793 16.7867 43.8459 15.86 43.8459 14.82C43.8459 13.9933 43.9793 13.24 44.2459 12.56C44.5126 11.88 44.8793 11.2933 45.3459 10.8C45.8259 10.2933 46.3926 9.90667 47.0459 9.64C47.7126 9.36 48.4393 9.22 49.2259 9.22C49.9193 9.22 50.5659 9.35333 51.1659 9.62C51.7659 9.88667 52.2859 10.2533 52.7259 10.72C53.1659 11.1733 53.4993 11.72 53.7259 12.36C53.9659 12.9867 54.0793 13.6733 54.0659 14.42L54.0459 15.28H45.5059L45.0459 13.6H51.9859L51.6659 13.94V13.5C51.6259 13.0867 51.4926 12.7267 51.2659 12.42C51.0393 12.1 50.7459 11.8533 50.3859 11.68C50.0393 11.4933 49.6526 11.4 49.2259 11.4C48.5726 11.4 48.0193 11.5267 47.5659 11.78C47.1259 12.0333 46.7926 12.4 46.5659 12.88C46.3393 13.3467 46.2259 13.9333 46.2259 14.64C46.2259 15.32 46.3659 15.9133 46.6459 16.42C46.9393 16.9267 47.3459 17.32 47.8659 17.6C48.3993 17.8667 49.0193 18 49.7259 18C50.2193 18 50.6726 17.92 51.0859 17.76C51.4993 17.6 51.9459 17.3133 52.4259 16.9L53.6459 18.6C53.2859 18.9333 52.8726 19.22 52.4059 19.46C51.9526 19.6867 51.4726 19.8667 50.9659 20C50.4593 20.1333 49.9593 20.2 49.4659 20.2ZM56.2447 20V9.46H58.6447L58.6847 11.62L58.2247 11.86C58.358 11.38 58.618 10.9467 59.0047 10.56C59.3914 10.16 59.8514 9.84 60.3847 9.6C60.918 9.36 61.4647 9.24 62.0247 9.24C62.8247 9.24 63.4914 9.4 64.0247 9.72C64.5714 10.04 64.978 10.52 65.2447 11.16C65.5247 11.8 65.6647 12.6 65.6647 13.56V20H63.2047V13.74C63.2047 13.2067 63.1314 12.7667 62.9847 12.42C62.838 12.06 62.6114 11.8 62.3047 11.64C61.998 11.4667 61.6247 11.3867 61.1847 11.4C60.8247 11.4 60.4914 11.46 60.1847 11.58C59.8914 11.6867 59.6314 11.8467 59.4047 12.06C59.1914 12.26 59.018 12.4933 58.8847 12.76C58.7647 13.0267 58.7047 13.32 58.7047 13.64V20H57.4847C57.2447 20 57.018 20 56.8047 20C56.6047 20 56.418 20 56.2447 20ZM72.6722 20.2C71.7389 20.2 70.8989 19.9667 70.1522 19.5C69.4189 19.02 68.8322 18.3733 68.3922 17.56C67.9655 16.7333 67.7522 15.7867 67.7522 14.72C67.7522 13.6667 67.9655 12.7267 68.3922 11.9C68.8189 11.0733 69.3989 10.4267 70.1322 9.96C70.8789 9.48 71.7189 9.24 72.6522 9.24C73.1589 9.24 73.6455 9.32 74.1122 9.48C74.5922 9.64 75.0189 9.86 75.3922 10.14C75.7655 10.4067 76.0589 10.7067 76.2722 11.04C76.4989 11.3733 76.6255 11.72 76.6522 12.08L75.9922 12.16V5.2H78.4522V20H76.0722L76.0122 17.52L76.4922 17.56C76.4789 17.8933 76.3589 18.2133 76.1322 18.52C75.9189 18.8267 75.6389 19.1067 75.2922 19.36C74.9455 19.6133 74.5389 19.82 74.0722 19.98C73.6189 20.1267 73.1522 20.2 72.6722 20.2ZM73.1522 18.12C73.7255 18.12 74.2322 17.9733 74.6722 17.68C75.1122 17.3867 75.4522 16.9867 75.6922 16.48C75.9455 15.9733 76.0722 15.3867 76.0722 14.72C76.0722 14.0667 75.9455 13.4867 75.6922 12.98C75.4522 12.46 75.1122 12.06 74.6722 11.78C74.2322 11.4867 73.7255 11.34 73.1522 11.34C72.5789 11.34 72.0722 11.4867 71.6322 11.78C71.1922 12.06 70.8455 12.46 70.5922 12.98C70.3522 13.4867 70.2322 14.0667 70.2322 14.72C70.2322 15.3867 70.3522 15.9733 70.5922 16.48C70.8455 16.9867 71.1922 17.3867 71.6322 17.68C72.0722 17.9733 72.5789 18.12 73.1522 18.12ZM86.0647 20.2C85.0114 20.2 84.0714 19.9667 83.2447 19.5C82.418 19.02 81.7647 18.3667 81.2847 17.54C80.8047 16.7133 80.5647 15.7733 80.5647 14.72C80.5647 13.6667 80.8047 12.7267 81.2847 11.9C81.7647 11.0733 82.418 10.4267 83.2447 9.96C84.0714 9.48 85.0114 9.24 86.0647 9.24C87.1047 9.24 88.038 9.48 88.8647 9.96C89.6914 10.4267 90.338 11.0733 90.8047 11.9C91.2847 12.7267 91.5247 13.6667 91.5247 14.72C91.5247 15.7733 91.2847 16.7133 90.8047 17.54C90.338 18.3667 89.6914 19.02 88.8647 19.5C88.038 19.9667 87.1047 20.2 86.0647 20.2ZM86.0647 18.02C86.638 18.02 87.1447 17.8733 87.5847 17.58C88.038 17.2867 88.3914 16.8933 88.6447 16.4C88.9114 15.9067 89.038 15.3467 89.0247 14.72C89.038 14.08 88.9114 13.5133 88.6447 13.02C88.3914 12.5267 88.038 12.14 87.5847 11.86C87.1447 11.5667 86.638 11.42 86.0647 11.42C85.4914 11.42 84.9714 11.5667 84.5047 11.86C84.0514 12.14 83.698 12.5333 83.4447 13.04C83.1914 13.5333 83.0647 14.0933 83.0647 14.72C83.0647 15.3467 83.1914 15.9067 83.4447 16.4C83.698 16.8933 84.0514 17.2867 84.5047 17.58C84.9714 17.8733 85.4914 18.02 86.0647 18.02ZM93.7252 20V9.46H96.1452L96.1852 12.82L95.8452 12.06C95.9918 11.5267 96.2452 11.0467 96.6052 10.62C96.9652 10.1933 97.3785 9.86 97.8452 9.62C98.3252 9.36667 98.8252 9.24 99.3452 9.24C99.5718 9.24 99.7852 9.26 99.9852 9.3C100.198 9.34 100.372 9.38667 100.505 9.44L99.8452 12.14C99.6985 12.06 99.5185 11.9933 99.3052 11.94C99.0918 11.8867 98.8785 11.86 98.6652 11.86C98.3318 11.86 98.0118 11.9267 97.7052 12.06C97.4118 12.18 97.1518 12.3533 96.9252 12.58C96.6985 12.8067 96.5185 13.0733 96.3852 13.38C96.2652 13.6733 96.2052 14.0067 96.2052 14.38V20H93.7252ZM111.262 20V8.4H107.262V6H117.982V8.4H113.862V20H111.262ZM122.08 20.2C121.213 20.2 120.427 19.96 119.72 19.48C119.013 19 118.447 18.3467 118.02 17.52C117.593 16.6933 117.38 15.7533 117.38 14.7C117.38 13.6467 117.593 12.7067 118.02 11.88C118.447 11.0533 119.027 10.4067 119.76 9.94C120.493 9.47333 121.32 9.24 122.24 9.24C122.773 9.24 123.26 9.32 123.7 9.48C124.14 9.62667 124.527 9.84 124.86 10.12C125.193 10.4 125.467 10.72 125.68 11.08C125.907 11.44 126.06 11.8267 126.14 12.24L125.6 12.1V9.46H128.08V20H125.58V17.48L126.16 17.38C126.067 17.74 125.893 18.0933 125.64 18.44C125.4 18.7733 125.093 19.0733 124.72 19.34C124.36 19.5933 123.953 19.8 123.5 19.96C123.06 20.12 122.587 20.2 122.08 20.2ZM122.76 18.02C123.333 18.02 123.84 17.88 124.28 17.6C124.72 17.32 125.06 16.9333 125.3 16.44C125.553 15.9333 125.68 15.3533 125.68 14.7C125.68 14.06 125.553 13.4933 125.3 13C125.06 12.5067 124.72 12.12 124.28 11.84C123.84 11.56 123.333 11.42 122.76 11.42C122.187 11.42 121.68 11.56 121.24 11.84C120.813 12.12 120.48 12.5067 120.24 13C120 13.4933 119.88 14.06 119.88 14.7C119.88 15.3533 120 15.9333 120.24 16.44C120.48 16.9333 120.813 17.32 121.24 17.6C121.68 17.88 122.187 18.02 122.76 18.02ZM136.752 20.2C136.286 20.2 135.826 20.1333 135.372 20C134.932 19.8533 134.532 19.66 134.172 19.42C133.812 19.18 133.512 18.9133 133.272 18.62C133.032 18.3133 132.872 18.0067 132.792 17.7L133.372 17.44L133.312 19.96H130.932V5.2H133.392V11.9L132.952 11.7C133.019 11.38 133.166 11.08 133.392 10.8C133.632 10.5067 133.926 10.2467 134.272 10.02C134.619 9.78 134.999 9.59333 135.412 9.46C135.826 9.31333 136.246 9.24 136.672 9.24C137.619 9.24 138.452 9.47333 139.172 9.94C139.906 10.4067 140.479 11.0533 140.892 11.88C141.319 12.7067 141.532 13.6467 141.532 14.7C141.532 15.7667 141.326 16.7133 140.912 17.54C140.499 18.3667 139.926 19.02 139.192 19.5C138.472 19.9667 137.659 20.2 136.752 20.2ZM136.232 18.04C136.792 18.04 137.292 17.9 137.732 17.62C138.172 17.3267 138.519 16.9333 138.772 16.44C139.026 15.9333 139.152 15.3533 139.152 14.7C139.152 14.06 139.026 13.4933 138.772 13C138.532 12.5067 138.192 12.12 137.752 11.84C137.312 11.56 136.806 11.42 136.232 11.42C135.659 11.42 135.152 11.56 134.712 11.84C134.272 12.12 133.926 12.5067 133.672 13C133.419 13.4933 133.292 14.06 133.292 14.7C133.292 15.3533 133.419 15.9333 133.672 16.44C133.926 16.9333 134.272 17.3267 134.712 17.62C135.152 17.9 135.659 18.04 136.232 18.04ZM143.745 20V5.2H146.225V20H143.745ZM153.86 20.2C152.754 20.2 151.774 19.9667 150.92 19.5C150.08 19.0333 149.42 18.4 148.94 17.6C148.474 16.7867 148.24 15.86 148.24 14.82C148.24 13.9933 148.374 13.24 148.64 12.56C148.907 11.88 149.274 11.2933 149.74 10.8C150.22 10.2933 150.787 9.90667 151.44 9.64C152.107 9.36 152.834 9.22 153.62 9.22C154.314 9.22 154.96 9.35333 155.56 9.62C156.16 9.88667 156.68 10.2533 157.12 10.72C157.56 11.1733 157.894 11.72 158.12 12.36C158.36 12.9867 158.474 13.6733 158.46 14.42L158.44 15.28H149.9L149.44 13.6H156.38L156.06 13.94V13.5C156.02 13.0867 155.887 12.7267 155.66 12.42C155.434 12.1 155.14 11.8533 154.78 11.68C154.434 11.4933 154.047 11.4 153.62 11.4C152.967 11.4 152.414 11.5267 151.96 11.78C151.52 12.0333 151.187 12.4 150.96 12.88C150.734 13.3467 150.62 13.9333 150.62 14.64C150.62 15.32 150.76 15.9133 151.04 16.42C151.334 16.9267 151.74 17.32 152.26 17.6C152.794 17.8667 153.414 18 154.12 18C154.614 18 155.067 17.92 155.48 17.76C155.894 17.6 156.34 17.3133 156.82 16.9L158.04 18.6C157.68 18.9333 157.267 19.22 156.8 19.46C156.347 19.6867 155.867 19.8667 155.36 20C154.854 20.1333 154.354 20.2 153.86 20.2Z" fill="#292A2B" />
            </svg>
        </span>
    </div>
    <div class="headerbuttons">
        <span @onclick="async () => { Filter = new(); Paginate(1); await GetVendorData(); }">
            <svg width="89" height="38" viewBox="0 0 89 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="89" height="38" rx="4" fill="#292A2B" />
                <path d="M16.4396 24.4261C16.5371 24.4727 16.6439 24.4967 16.752 24.4964C16.9183 24.4964 17.0796 24.4398 17.2094 24.3359C17.3392 24.232 17.4298 24.0869 17.4662 23.9247C17.5026 23.7625 17.4827 23.5926 17.4097 23.4432C17.3367 23.2938 17.215 23.1737 17.0647 23.1027C16.1347 22.6641 15.3813 21.9225 14.928 20.9996C14.4747 20.0767 14.3484 19.0272 14.5698 18.023C14.7912 17.0189 15.3471 16.1198 16.1465 15.4731C16.616 15.0932 17.1531 14.8137 17.7247 14.6462L17.1747 15.7462C17.119 15.8577 17.0927 15.9816 17.0984 16.1061C17.104 16.2307 17.1413 16.3517 17.2068 16.4577C17.2723 16.5638 17.3638 16.6513 17.4726 16.7121C17.5815 16.7728 17.704 16.8048 17.8287 16.8049C17.9648 16.8055 18.0983 16.7678 18.2141 16.6963C18.3299 16.6247 18.4233 16.5222 18.4838 16.4002L19.6545 14.0587C19.7412 13.8851 19.7554 13.6842 19.694 13.5001C19.6326 13.3161 19.5005 13.1639 19.3269 13.0772C19.2197 13.0236 19.102 12.9977 18.9843 13.0003C18.9763 13 18.9683 13 18.9603 13C17.6004 13.009 16.2839 13.4797 15.2266 14.335C14.1693 15.1902 13.4338 16.3793 13.1409 17.7073C12.8479 19.0353 13.0148 20.4234 13.6141 21.6441C14.2135 22.8648 15.2097 23.8457 16.4396 24.4261Z" fill="white" />
                <path d="M19.0193 24.9998C19.0246 24.9999 19.0299 25 19.0352 25H19.0397C20.3996 24.991 21.7161 24.5203 22.7734 23.665C23.8307 22.8098 24.5662 21.6207 24.8591 20.2927C25.1521 18.9647 24.9852 17.5766 24.3859 16.3559C23.7865 15.1351 22.7903 14.1542 21.5604 13.5739C21.463 13.5272 21.3563 13.503 21.2483 13.503C21.1095 13.5036 20.9737 13.5433 20.8565 13.6176C20.7393 13.692 20.6454 13.7979 20.5858 13.9232C20.5448 14.0101 20.5213 14.1042 20.5167 14.2001C20.5121 14.2961 20.5265 14.392 20.5589 14.4824C20.5914 14.5728 20.6414 14.656 20.706 14.727C20.7707 14.7981 20.8487 14.8558 20.9356 14.8967C21.8655 15.3354 22.6188 16.0771 23.0721 17C23.5253 17.923 23.6516 18.9725 23.4302 19.9767C23.2088 20.9808 22.6528 21.8799 21.8535 22.5267C21.384 22.9066 20.847 23.1861 20.2753 23.3536L20.8252 22.2538C20.912 22.0802 20.9262 21.8793 20.8647 21.6952C20.8033 21.5112 20.6713 21.359 20.4977 21.2723C20.3241 21.1856 20.1232 21.1714 19.9391 21.2328C19.755 21.2943 19.6029 21.4263 19.5162 21.5999L18.3454 23.9413C18.2898 24.0528 18.2635 24.1767 18.2691 24.3013C18.2747 24.4258 18.312 24.5468 18.3775 24.6528C18.443 24.7589 18.5346 24.8464 18.6434 24.9072C18.7522 24.968 18.8748 24.9999 18.9994 25C19.006 25 19.0127 25 19.0193 24.9998Z" fill="white" />
                <path d="M35.128 24V15.6H38.776C39.28 15.6 39.74 15.72 40.156 15.96C40.572 16.192 40.9 16.512 41.14 16.92C41.388 17.32 41.512 17.772 41.512 18.276C41.512 18.756 41.388 19.2 41.14 19.608C40.9 20.008 40.572 20.328 40.156 20.568C39.748 20.8 39.288 20.916 38.776 20.916H36.652V24H35.128ZM40 24L37.864 20.208L39.472 19.908L41.848 24.012L40 24ZM36.652 19.56H38.788C39.02 19.56 39.22 19.508 39.388 19.404C39.564 19.292 39.7 19.14 39.796 18.948C39.892 18.756 39.94 18.544 39.94 18.312C39.94 18.048 39.88 17.82 39.76 17.628C39.64 17.436 39.472 17.284 39.256 17.172C39.04 17.06 38.792 17.004 38.512 17.004H36.652V19.56ZM45.9608 24.12C45.2968 24.12 44.7088 23.98 44.1968 23.7C43.6928 23.42 43.2968 23.04 43.0088 22.56C42.7288 22.072 42.5888 21.516 42.5888 20.892C42.5888 20.396 42.6688 19.944 42.8288 19.536C42.9888 19.128 43.2088 18.776 43.4888 18.48C43.7768 18.176 44.1168 17.944 44.5088 17.784C44.9088 17.616 45.3448 17.532 45.8168 17.532C46.2328 17.532 46.6208 17.612 46.9808 17.772C47.3408 17.932 47.6528 18.152 47.9168 18.432C48.1808 18.704 48.3808 19.032 48.5168 19.416C48.6608 19.792 48.7288 20.204 48.7208 20.652L48.7088 21.168H43.5848L43.3088 20.16H47.4728L47.2808 20.364V20.1C47.2568 19.852 47.1768 19.636 47.0408 19.452C46.9048 19.26 46.7288 19.112 46.5128 19.008C46.3048 18.896 46.0728 18.84 45.8168 18.84C45.4248 18.84 45.0928 18.916 44.8208 19.068C44.5568 19.22 44.3568 19.44 44.2208 19.728C44.0848 20.008 44.0168 20.36 44.0168 20.784C44.0168 21.192 44.1008 21.548 44.2688 21.852C44.4448 22.156 44.6888 22.392 45.0008 22.56C45.3208 22.72 45.6928 22.8 46.1168 22.8C46.4128 22.8 46.6848 22.752 46.9328 22.656C47.1808 22.56 47.4488 22.388 47.7368 22.14L48.4688 23.16C48.2528 23.36 48.0048 23.532 47.7248 23.676C47.4528 23.812 47.1648 23.92 46.8608 24C46.5568 24.08 46.2568 24.12 45.9608 24.12ZM50.6041 24V17.112C50.6041 16.728 50.6881 16.388 50.8561 16.092C51.0241 15.788 51.2561 15.552 51.5521 15.384C51.8481 15.208 52.1921 15.12 52.5841 15.12C52.8561 15.12 53.1081 15.168 53.3401 15.264C53.5721 15.352 53.7681 15.476 53.9281 15.636L53.4721 16.752C53.3681 16.664 53.2561 16.596 53.1361 16.548C53.0241 16.5 52.9161 16.476 52.8121 16.476C52.6521 16.476 52.5161 16.504 52.4041 16.56C52.3001 16.608 52.2201 16.684 52.1641 16.788C52.1161 16.892 52.0921 17.016 52.0921 17.16V24H51.3481C51.2041 24 51.0681 24 50.9401 24C50.8201 24 50.7081 24 50.6041 24ZM49.6201 19.104V17.808H53.5201V19.104H49.6201ZM54.739 24V17.676H56.191L56.215 19.692L56.011 19.236C56.099 18.916 56.251 18.628 56.467 18.372C56.683 18.116 56.931 17.916 57.211 17.772C57.499 17.62 57.799 17.544 58.111 17.544C58.247 17.544 58.375 17.556 58.495 17.58C58.623 17.604 58.727 17.632 58.807 17.664L58.411 19.284C58.323 19.236 58.215 19.196 58.087 19.164C57.959 19.132 57.831 19.116 57.703 19.116C57.503 19.116 57.311 19.156 57.127 19.236C56.951 19.308 56.795 19.412 56.659 19.548C56.523 19.684 56.415 19.844 56.335 20.028C56.263 20.204 56.227 20.404 56.227 20.628V24H54.739ZM62.4843 24.12C61.8203 24.12 61.2323 23.98 60.7203 23.7C60.2163 23.42 59.8203 23.04 59.5323 22.56C59.2523 22.072 59.1123 21.516 59.1123 20.892C59.1123 20.396 59.1923 19.944 59.3523 19.536C59.5123 19.128 59.7323 18.776 60.0123 18.48C60.3003 18.176 60.6403 17.944 61.0323 17.784C61.4323 17.616 61.8683 17.532 62.3403 17.532C62.7563 17.532 63.1443 17.612 63.5043 17.772C63.8643 17.932 64.1763 18.152 64.4403 18.432C64.7043 18.704 64.9043 19.032 65.0403 19.416C65.1843 19.792 65.2523 20.204 65.2443 20.652L65.2323 21.168H60.1083L59.8323 20.16H63.9963L63.8043 20.364V20.1C63.7803 19.852 63.7003 19.636 63.5643 19.452C63.4283 19.26 63.2523 19.112 63.0363 19.008C62.8283 18.896 62.5963 18.84 62.3403 18.84C61.9483 18.84 61.6163 18.916 61.3443 19.068C61.0803 19.22 60.8803 19.44 60.7443 19.728C60.6083 20.008 60.5403 20.36 60.5403 20.784C60.5403 21.192 60.6243 21.548 60.7923 21.852C60.9683 22.156 61.2123 22.392 61.5243 22.56C61.8443 22.72 62.2163 22.8 62.6403 22.8C62.9363 22.8 63.2083 22.752 63.4563 22.656C63.7043 22.56 63.9723 22.388 64.2603 22.14L64.9922 23.16C64.7763 23.36 64.5283 23.532 64.2483 23.676C63.9763 23.812 63.6883 23.92 63.3843 24C63.0803 24.08 62.7803 24.12 62.4843 24.12ZM68.6635 24.12C68.1035 24.12 67.5995 24.028 67.1515 23.844C66.7115 23.652 66.3515 23.38 66.0715 23.028L67.0315 22.2C67.2715 22.472 67.5395 22.668 67.8355 22.788C68.1315 22.908 68.4435 22.968 68.7715 22.968C68.9075 22.968 69.0275 22.952 69.1315 22.92C69.2435 22.888 69.3395 22.84 69.4195 22.776C69.4995 22.712 69.5595 22.64 69.5995 22.56C69.6475 22.472 69.6715 22.376 69.6715 22.272C69.6715 22.08 69.5995 21.928 69.4555 21.816C69.3755 21.76 69.2475 21.7 69.0715 21.636C68.9035 21.572 68.6835 21.508 68.4115 21.444C67.9795 21.332 67.6195 21.204 67.3315 21.06C67.0435 20.908 66.8195 20.74 66.6595 20.556C66.5235 20.404 66.4195 20.232 66.3475 20.04C66.2835 19.848 66.2515 19.64 66.2515 19.416C66.2515 19.136 66.3115 18.884 66.4315 18.66C66.5595 18.428 66.7315 18.228 66.9475 18.06C67.1635 17.892 67.4155 17.764 67.7035 17.676C67.9915 17.588 68.2915 17.544 68.6035 17.544C68.9235 17.544 69.2315 17.584 69.5275 17.664C69.8315 17.744 70.1115 17.86 70.3675 18.012C70.6315 18.156 70.8555 18.332 71.0395 18.54L70.2235 19.44C70.0715 19.296 69.9035 19.168 69.7195 19.056C69.5435 18.944 69.3635 18.856 69.1795 18.792C68.9955 18.72 68.8235 18.684 68.6635 18.684C68.5115 18.684 68.3755 18.7 68.2555 18.732C68.1355 18.756 68.0355 18.796 67.9555 18.852C67.8755 18.908 67.8115 18.98 67.7635 19.068C67.7235 19.148 67.7035 19.244 67.7035 19.356C67.7115 19.452 67.7355 19.544 67.7755 19.632C67.8235 19.712 67.8875 19.78 67.9675 19.836C68.0555 19.892 68.1875 19.956 68.3635 20.028C68.5395 20.1 68.7675 20.168 69.0475 20.232C69.4395 20.336 69.7675 20.452 70.0315 20.58C70.2955 20.708 70.5035 20.856 70.6555 21.024C70.8075 21.168 70.9155 21.336 70.9795 21.528C71.0435 21.72 71.0755 21.932 71.0755 22.164C71.0755 22.54 70.9675 22.876 70.7515 23.172C70.5435 23.468 70.2555 23.7 69.8875 23.868C69.5275 24.036 69.1195 24.12 68.6635 24.12ZM72.3757 24V15.12H73.8397V18.972L73.5637 19.116C73.6437 18.828 73.7997 18.568 74.0317 18.336C74.2637 18.096 74.5397 17.904 74.8597 17.76C75.1797 17.616 75.5077 17.544 75.8437 17.544C76.3237 17.544 76.7237 17.64 77.0437 17.832C77.3717 18.024 77.6157 18.312 77.7757 18.696C77.9437 19.08 78.0277 19.56 78.0277 20.136V24H76.5517V20.244C76.5517 19.924 76.5077 19.66 76.4197 19.452C76.3317 19.236 76.1957 19.08 76.0117 18.984C75.8277 18.88 75.6037 18.832 75.3397 18.84C75.1237 18.84 74.9237 18.876 74.7397 18.948C74.5637 19.012 74.4077 19.108 74.2717 19.236C74.1437 19.356 74.0397 19.496 73.9597 19.656C73.8877 19.816 73.8517 19.992 73.8517 20.184V24H73.1197C72.9757 24 72.8397 24 72.7117 24C72.5917 24 72.4797 24 72.3757 24Z" fill="white" />
            </svg>
        </span>
        <span onclick="@(()=> Navigation.NavigateTo($"add-vendor"))">
            <svg width="144" height="38" viewBox="0 0 144 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="144" height="38" rx="4" fill="#292A2B" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26 19C26 22.866 22.866 26 19 26C15.134 26 12 22.866 12 19C12 15.134 15.134 12 19 12C22.866 12 26 15.134 26 19ZM19 15.675C19.2899 15.675 19.525 15.9101 19.525 16.2V18.475H21.8C22.0899 18.475 22.325 18.7101 22.325 19C22.325 19.2899 22.0899 19.525 21.8 19.525H19.525V21.8C19.525 22.0899 19.2899 22.325 19 22.325C18.7101 22.325 18.475 22.0899 18.475 21.8V19.525H16.2C15.9101 19.525 15.675 19.2899 15.675 19C15.675 18.7101 15.9101 18.475 16.2 18.475H18.475V16.2C18.475 15.9101 18.7101 15.675 19 15.675Z" fill="white" />
                <path d="M34.192 24L37.468 15.6H38.932L42.184 24H40.564L38.764 19.224C38.724 19.128 38.668 18.976 38.596 18.768C38.532 18.56 38.46 18.336 38.38 18.096C38.3 17.848 38.228 17.62 38.164 17.412C38.1 17.196 38.052 17.04 38.02 16.944L38.32 16.932C38.272 17.092 38.216 17.272 38.152 17.472C38.088 17.672 38.02 17.88 37.948 18.096C37.876 18.312 37.804 18.52 37.732 18.72C37.668 18.92 37.608 19.1 37.552 19.26L35.752 24H34.192ZM35.68 22.08L36.22 20.724H40.048L40.624 22.08H35.68ZM45.5525 24.12C44.9925 24.12 44.4885 23.98 44.0405 23.7C43.6005 23.412 43.2485 23.024 42.9845 22.536C42.7285 22.04 42.6005 21.472 42.6005 20.832C42.6005 20.2 42.7285 19.636 42.9845 19.14C43.2405 18.644 43.5885 18.256 44.0285 17.976C44.4765 17.688 44.9805 17.544 45.5405 17.544C45.8445 17.544 46.1365 17.592 46.4165 17.688C46.7045 17.784 46.9605 17.916 47.1845 18.084C47.4085 18.244 47.5845 18.424 47.7125 18.624C47.8485 18.824 47.9245 19.032 47.9405 19.248L47.5445 19.296V15.12H49.0205V24H47.5925L47.5565 22.512L47.8445 22.536C47.8365 22.736 47.7645 22.928 47.6285 23.112C47.5005 23.296 47.3325 23.464 47.1245 23.616C46.9165 23.768 46.6725 23.892 46.3925 23.988C46.1205 24.076 45.8405 24.12 45.5525 24.12ZM45.8405 22.872C46.1845 22.872 46.4885 22.784 46.7525 22.608C47.0165 22.432 47.2205 22.192 47.3645 21.888C47.5165 21.584 47.5925 21.232 47.5925 20.832C47.5925 20.44 47.5165 20.092 47.3645 19.788C47.2205 19.476 47.0165 19.236 46.7525 19.068C46.4885 18.892 46.1845 18.804 45.8405 18.804C45.4965 18.804 45.1925 18.892 44.9285 19.068C44.6645 19.236 44.4565 19.476 44.3045 19.788C44.1605 20.092 44.0885 20.44 44.0885 20.832C44.0885 21.232 44.1605 21.584 44.3045 21.888C44.4565 22.192 44.6645 22.432 44.9285 22.608C45.1925 22.784 45.4965 22.872 45.8405 22.872ZM53.24 24.12C52.68 24.12 52.176 23.98 51.728 23.7C51.288 23.412 50.936 23.024 50.672 22.536C50.416 22.04 50.288 21.472 50.288 20.832C50.288 20.2 50.416 19.636 50.672 19.14C50.928 18.644 51.276 18.256 51.716 17.976C52.164 17.688 52.668 17.544 53.228 17.544C53.532 17.544 53.824 17.592 54.104 17.688C54.392 17.784 54.648 17.916 54.872 18.084C55.096 18.244 55.272 18.424 55.4 18.624C55.536 18.824 55.612 19.032 55.628 19.248L55.232 19.296V15.12H56.708V24H55.28L55.244 22.512L55.532 22.536C55.524 22.736 55.452 22.928 55.316 23.112C55.188 23.296 55.02 23.464 54.812 23.616C54.604 23.768 54.36 23.892 54.08 23.988C53.808 24.076 53.528 24.12 53.24 24.12ZM53.528 22.872C53.872 22.872 54.176 22.784 54.44 22.608C54.704 22.432 54.908 22.192 55.052 21.888C55.204 21.584 55.28 21.232 55.28 20.832C55.28 20.44 55.204 20.092 55.052 19.788C54.908 19.476 54.704 19.236 54.44 19.068C54.176 18.892 53.872 18.804 53.528 18.804C53.184 18.804 52.88 18.892 52.616 19.068C52.352 19.236 52.144 19.476 51.992 19.788C51.848 20.092 51.776 20.44 51.776 20.832C51.776 21.232 51.848 21.584 51.992 21.888C52.144 22.192 52.352 22.432 52.616 22.608C52.88 22.784 53.184 22.872 53.528 22.872ZM62.1749 24V15.6H63.5789L68.4509 22.164L68.1869 22.212C68.1549 21.988 68.1269 21.76 68.1029 21.528C68.0789 21.288 68.0549 21.04 68.0309 20.784C68.0149 20.528 67.9989 20.26 67.9829 19.98C67.9749 19.7 67.9669 19.408 67.9589 19.104C67.9509 18.792 67.9469 18.464 67.9469 18.12V15.6H69.4949V24H68.0669L63.1709 17.532L63.4829 17.448C63.5229 17.896 63.5549 18.28 63.5789 18.6C63.6109 18.912 63.6349 19.184 63.6509 19.416C63.6669 19.64 63.6789 19.828 63.6869 19.98C63.7029 20.132 63.7109 20.272 63.7109 20.4C63.7189 20.52 63.7229 20.636 63.7229 20.748V24H62.1749ZM74.4491 24.12C73.7851 24.12 73.1971 23.98 72.6851 23.7C72.1811 23.42 71.7851 23.04 71.4971 22.56C71.2171 22.072 71.0771 21.516 71.0771 20.892C71.0771 20.396 71.1571 19.944 71.3171 19.536C71.4771 19.128 71.6971 18.776 71.9771 18.48C72.2651 18.176 72.6051 17.944 72.9971 17.784C73.3971 17.616 73.8331 17.532 74.3051 17.532C74.7211 17.532 75.1091 17.612 75.4691 17.772C75.8291 17.932 76.1411 18.152 76.4051 18.432C76.6691 18.704 76.8691 19.032 77.0051 19.416C77.1491 19.792 77.2171 20.204 77.2091 20.652L77.1971 21.168H72.0731L71.7971 20.16H75.9611L75.7691 20.364V20.1C75.7451 19.852 75.6651 19.636 75.5291 19.452C75.3931 19.26 75.2171 19.112 75.0011 19.008C74.7931 18.896 74.5611 18.84 74.3051 18.84C73.9131 18.84 73.5811 18.916 73.3091 19.068C73.0451 19.22 72.8451 19.44 72.7091 19.728C72.5731 20.008 72.5051 20.36 72.5051 20.784C72.5051 21.192 72.5891 21.548 72.7571 21.852C72.9331 22.156 73.1771 22.392 73.4891 22.56C73.8091 22.72 74.1811 22.8 74.6051 22.8C74.9011 22.8 75.1731 22.752 75.4211 22.656C75.6691 22.56 75.9371 22.388 76.2251 22.14L76.9571 23.16C76.7411 23.36 76.4931 23.532 76.2131 23.676C75.9411 23.812 75.6531 23.92 75.3491 24C75.0451 24.08 74.7451 24.12 74.4491 24.12ZM79.8872 24L77.7632 17.676L79.2752 17.664L80.6912 22.176L80.4272 22.152L81.9632 18.624H82.8512L84.4232 22.152L84.1112 22.188L85.5392 17.676H87.0512L84.9152 24H83.8592L82.2392 20.208L82.4672 20.232L80.9312 24H79.8872ZM94.3648 24L91.0048 15.6H92.7088L94.5808 20.412C94.6768 20.66 94.7608 20.884 94.8328 21.084C94.9048 21.276 94.9688 21.452 95.0248 21.612C95.0808 21.772 95.1288 21.928 95.1688 22.08C95.2168 22.232 95.2688 22.392 95.3248 22.56H94.9888C95.0448 22.344 95.1048 22.132 95.1688 21.924C95.2328 21.708 95.3048 21.48 95.3848 21.24C95.4728 21 95.5768 20.724 95.6968 20.412L97.4488 15.6H99.1648L95.7808 24H94.3648ZM102.48 24.12C101.816 24.12 101.228 23.98 100.716 23.7C100.212 23.42 99.8163 23.04 99.5283 22.56C99.2483 22.072 99.1083 21.516 99.1083 20.892C99.1083 20.396 99.1883 19.944 99.3483 19.536C99.5083 19.128 99.7283 18.776 100.008 18.48C100.296 18.176 100.636 17.944 101.028 17.784C101.428 17.616 101.864 17.532 102.336 17.532C102.752 17.532 103.14 17.612 103.5 17.772C103.86 17.932 104.172 18.152 104.436 18.432C104.7 18.704 104.9 19.032 105.036 19.416C105.18 19.792 105.248 20.204 105.24 20.652L105.228 21.168H100.104L99.8283 20.16H103.992L103.8 20.364V20.1C103.776 19.852 103.696 19.636 103.56 19.452C103.424 19.26 103.248 19.112 103.032 19.008C102.824 18.896 102.592 18.84 102.336 18.84C101.944 18.84 101.612 18.916 101.34 19.068C101.076 19.22 100.876 19.44 100.74 19.728C100.604 20.008 100.536 20.36 100.536 20.784C100.536 21.192 100.62 21.548 100.788 21.852C100.964 22.156 101.208 22.392 101.52 22.56C101.84 22.72 102.212 22.8 102.636 22.8C102.932 22.8 103.204 22.752 103.452 22.656C103.7 22.56 103.968 22.388 104.256 22.14L104.988 23.16C104.772 23.36 104.524 23.532 104.244 23.676C103.972 23.812 103.684 23.92 103.38 24C103.076 24.08 102.776 24.12 102.48 24.12ZM106.548 24V17.676H107.988L108.012 18.972L107.736 19.116C107.816 18.828 107.972 18.568 108.204 18.336C108.436 18.096 108.712 17.904 109.032 17.76C109.352 17.616 109.68 17.544 110.016 17.544C110.496 17.544 110.896 17.64 111.216 17.832C111.544 18.024 111.788 18.312 111.948 18.696C112.116 19.08 112.2 19.56 112.2 20.136V24H110.724V20.244C110.724 19.924 110.68 19.66 110.592 19.452C110.504 19.236 110.368 19.08 110.184 18.984C110 18.88 109.776 18.832 109.512 18.84C109.296 18.84 109.096 18.876 108.912 18.948C108.736 19.012 108.58 19.108 108.444 19.236C108.316 19.356 108.212 19.496 108.132 19.656C108.06 19.816 108.024 19.992 108.024 20.184V24H107.292C107.148 24 107.012 24 106.884 24C106.764 24 106.652 24 106.548 24ZM116.404 24.12C115.844 24.12 115.34 23.98 114.892 23.7C114.452 23.412 114.1 23.024 113.836 22.536C113.58 22.04 113.452 21.472 113.452 20.832C113.452 20.2 113.58 19.636 113.836 19.14C114.092 18.644 114.44 18.256 114.88 17.976C115.328 17.688 115.832 17.544 116.392 17.544C116.696 17.544 116.988 17.592 117.268 17.688C117.556 17.784 117.812 17.916 118.036 18.084C118.26 18.244 118.436 18.424 118.564 18.624C118.7 18.824 118.776 19.032 118.792 19.248L118.396 19.296V15.12H119.872V24H118.444L118.408 22.512L118.696 22.536C118.688 22.736 118.616 22.928 118.48 23.112C118.352 23.296 118.184 23.464 117.976 23.616C117.768 23.768 117.524 23.892 117.244 23.988C116.972 24.076 116.692 24.12 116.404 24.12ZM116.692 22.872C117.036 22.872 117.34 22.784 117.604 22.608C117.868 22.432 118.072 22.192 118.216 21.888C118.368 21.584 118.444 21.232 118.444 20.832C118.444 20.44 118.368 20.092 118.216 19.788C118.072 19.476 117.868 19.236 117.604 19.068C117.34 18.892 117.036 18.804 116.692 18.804C116.348 18.804 116.044 18.892 115.78 19.068C115.516 19.236 115.308 19.476 115.156 19.788C115.012 20.092 114.94 20.44 114.94 20.832C114.94 21.232 115.012 21.584 115.156 21.888C115.308 22.192 115.516 22.432 115.78 22.608C116.044 22.784 116.348 22.872 116.692 22.872ZM124.44 24.12C123.808 24.12 123.244 23.98 122.748 23.7C122.252 23.412 121.86 23.02 121.572 22.524C121.284 22.028 121.14 21.464 121.14 20.832C121.14 20.2 121.284 19.636 121.572 19.14C121.86 18.644 122.252 18.256 122.748 17.976C123.244 17.688 123.808 17.544 124.44 17.544C125.064 17.544 125.624 17.688 126.12 17.976C126.616 18.256 127.004 18.644 127.284 19.14C127.572 19.636 127.716 20.2 127.716 20.832C127.716 21.464 127.572 22.028 127.284 22.524C127.004 23.02 126.616 23.412 126.12 23.7C125.624 23.98 125.064 24.12 124.44 24.12ZM124.44 22.812C124.784 22.812 125.088 22.724 125.352 22.548C125.624 22.372 125.836 22.136 125.988 21.84C126.148 21.544 126.224 21.208 126.216 20.832C126.224 20.448 126.148 20.108 125.988 19.812C125.836 19.516 125.624 19.284 125.352 19.116C125.088 18.94 124.784 18.852 124.44 18.852C124.096 18.852 123.784 18.94 123.504 19.116C123.232 19.284 123.02 19.52 122.868 19.824C122.716 20.12 122.64 20.456 122.64 20.832C122.64 21.208 122.716 21.544 122.868 21.84C123.02 22.136 123.232 22.372 123.504 22.548C123.784 22.724 124.096 22.812 124.44 22.812ZM129.036 24V17.676H130.488L130.512 19.692L130.308 19.236C130.396 18.916 130.548 18.628 130.764 18.372C130.98 18.116 131.228 17.916 131.508 17.772C131.796 17.62 132.096 17.544 132.408 17.544C132.544 17.544 132.672 17.556 132.792 17.58C132.92 17.604 133.024 17.632 133.104 17.664L132.708 19.284C132.62 19.236 132.512 19.196 132.384 19.164C132.256 19.132 132.128 19.116 132 19.116C131.8 19.116 131.608 19.156 131.424 19.236C131.248 19.308 131.092 19.412 130.956 19.548C130.82 19.684 130.712 19.844 130.632 20.028C130.56 20.204 130.524 20.404 130.524 20.628V24H129.036Z" fill="white" />
            </svg>
        </span>
    </div>
</div>

<div class="TenantMasterTable">
    <div class="TenantMasterTableHeader">
        <div class="SearchBar">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="searchicon">
                <path d="M16.6673 16.6654L15.334 15.332M3.33398 9.66537C3.33398 6.16756 6.16951 3.33203 9.66732 3.33203C13.1651 3.33203 16.0006 6.16756 16.0006 9.66537C16.0006 13.1632 13.1651 15.9987 9.66732 15.9987C6.16951 15.9987 3.33398 13.1632 3.33398 9.66537Z" stroke="#BBBBBB" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <input type="text" placeholder="Press Enter to Search" class="Searchbarbar" @bind-value="Filter.SearchByContact" @onkeyup="GetVendorData" />
        </div>
        <div class="buttons">
            <span class="buttonbutton" title="Page Size">
                <select id="pagesizeinput" @bind="PageSize" @bind:after="GetVendorData">
                    @foreach (var dv in count)
                    {
                        <option value="@dv">@dv</option>
                    }
                </select>
            </span>
        </div>
    </div>
    @if (Collection == null)
    {
        <span>No Data Found</span>
    }
    else
    {
        @if (Collection.Any(i => i != null))
        {
            <div class="all-tables">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Sl.No</th>
                                <th>Created Date</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Collection.Select((value, i) => new { value, i }))
                            {
                                <tr>
                                    <td>@(((PageNumber - 1) * PageSize) + @item.i + 1)</td>
                                    <td>@item.value?.CreatedOn</td>
                                    <td>@item.value?.FirstName @item.value?.LastName</td>
                                    <td>@item.value?.PhoneNumber</td>
                                    @* <td>
                        <span class="oi oi-eye" @onclick="@(() => Navigation.NavigateTo($"/tenant/{item.value.TenantId}"))"></span>
                        </td> *@
                                    <td>
                                        <span @onclick="@(() => Navigation.NavigateTo($"edit-vendor/{item.value.Id}"))" class="edit_delelte">
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="20" height="20" rx="4" fill="#78AECC" />
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9898 5.68311C11.4553 5.215 12.2099 5.215 12.6753 5.68311L13.518 6.5307C13.9835 6.9988 13.9835 7.75776 13.518 8.22586L12.7708 8.97736C12.7122 8.94815 12.6505 8.91656 12.5865 8.88268C12.1558 8.65462 11.6447 8.33455 11.2632 7.95091C10.8818 7.56727 10.5635 7.05323 10.3368 6.62005C10.3031 6.55568 10.2717 6.4936 10.2426 6.4346L10.9898 5.68311ZM10.7164 8.50088C11.1678 8.95485 11.7418 9.31215 12.1964 9.55506L9.25808 12.5103C9.07573 12.6937 8.83916 12.8127 8.58388 12.8494L6.81413 13.1036C6.42085 13.1601 6.08375 12.8211 6.13994 12.4256L6.39276 10.6456C6.42923 10.3889 6.54751 10.151 6.72986 9.96757L9.66823 7.0123C9.90975 7.46954 10.265 8.0469 10.7164 8.50088ZM8.96934 13.8876C8.75579 13.8876 8.58268 14.0617 8.58268 14.2765C8.58268 14.4913 8.75579 14.6654 8.96934 14.6654H13.4805C13.694 14.6654 13.8671 14.4913 13.8671 14.2765C13.8671 14.0617 13.694 13.8876 13.4805 13.8876H8.96934Z" fill="white" />
                                            </svg>

                                        </span>
                                        <span @onclick="@( async () => { RemoveVendorAsync(item.value.Id); await GetVendorData();})" class="edit_delelte">
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="20" height="20" rx="4" fill="#EF595C" />
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0502 14.6654C12.0188 14.6654 12.8227 13.9085 12.8917 12.9315L13.2093 8.43469C12.2321 8.09011 11.1452 7.89804 10 7.89804C8.85484 7.89804 7.76794 8.09011 6.79073 8.43469L7.10837 12.9315C7.17737 13.9085 7.98125 14.6654 8.9498 14.6654H11.0502ZM9.07696 9.1815C9.26813 9.1815 9.42311 9.33821 9.42311 9.53153V12.3318C9.42311 12.5251 9.26813 12.6818 9.07696 12.6818C8.88579 12.6818 8.73081 12.5251 8.73081 12.3318V9.53153C8.73081 9.33821 8.88579 9.1815 9.07696 9.1815ZM11.2692 9.53153C11.2692 9.33821 11.1143 9.1815 10.9231 9.1815C10.7319 9.1815 10.5769 9.33821 10.5769 9.53153V12.3318C10.5769 12.5251 10.7319 12.6818 10.9231 12.6818C11.1143 12.6818 11.2692 12.5251 11.2692 12.3318V9.53153Z" fill="white" />
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10 5.33203C9.29904 5.33203 8.7308 5.90666 8.7308 6.61549V6.6928C7.63006 6.82826 6.60172 7.13726 5.69459 7.58429C5.52274 7.66898 5.45131 7.87851 5.53506 8.05229C5.61881 8.22607 5.82601 8.2983 5.99786 8.21361C7.14783 7.64691 8.52114 7.31556 10 7.31556C11.4789 7.31556 12.8522 7.64691 14.0021 8.21361C14.174 8.2983 14.3812 8.22607 14.4649 8.05229C14.5487 7.87851 14.4773 7.66898 14.3054 7.58429C13.3983 7.13726 12.37 6.82826 11.2692 6.6928V6.61549C11.2692 5.90666 10.701 5.33203 10 5.33203Z" fill="white" />
                                            </svg>
                                        </span>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }
        else
        {
            <span>No Data Found</span>
        }
    }

    <div class="tenantmasterfooter">
        <div class="pagesize">
            <div class="pagesizebox">
               
            </div>
            <div class="pagesizebox">
                <span>@ShowingCount of @TotalCount</span>
            </div>
        </div>
        <div class="pagenumber">
            @if (Collection.Count < @PageSize)
            {
                <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()">Back</span>
            }
            else
            {
                @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                {
                    if (i < PageNumber || i == 0)
                    {
                        <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()"> &lt;&lt; </span>
                    }
                    else if (i == PageNumber)
                    {
                        <span id="pagenumbercircle">@i</span>
                    }
                    else if (i > PageNumber)
                    {
                        <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber()">&gt;&gt;</span>
                    }
                }
            }



        </div>
    </div>
</div>