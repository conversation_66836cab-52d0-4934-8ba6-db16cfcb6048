﻿using Blazored.LocalStorage;
using Blazored.Modal;
using Blazored.Modal.Services;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Pages.InvoiceGeneration;
using Lrb.Admin.UI.Pages.SubscriptionPage;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.Subscription
{

    public partial class PaymentDetails
    {
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public IModalService Modal { get; set; }

        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        [Parameter]
        public List<LRBPayments> Payments { get; set; }
        [Parameter]
        public Guid SubscriptionId { get; set; }
        public int MaxPartNumber {  get; set; }
        public double MaxPendingAmount {  get; set; }
        [Parameter]
        public bool IsAddOn {  get; set; }
        public UserModelDto LoginAdminUserInfo { get; set; } = new();
        public AdminUserActivity AdminUser { get; set; } = new();
        public string? UserLoginToken { get; set; }
        public string? Token { get; set; }

        protected override async Task OnInitializedAsync()
        {
            Token = await LocalStorage.GetItemAsync<string>("token");
            AdminUser = await ActivityService.GetUserByTokenAsync(Token);
            LoginAdminUserInfo = await AuthService.GetUser(AdminUser.AdminUserId);
        }
        public async Task Close()
        {
            await JsRuntime.InvokeVoidAsync("location.reload");
        }
        private async Task AddPayments()
        {
            if (Payments.Count() != 0 )
            {
                //MaxPartNumber = Payments.Select(i => i.PartNumber).Max();
                MaxPendingAmount = Payments.Where(i=> i.IsDeleted == false).Select(i => i.PendingAmount).Min();
            }
            else
            {
                MaxPartNumber = 0;
            }
            var parameter = new ModalParameters
            {
                { nameof(Subscription.AddPayments.TenantId), TenantId},
                { nameof(Subscription.AddPayments.SubscriptionId), SubscriptionId },
                { nameof(Subscription.AddPayments.IsAddon),IsAddOn},
                //{ nameof(Subscription.AddPayments.PaymentCount),Payments.Count() },
                { nameof(Subscription.AddPayments.PartNumber),MaxPartNumber},
                { nameof(Subscription.AddPayments.MaxPendingAmount),MaxPendingAmount}
            };
            var option = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<AddPayments>($"Add New Payment (Prev Pending : {MaxPendingAmount} )", parameter, option);
        }

        public async Task UpdatePayments(LRBPayments model)
        {

            var parameter = new ModalParameters();
            parameter.Add(nameof(UpdatePayment.TenantId), TenantId);
            parameter.Add(nameof(UpdatePayment.SubscriptionId), SubscriptionId);
            parameter.Add(nameof(UpdatePayment.PaymentModel), model);
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<UpdatePayment>("Update Payment", parameter, options);
        }

        public async Task DeleteSubscription(LRBPayments? payment)
        {
            var responseSub = await SubscriptionService.DeleteSubscriptionPayment(payment);
            if (responseSub.Succeeded)
            {
                var response = await PaymentService.DeletePaymentAsync(payment.Id);
                if (response)
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"Payments Deleted Successfully");
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity userActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.DeletePayment + " " + payment.TenantId,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(userActivity);
                    await JsRuntime.InvokeVoidAsync("location.reload");

                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", "Something Went Wrong {PaymentD : 69}");
                }
            }
        }

        public async Task DownloadInvoice(Guid? subscriptionId, Guid? paymentId)
        {
            var parameter = new ModalParameters();
            parameter.Add(nameof(TaxInvoice.TenantId), TenantId);
            parameter.Add(nameof(TaxInvoice.SubscriptionId), subscriptionId);
            parameter.Add(nameof(TaxInvoice.PaymentId), paymentId);
            parameter.Add(nameof(TaxInvoice.IsAddOn), IsAddOn);
            if (IsAddOn == false) 
            {
                parameter.Add(nameof(TaxInvoice.Description), "Leadrat CRM Application");
            }
            else
            {
                parameter.Add(nameof(TaxInvoice.Description), "Additional Subscription");
            }
            
            var options = new ModalOptions
            {
                Size = ModalSize.ExtraLarge
            };
            var modal = Modal.Show<TaxInvoice>(null,parameter, options);
        }
    }
}
