﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Lead;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Lrb.Admin.Data.Repos
{
    internal class DuplicateFeatureRepo : IDuplicateFeatureRepo
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string Schema = "LeadratBlack";

        public DuplicateFeatureRepo(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        public async Task<PagedResponse<DuplicateFeatureViewModel, string>> GetAllDuplicateFeatureAsync(int pageNumber, int pageSize, DuplicateFeatureFilter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await TotalDuplicateFeatureCount();
                var query = db.Query($"{Schema}.DuplicateFeatureInfo").Where("IsDeleted", "=", false).Select("IsFeatureAdded", "TenatId");
                query = query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                IEnumerable<DuplicateFeatureViewModel> duplicateLeads = await query.GetAsync<DuplicateFeatureViewModel>();
                connection.Close();
                return new PagedResponse<DuplicateFeatureViewModel, string>(duplicateLeads, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<DuplicateFeatureViewModel> GetDuplicateFeatureById(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.DuplicateFeatureInfo").Where("IsDeleted", "=", false).Where("TenantId", "=", tenantId).Select("IsFeatureAdded", "TenatId");
                connection.Close();
                return await query.FirstOrDefaultAsync<DuplicateFeatureViewModel>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> TotalDuplicateFeatureCount()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.DuplicateFeatureInfo").Where("IsDeleted", "=", false).Select("count(\"Id\")");
                connection.Close();
                return await query.CountAsync<int>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
