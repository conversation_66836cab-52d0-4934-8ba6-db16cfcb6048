﻿namespace Lrb.Admin.Data.Models.Wrappers
{
    public class Response<T> : Response//BaseApiResponse
    {
        public Response()
        {
        }
        public Response(T data, string message = null)
        {
            Succeeded = true;
            Message = message;
            Data = data;
        }

        public Response(string message)
        {
            Succeeded = false;
            Message = message;
        }
        public bool Succeeded { get; set; }
        public List<string> Errors { get; set; }
        public T Data { get; set; }
    }
    public class Response
    {
        public string Message { get; set; }

    }
}
