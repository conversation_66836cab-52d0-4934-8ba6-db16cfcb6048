﻿@page "/"
@using Radzen.Blazor.Rendering;
@inject DialogService DialogService

<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
</head>
@* <Popup @ref=popup Lazy=true class="popupStyle">
    <TenantMasterFilter PageSize="@PageSize" PageNumber="@PageNumber" Filter="@Filter" Count="@Count" LicenseMonitor="@LicenseMonitor" GetTenantsAsync="@GetTenantsAsync" />
</Popup> *@
<div class="tenantmasterheader">
    <div class="headerText">
        <span class="headername">
            @* Tenant Master table *@
            <svg width="229" height="24" viewBox="0 0 229 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.23687 12.2657L12.635 17.2137C12.8794 17.4886 13.334 17.3157 13.334 16.9479L13.334 7.05208C13.334 6.68427 12.8794 6.51143 12.635 6.78633L8.23687 11.7343C8.10215 11.8858 8.10215 12.1142 8.23687 12.2657Z" fill="#292A2B" />
                <path d="M34.68 20V8.4H30.68V6H41.4V8.4H37.28V20H34.68ZM46.4777 20.2C45.371 20.2 44.391 19.9667 43.5377 19.5C42.6977 19.0333 42.0377 18.4 41.5577 17.6C41.091 16.7867 40.8577 15.86 40.8577 14.82C40.8577 13.9933 40.991 13.24 41.2577 12.56C41.5243 11.88 41.891 11.2933 42.3577 10.8C42.8377 10.2933 43.4043 9.90667 44.0577 9.64C44.7243 9.36 45.451 9.22 46.2377 9.22C46.931 9.22 47.5777 9.35333 48.1777 9.62C48.7777 9.88667 49.2977 10.2533 49.7377 10.72C50.1777 11.1733 50.511 11.72 50.7377 12.36C50.9777 12.9867 51.091 13.6733 51.0777 14.42L51.0577 15.28H42.5177L42.0577 13.6H48.9977L48.6777 13.94V13.5C48.6377 13.0867 48.5043 12.7267 48.2777 12.42C48.051 12.1 47.7577 11.8533 47.3977 11.68C47.051 11.4933 46.6643 11.4 46.2377 11.4C45.5843 11.4 45.031 11.5267 44.5777 11.78C44.1377 12.0333 43.8043 12.4 43.5777 12.88C43.351 13.3467 43.2377 13.9333 43.2377 14.64C43.2377 15.32 43.3777 15.9133 43.6577 16.42C43.951 16.9267 44.3577 17.32 44.8777 17.6C45.411 17.8667 46.031 18 46.7377 18C47.231 18 47.6843 17.92 48.0977 17.76C48.511 17.6 48.9577 17.3133 49.4377 16.9L50.6577 18.6C50.2977 18.9333 49.8843 19.22 49.4177 19.46C48.9643 19.6867 48.4843 19.8667 47.9777 20C47.471 20.1333 46.971 20.2 46.4777 20.2ZM53.2564 20V9.46H55.6564L55.6964 11.62L55.2364 11.86C55.3697 11.38 55.6297 10.9467 56.0164 10.56C56.4031 10.16 56.8631 9.84 57.3964 9.6C57.9297 9.36 58.4764 9.24 59.0364 9.24C59.8364 9.24 60.5031 9.4 61.0364 9.72C61.5831 10.04 61.9897 10.52 62.2564 11.16C62.5364 11.8 62.6764 12.6 62.6764 13.56V20H60.2164V13.74C60.2164 13.2067 60.1431 12.7667 59.9964 12.42C59.8497 12.06 59.6231 11.8 59.3164 11.64C59.0097 11.4667 58.6364 11.3867 58.1964 11.4C57.8364 11.4 57.5031 11.46 57.1964 11.58C56.9031 11.6867 56.6431 11.8467 56.4164 12.06C56.2031 12.26 56.0297 12.4933 55.8964 12.76C55.7764 13.0267 55.7164 13.32 55.7164 13.64V20H54.4964C54.2564 20 54.0297 20 53.8164 20C53.6164 20 53.4297 20 53.2564 20ZM69.4239 20.2C68.5572 20.2 67.7706 19.96 67.0639 19.48C66.3572 19 65.7906 18.3467 65.3639 17.52C64.9372 16.6933 64.7239 15.7533 64.7239 14.7C64.7239 13.6467 64.9372 12.7067 65.3639 11.88C65.7906 11.0533 66.3706 10.4067 67.1039 9.94C67.8372 9.47333 68.6639 9.24 69.5839 9.24C70.1172 9.24 70.6039 9.32 71.0439 9.48C71.4839 9.62667 71.8706 9.84 72.2039 10.12C72.5372 10.4 72.8106 10.72 73.0239 11.08C73.2506 11.44 73.4039 11.8267 73.4839 12.24L72.9439 12.1V9.46H75.4239V20H72.9239V17.48L73.5039 17.38C73.4106 17.74 73.2372 18.0933 72.9839 18.44C72.7439 18.7733 72.4372 19.0733 72.0639 19.34C71.7039 19.5933 71.2972 19.8 70.8439 19.96C70.4039 20.12 69.9306 20.2 69.4239 20.2ZM70.1039 18.02C70.6772 18.02 71.1839 17.88 71.6239 17.6C72.0639 17.32 72.4039 16.9333 72.6439 16.44C72.8972 15.9333 73.0239 15.3533 73.0239 14.7C73.0239 14.06 72.8972 13.4933 72.6439 13C72.4039 12.5067 72.0639 12.12 71.6239 11.84C71.1839 11.56 70.6772 11.42 70.1039 11.42C69.5306 11.42 69.0239 11.56 68.5839 11.84C68.1572 12.12 67.8239 12.5067 67.5839 13C67.3439 13.4933 67.2239 14.06 67.2239 14.7C67.2239 15.3533 67.3439 15.9333 67.5839 16.44C67.8239 16.9333 68.1572 17.32 68.5839 17.6C69.0239 17.88 69.5306 18.02 70.1039 18.02ZM78.2759 20V9.46H80.6759L80.7159 11.62L80.2559 11.86C80.3893 11.38 80.6493 10.9467 81.0359 10.56C81.4226 10.16 81.8826 9.84 82.4159 9.6C82.9493 9.36 83.4959 9.24 84.0559 9.24C84.8559 9.24 85.5226 9.4 86.0559 9.72C86.6026 10.04 87.0093 10.52 87.2759 11.16C87.5559 11.8 87.6959 12.6 87.6959 13.56V20H85.2359V13.74C85.2359 13.2067 85.1626 12.7667 85.0159 12.42C84.8693 12.06 84.6426 11.8 84.3359 11.64C84.0293 11.4667 83.6559 11.3867 83.2159 11.4C82.8559 11.4 82.5226 11.46 82.2159 11.58C81.9226 11.6867 81.6626 11.8467 81.4359 12.06C81.2226 12.26 81.0493 12.4933 80.9159 12.76C80.7959 13.0267 80.7359 13.32 80.7359 13.64V20H79.5159C79.2759 20 79.0493 20 78.8359 20C78.6359 20 78.4493 20 78.2759 20ZM91.6034 20V6.78H94.0634V20H91.6034ZM89.5234 11.74V9.46H96.3634V11.74H89.5234ZM104.556 20V6H107.016L112.116 14.3L110.676 14.28L115.836 6H118.176V20H115.596V14.74C115.596 13.54 115.622 12.46 115.676 11.5C115.742 10.54 115.849 9.58667 115.996 8.64L116.316 9.5L111.976 16.2H110.656L106.456 9.56L106.736 8.64C106.882 9.53333 106.982 10.4533 107.036 11.4C107.102 12.3333 107.136 13.4467 107.136 14.74V20H104.556ZM125.479 20.2C124.612 20.2 123.825 19.96 123.119 19.48C122.412 19 121.845 18.3467 121.419 17.52C120.992 16.6933 120.779 15.7533 120.779 14.7C120.779 13.6467 120.992 12.7067 121.419 11.88C121.845 11.0533 122.425 10.4067 123.159 9.94C123.892 9.47333 124.719 9.24 125.639 9.24C126.172 9.24 126.659 9.32 127.099 9.48C127.539 9.62667 127.925 9.84 128.259 10.12C128.592 10.4 128.865 10.72 129.079 11.08C129.305 11.44 129.459 11.8267 129.539 12.24L128.999 12.1V9.46H131.479V20H128.979V17.48L129.559 17.38C129.465 17.74 129.292 18.0933 129.039 18.44C128.799 18.7733 128.492 19.0733 128.119 19.34C127.759 19.5933 127.352 19.8 126.899 19.96C126.459 20.12 125.985 20.2 125.479 20.2ZM126.159 18.02C126.732 18.02 127.239 17.88 127.679 17.6C128.119 17.32 128.459 16.9333 128.699 16.44C128.952 15.9333 129.079 15.3533 129.079 14.7C129.079 14.06 128.952 13.4933 128.699 13C128.459 12.5067 128.119 12.12 127.679 11.84C127.239 11.56 126.732 11.42 126.159 11.42C125.585 11.42 125.079 11.56 124.639 11.84C124.212 12.12 123.879 12.5067 123.639 13C123.399 13.4933 123.279 14.06 123.279 14.7C123.279 15.3533 123.399 15.9333 123.639 16.44C123.879 16.9333 124.212 17.32 124.639 17.6C125.079 17.88 125.585 18.02 126.159 18.02ZM137.851 20.2C136.917 20.2 136.077 20.0467 135.331 19.74C134.597 19.42 133.997 18.9667 133.531 18.38L135.131 17C135.531 17.4533 135.977 17.78 136.471 17.98C136.964 18.18 137.484 18.28 138.031 18.28C138.257 18.28 138.457 18.2533 138.631 18.2C138.817 18.1467 138.977 18.0667 139.111 17.96C139.244 17.8533 139.344 17.7333 139.411 17.6C139.491 17.4533 139.531 17.2933 139.531 17.12C139.531 16.8 139.411 16.5467 139.171 16.36C139.037 16.2667 138.824 16.1667 138.531 16.06C138.251 15.9533 137.884 15.8467 137.431 15.74C136.711 15.5533 136.111 15.34 135.631 15.1C135.151 14.8467 134.777 14.5667 134.511 14.26C134.284 14.0067 134.111 13.72 133.991 13.4C133.884 13.08 133.831 12.7333 133.831 12.36C133.831 11.8933 133.931 11.4733 134.131 11.1C134.344 10.7133 134.631 10.38 134.991 10.1C135.351 9.82 135.771 9.60667 136.251 9.46C136.731 9.31333 137.231 9.24 137.751 9.24C138.284 9.24 138.797 9.30667 139.291 9.44C139.797 9.57333 140.264 9.76667 140.691 10.02C141.131 10.26 141.504 10.5533 141.811 10.9L140.451 12.4C140.197 12.16 139.917 11.9467 139.611 11.76C139.317 11.5733 139.017 11.4267 138.711 11.32C138.404 11.2 138.117 11.14 137.851 11.14C137.597 11.14 137.371 11.1667 137.171 11.22C136.971 11.26 136.804 11.3267 136.671 11.42C136.537 11.5133 136.431 11.6333 136.351 11.78C136.284 11.9133 136.251 12.0733 136.251 12.26C136.264 12.42 136.304 12.5733 136.371 12.72C136.451 12.8533 136.557 12.9667 136.691 13.06C136.837 13.1533 137.057 13.26 137.351 13.38C137.644 13.5 138.024 13.6133 138.491 13.72C139.144 13.8933 139.691 14.0867 140.131 14.3C140.571 14.5133 140.917 14.76 141.171 15.04C141.424 15.28 141.604 15.56 141.711 15.88C141.817 16.2 141.871 16.5533 141.871 16.94C141.871 17.5667 141.691 18.1267 141.331 18.62C140.984 19.1133 140.504 19.5 139.891 19.78C139.291 20.06 138.611 20.2 137.851 20.2ZM145.178 20V6.78H147.638V20H145.178ZM143.098 11.74V9.46H149.938V11.74H143.098ZM156.653 20.2C155.547 20.2 154.567 19.9667 153.713 19.5C152.873 19.0333 152.213 18.4 151.733 17.6C151.267 16.7867 151.033 15.86 151.033 14.82C151.033 13.9933 151.167 13.24 151.433 12.56C151.7 11.88 152.067 11.2933 152.533 10.8C153.013 10.2933 153.58 9.90667 154.233 9.64C154.9 9.36 155.627 9.22 156.413 9.22C157.107 9.22 157.753 9.35333 158.353 9.62C158.953 9.88667 159.473 10.2533 159.913 10.72C160.353 11.1733 160.687 11.72 160.913 12.36C161.153 12.9867 161.267 13.6733 161.253 14.42L161.233 15.28H152.693L152.233 13.6H159.173L158.853 13.94V13.5C158.813 13.0867 158.68 12.7267 158.453 12.42C158.227 12.1 157.933 11.8533 157.573 11.68C157.227 11.4933 156.84 11.4 156.413 11.4C155.76 11.4 155.207 11.5267 154.753 11.78C154.313 12.0333 153.98 12.4 153.753 12.88C153.527 13.3467 153.413 13.9333 153.413 14.64C153.413 15.32 153.553 15.9133 153.833 16.42C154.127 16.9267 154.533 17.32 155.053 17.6C155.587 17.8667 156.207 18 156.913 18C157.407 18 157.86 17.92 158.273 17.76C158.687 17.6 159.133 17.3133 159.613 16.9L160.833 18.6C160.473 18.9333 160.06 19.22 159.593 19.46C159.14 19.6867 158.66 19.8667 158.153 20C157.647 20.1333 157.147 20.2 156.653 20.2ZM163.432 20V9.46H165.852L165.892 12.82L165.552 12.06C165.699 11.5267 165.952 11.0467 166.312 10.62C166.672 10.1933 167.086 9.86 167.552 9.62C168.032 9.36667 168.532 9.24 169.052 9.24C169.279 9.24 169.492 9.26 169.692 9.3C169.906 9.34 170.079 9.38667 170.212 9.44L169.552 12.14C169.406 12.06 169.226 11.9933 169.012 11.94C168.799 11.8867 168.586 11.86 168.372 11.86C168.039 11.86 167.719 11.9267 167.412 12.06C167.119 12.18 166.859 12.3533 166.632 12.58C166.406 12.8067 166.226 13.0733 166.092 13.38C165.972 13.6733 165.912 14.0067 165.912 14.38V20H163.432ZM180.969 20V8.4H176.969V6H187.689V8.4H183.569V20H180.969ZM191.807 20.2C190.94 20.2 190.153 19.96 189.447 19.48C188.74 19 188.173 18.3467 187.747 17.52C187.32 16.6933 187.107 15.7533 187.107 14.7C187.107 13.6467 187.32 12.7067 187.747 11.88C188.173 11.0533 188.753 10.4067 189.487 9.94C190.22 9.47333 191.047 9.24 191.967 9.24C192.5 9.24 192.987 9.32 193.427 9.48C193.867 9.62667 194.253 9.84 194.587 10.12C194.92 10.4 195.193 10.72 195.407 11.08C195.633 11.44 195.787 11.8267 195.867 12.24L195.327 12.1V9.46H197.807V20H195.307V17.48L195.887 17.38C195.793 17.74 195.62 18.0933 195.367 18.44C195.127 18.7733 194.82 19.0733 194.447 19.34C194.087 19.5933 193.68 19.8 193.227 19.96C192.787 20.12 192.313 20.2 191.807 20.2ZM192.487 18.02C193.06 18.02 193.567 17.88 194.007 17.6C194.447 17.32 194.787 16.9333 195.027 16.44C195.28 15.9333 195.407 15.3533 195.407 14.7C195.407 14.06 195.28 13.4933 195.027 13C194.787 12.5067 194.447 12.12 194.007 11.84C193.567 11.56 193.06 11.42 192.487 11.42C191.913 11.42 191.407 11.56 190.967 11.84C190.54 12.12 190.207 12.5067 189.967 13C189.727 13.4933 189.607 14.06 189.607 14.7C189.607 15.3533 189.727 15.9333 189.967 16.44C190.207 16.9333 190.54 17.32 190.967 17.6C191.407 17.88 191.913 18.02 192.487 18.02ZM206.479 20.2C206.012 20.2 205.552 20.1333 205.099 20C204.659 19.8533 204.259 19.66 203.899 19.42C203.539 19.18 203.239 18.9133 202.999 18.62C202.759 18.3133 202.599 18.0067 202.519 17.7L203.099 17.44L203.039 19.96H200.659V5.2H203.119V11.9L202.679 11.7C202.745 11.38 202.892 11.08 203.119 10.8C203.359 10.5067 203.652 10.2467 203.999 10.02C204.345 9.78 204.725 9.59333 205.139 9.46C205.552 9.31333 205.972 9.24 206.399 9.24C207.345 9.24 208.179 9.47333 208.899 9.94C209.632 10.4067 210.205 11.0533 210.619 11.88C211.045 12.7067 211.259 13.6467 211.259 14.7C211.259 15.7667 211.052 16.7133 210.639 17.54C210.225 18.3667 209.652 19.02 208.919 19.5C208.199 19.9667 207.385 20.2 206.479 20.2ZM205.959 18.04C206.519 18.04 207.019 17.9 207.459 17.62C207.899 17.3267 208.245 16.9333 208.499 16.44C208.752 15.9333 208.879 15.3533 208.879 14.7C208.879 14.06 208.752 13.4933 208.499 13C208.259 12.5067 207.919 12.12 207.479 11.84C207.039 11.56 206.532 11.42 205.959 11.42C205.385 11.42 204.879 11.56 204.439 11.84C203.999 12.12 203.652 12.5067 203.399 13C203.145 13.4933 203.019 14.06 203.019 14.7C203.019 15.3533 203.145 15.9333 203.399 16.44C203.652 16.9333 203.999 17.3267 204.439 17.62C204.879 17.9 205.385 18.04 205.959 18.04ZM213.472 20V5.2H215.952V20H213.472ZM223.587 20.2C222.48 20.2 221.5 19.9667 220.647 19.5C219.807 19.0333 219.147 18.4 218.667 17.6C218.2 16.7867 217.967 15.86 217.967 14.82C217.967 13.9933 218.1 13.24 218.367 12.56C218.634 11.88 219 11.2933 219.467 10.8C219.947 10.2933 220.514 9.90667 221.167 9.64C221.834 9.36 222.56 9.22 223.347 9.22C224.04 9.22 224.687 9.35333 225.287 9.62C225.887 9.88667 226.407 10.2533 226.847 10.72C227.287 11.1733 227.62 11.72 227.847 12.36C228.087 12.9867 228.2 13.6733 228.187 14.42L228.167 15.28H219.627L219.167 13.6H226.107L225.787 13.94V13.5C225.747 13.0867 225.614 12.7267 225.387 12.42C225.16 12.1 224.867 11.8533 224.507 11.68C224.16 11.4933 223.774 11.4 223.347 11.4C222.694 11.4 222.14 11.5267 221.687 11.78C221.247 12.0333 220.914 12.4 220.687 12.88C220.46 13.3467 220.347 13.9333 220.347 14.64C220.347 15.32 220.487 15.9133 220.767 16.42C221.06 16.9267 221.467 17.32 221.987 17.6C222.52 17.8667 223.14 18 223.847 18C224.34 18 224.794 17.92 225.207 17.76C225.62 17.6 226.067 17.3133 226.547 16.9L227.767 18.6C227.407 18.9333 226.994 19.22 226.527 19.46C226.074 19.6867 225.594 19.8667 225.087 20C224.58 20.1333 224.08 20.2 223.587 20.2Z" fill="#292A2B" />
            </svg>

        </span>
    </div>
    <div class="headerbuttons">
        <span @onclick="async () => { Filter = new(); Paginate(1); await GetTenantsAsync(); }">
            @* Refresh *@
            <svg width="89" height="38" viewBox="0 0 89 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="89" height="38" rx="4" fill="#292A2B" />
                <path d="M16.4396 24.4261C16.5371 24.4727 16.6439 24.4967 16.752 24.4964C16.9183 24.4964 17.0796 24.4398 17.2094 24.3359C17.3392 24.232 17.4298 24.0869 17.4662 23.9247C17.5026 23.7625 17.4827 23.5926 17.4097 23.4432C17.3367 23.2938 17.215 23.1737 17.0647 23.1027C16.1347 22.6641 15.3813 21.9225 14.928 20.9996C14.4747 20.0767 14.3484 19.0272 14.5698 18.023C14.7912 17.0189 15.3471 16.1198 16.1465 15.4731C16.616 15.0932 17.1531 14.8137 17.7247 14.6462L17.1747 15.7462C17.119 15.8577 17.0927 15.9816 17.0984 16.1061C17.104 16.2307 17.1413 16.3517 17.2068 16.4577C17.2723 16.5638 17.3638 16.6513 17.4726 16.7121C17.5815 16.7728 17.704 16.8048 17.8287 16.8049C17.9648 16.8055 18.0983 16.7678 18.2141 16.6963C18.3299 16.6247 18.4233 16.5222 18.4838 16.4002L19.6545 14.0587C19.7412 13.8851 19.7554 13.6842 19.694 13.5001C19.6326 13.3161 19.5005 13.1639 19.3269 13.0772C19.2197 13.0236 19.102 12.9977 18.9843 13.0003C18.9763 13 18.9683 13 18.9603 13C17.6004 13.009 16.2839 13.4797 15.2266 14.335C14.1693 15.1902 13.4338 16.3793 13.1409 17.7073C12.8479 19.0353 13.0148 20.4234 13.6141 21.6441C14.2135 22.8648 15.2097 23.8457 16.4396 24.4261Z" fill="white" />
                <path d="M19.0193 24.9998C19.0246 24.9999 19.0299 25 19.0352 25H19.0397C20.3996 24.991 21.7161 24.5203 22.7734 23.665C23.8307 22.8098 24.5662 21.6207 24.8591 20.2927C25.1521 18.9647 24.9852 17.5766 24.3859 16.3559C23.7865 15.1351 22.7903 14.1542 21.5604 13.5739C21.463 13.5272 21.3563 13.503 21.2483 13.503C21.1095 13.5036 20.9737 13.5433 20.8565 13.6176C20.7393 13.692 20.6454 13.7979 20.5858 13.9232C20.5448 14.0101 20.5213 14.1042 20.5167 14.2001C20.5121 14.2961 20.5265 14.392 20.5589 14.4824C20.5914 14.5728 20.6414 14.656 20.706 14.727C20.7707 14.7981 20.8487 14.8558 20.9356 14.8967C21.8655 15.3354 22.6188 16.0771 23.0721 17C23.5253 17.923 23.6516 18.9725 23.4302 19.9767C23.2088 20.9808 22.6528 21.8799 21.8535 22.5267C21.384 22.9066 20.847 23.1861 20.2753 23.3536L20.8252 22.2538C20.912 22.0802 20.9262 21.8793 20.8647 21.6952C20.8033 21.5112 20.6713 21.359 20.4977 21.2723C20.3241 21.1856 20.1232 21.1714 19.9391 21.2328C19.755 21.2943 19.6029 21.4263 19.5162 21.5999L18.3454 23.9413C18.2898 24.0528 18.2635 24.1767 18.2691 24.3013C18.2747 24.4258 18.312 24.5468 18.3775 24.6528C18.443 24.7589 18.5346 24.8464 18.6434 24.9072C18.7522 24.968 18.8748 24.9999 18.9994 25C19.006 25 19.0127 25 19.0193 24.9998Z" fill="white" />
                <path d="M35.128 24V15.6H38.776C39.28 15.6 39.74 15.72 40.156 15.96C40.572 16.192 40.9 16.512 41.14 16.92C41.388 17.32 41.512 17.772 41.512 18.276C41.512 18.756 41.388 19.2 41.14 19.608C40.9 20.008 40.572 20.328 40.156 20.568C39.748 20.8 39.288 20.916 38.776 20.916H36.652V24H35.128ZM40 24L37.864 20.208L39.472 19.908L41.848 24.012L40 24ZM36.652 19.56H38.788C39.02 19.56 39.22 19.508 39.388 19.404C39.564 19.292 39.7 19.14 39.796 18.948C39.892 18.756 39.94 18.544 39.94 18.312C39.94 18.048 39.88 17.82 39.76 17.628C39.64 17.436 39.472 17.284 39.256 17.172C39.04 17.06 38.792 17.004 38.512 17.004H36.652V19.56ZM45.9608 24.12C45.2968 24.12 44.7088 23.98 44.1968 23.7C43.6928 23.42 43.2968 23.04 43.0088 22.56C42.7288 22.072 42.5888 21.516 42.5888 20.892C42.5888 20.396 42.6688 19.944 42.8288 19.536C42.9888 19.128 43.2088 18.776 43.4888 18.48C43.7768 18.176 44.1168 17.944 44.5088 17.784C44.9088 17.616 45.3448 17.532 45.8168 17.532C46.2328 17.532 46.6208 17.612 46.9808 17.772C47.3408 17.932 47.6528 18.152 47.9168 18.432C48.1808 18.704 48.3808 19.032 48.5168 19.416C48.6608 19.792 48.7288 20.204 48.7208 20.652L48.7088 21.168H43.5848L43.3088 20.16H47.4728L47.2808 20.364V20.1C47.2568 19.852 47.1768 19.636 47.0408 19.452C46.9048 19.26 46.7288 19.112 46.5128 19.008C46.3048 18.896 46.0728 18.84 45.8168 18.84C45.4248 18.84 45.0928 18.916 44.8208 19.068C44.5568 19.22 44.3568 19.44 44.2208 19.728C44.0848 20.008 44.0168 20.36 44.0168 20.784C44.0168 21.192 44.1008 21.548 44.2688 21.852C44.4448 22.156 44.6888 22.392 45.0008 22.56C45.3208 22.72 45.6928 22.8 46.1168 22.8C46.4128 22.8 46.6848 22.752 46.9328 22.656C47.1808 22.56 47.4488 22.388 47.7368 22.14L48.4688 23.16C48.2528 23.36 48.0048 23.532 47.7248 23.676C47.4528 23.812 47.1648 23.92 46.8608 24C46.5568 24.08 46.2568 24.12 45.9608 24.12ZM50.6041 24V17.112C50.6041 16.728 50.6881 16.388 50.8561 16.092C51.0241 15.788 51.2561 15.552 51.5521 15.384C51.8481 15.208 52.1921 15.12 52.5841 15.12C52.8561 15.12 53.1081 15.168 53.3401 15.264C53.5721 15.352 53.7681 15.476 53.9281 15.636L53.4721 16.752C53.3681 16.664 53.2561 16.596 53.1361 16.548C53.0241 16.5 52.9161 16.476 52.8121 16.476C52.6521 16.476 52.5161 16.504 52.4041 16.56C52.3001 16.608 52.2201 16.684 52.1641 16.788C52.1161 16.892 52.0921 17.016 52.0921 17.16V24H51.3481C51.2041 24 51.0681 24 50.9401 24C50.8201 24 50.7081 24 50.6041 24ZM49.6201 19.104V17.808H53.5201V19.104H49.6201ZM54.739 24V17.676H56.191L56.215 19.692L56.011 19.236C56.099 18.916 56.251 18.628 56.467 18.372C56.683 18.116 56.931 17.916 57.211 17.772C57.499 17.62 57.799 17.544 58.111 17.544C58.247 17.544 58.375 17.556 58.495 17.58C58.623 17.604 58.727 17.632 58.807 17.664L58.411 19.284C58.323 19.236 58.215 19.196 58.087 19.164C57.959 19.132 57.831 19.116 57.703 19.116C57.503 19.116 57.311 19.156 57.127 19.236C56.951 19.308 56.795 19.412 56.659 19.548C56.523 19.684 56.415 19.844 56.335 20.028C56.263 20.204 56.227 20.404 56.227 20.628V24H54.739ZM62.4843 24.12C61.8203 24.12 61.2323 23.98 60.7203 23.7C60.2163 23.42 59.8203 23.04 59.5323 22.56C59.2523 22.072 59.1123 21.516 59.1123 20.892C59.1123 20.396 59.1923 19.944 59.3523 19.536C59.5123 19.128 59.7323 18.776 60.0123 18.48C60.3003 18.176 60.6403 17.944 61.0323 17.784C61.4323 17.616 61.8683 17.532 62.3403 17.532C62.7563 17.532 63.1443 17.612 63.5043 17.772C63.8643 17.932 64.1763 18.152 64.4403 18.432C64.7043 18.704 64.9043 19.032 65.0403 19.416C65.1843 19.792 65.2523 20.204 65.2443 20.652L65.2323 21.168H60.1083L59.8323 20.16H63.9963L63.8043 20.364V20.1C63.7803 19.852 63.7003 19.636 63.5643 19.452C63.4283 19.26 63.2523 19.112 63.0363 19.008C62.8283 18.896 62.5963 18.84 62.3403 18.84C61.9483 18.84 61.6163 18.916 61.3443 19.068C61.0803 19.22 60.8803 19.44 60.7443 19.728C60.6083 20.008 60.5403 20.36 60.5403 20.784C60.5403 21.192 60.6243 21.548 60.7923 21.852C60.9683 22.156 61.2123 22.392 61.5243 22.56C61.8443 22.72 62.2163 22.8 62.6403 22.8C62.9363 22.8 63.2083 22.752 63.4563 22.656C63.7043 22.56 63.9723 22.388 64.2603 22.14L64.9922 23.16C64.7763 23.36 64.5283 23.532 64.2483 23.676C63.9763 23.812 63.6883 23.92 63.3843 24C63.0803 24.08 62.7803 24.12 62.4843 24.12ZM68.6635 24.12C68.1035 24.12 67.5995 24.028 67.1515 23.844C66.7115 23.652 66.3515 23.38 66.0715 23.028L67.0315 22.2C67.2715 22.472 67.5395 22.668 67.8355 22.788C68.1315 22.908 68.4435 22.968 68.7715 22.968C68.9075 22.968 69.0275 22.952 69.1315 22.92C69.2435 22.888 69.3395 22.84 69.4195 22.776C69.4995 22.712 69.5595 22.64 69.5995 22.56C69.6475 22.472 69.6715 22.376 69.6715 22.272C69.6715 22.08 69.5995 21.928 69.4555 21.816C69.3755 21.76 69.2475 21.7 69.0715 21.636C68.9035 21.572 68.6835 21.508 68.4115 21.444C67.9795 21.332 67.6195 21.204 67.3315 21.06C67.0435 20.908 66.8195 20.74 66.6595 20.556C66.5235 20.404 66.4195 20.232 66.3475 20.04C66.2835 19.848 66.2515 19.64 66.2515 19.416C66.2515 19.136 66.3115 18.884 66.4315 18.66C66.5595 18.428 66.7315 18.228 66.9475 18.06C67.1635 17.892 67.4155 17.764 67.7035 17.676C67.9915 17.588 68.2915 17.544 68.6035 17.544C68.9235 17.544 69.2315 17.584 69.5275 17.664C69.8315 17.744 70.1115 17.86 70.3675 18.012C70.6315 18.156 70.8555 18.332 71.0395 18.54L70.2235 19.44C70.0715 19.296 69.9035 19.168 69.7195 19.056C69.5435 18.944 69.3635 18.856 69.1795 18.792C68.9955 18.72 68.8235 18.684 68.6635 18.684C68.5115 18.684 68.3755 18.7 68.2555 18.732C68.1355 18.756 68.0355 18.796 67.9555 18.852C67.8755 18.908 67.8115 18.98 67.7635 19.068C67.7235 19.148 67.7035 19.244 67.7035 19.356C67.7115 19.452 67.7355 19.544 67.7755 19.632C67.8235 19.712 67.8875 19.78 67.9675 19.836C68.0555 19.892 68.1875 19.956 68.3635 20.028C68.5395 20.1 68.7675 20.168 69.0475 20.232C69.4395 20.336 69.7675 20.452 70.0315 20.58C70.2955 20.708 70.5035 20.856 70.6555 21.024C70.8075 21.168 70.9155 21.336 70.9795 21.528C71.0435 21.72 71.0755 21.932 71.0755 22.164C71.0755 22.54 70.9675 22.876 70.7515 23.172C70.5435 23.468 70.2555 23.7 69.8875 23.868C69.5275 24.036 69.1195 24.12 68.6635 24.12ZM72.3757 24V15.12H73.8397V18.972L73.5637 19.116C73.6437 18.828 73.7997 18.568 74.0317 18.336C74.2637 18.096 74.5397 17.904 74.8597 17.76C75.1797 17.616 75.5077 17.544 75.8437 17.544C76.3237 17.544 76.7237 17.64 77.0437 17.832C77.3717 18.024 77.6157 18.312 77.7757 18.696C77.9437 19.08 78.0277 19.56 78.0277 20.136V24H76.5517V20.244C76.5517 19.924 76.5077 19.66 76.4197 19.452C76.3317 19.236 76.1957 19.08 76.0117 18.984C75.8277 18.88 75.6037 18.832 75.3397 18.84C75.1237 18.84 74.9237 18.876 74.7397 18.948C74.5637 19.012 74.4077 19.108 74.2717 19.236C74.1437 19.356 74.0397 19.496 73.9597 19.656C73.8877 19.816 73.8517 19.992 73.8517 20.184V24H73.1197C72.9757 24 72.8397 24 72.7117 24C72.5917 24 72.4797 24 72.3757 24Z" fill="white" />
            </svg>

        </span>
        <span onclick="@(()=> Navigation.NavigateTo($"add-tenant"))">
            @* Add new Tenant *@
            <svg width="141" height="38" viewBox="0 0 141 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="141" height="38" rx="4" fill="#292A2B" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26 19C26 22.866 22.866 26 19 26C15.134 26 12 22.866 12 19C12 15.134 15.134 12 19 12C22.866 12 26 15.134 26 19ZM19 15.675C19.2899 15.675 19.525 15.9101 19.525 16.2V18.475H21.8C22.0899 18.475 22.325 18.7101 22.325 19C22.325 19.2899 22.0899 19.525 21.8 19.525H19.525V21.8C19.525 22.0899 19.2899 22.325 19 22.325C18.7101 22.325 18.475 22.0899 18.475 21.8V19.525H16.2C15.9101 19.525 15.675 19.2899 15.675 19C15.675 18.7101 15.9101 18.475 16.2 18.475H18.475V16.2C18.475 15.9101 18.7101 15.675 19 15.675Z" fill="white" />
                <path d="M34.192 24L37.468 15.6H38.932L42.184 24H40.564L38.764 19.224C38.724 19.128 38.668 18.976 38.596 18.768C38.532 18.56 38.46 18.336 38.38 18.096C38.3 17.848 38.228 17.62 38.164 17.412C38.1 17.196 38.052 17.04 38.02 16.944L38.32 16.932C38.272 17.092 38.216 17.272 38.152 17.472C38.088 17.672 38.02 17.88 37.948 18.096C37.876 18.312 37.804 18.52 37.732 18.72C37.668 18.92 37.608 19.1 37.552 19.26L35.752 24H34.192ZM35.68 22.08L36.22 20.724H40.048L40.624 22.08H35.68ZM45.5525 24.12C44.9925 24.12 44.4885 23.98 44.0405 23.7C43.6005 23.412 43.2485 23.024 42.9845 22.536C42.7285 22.04 42.6005 21.472 42.6005 20.832C42.6005 20.2 42.7285 19.636 42.9845 19.14C43.2405 18.644 43.5885 18.256 44.0285 17.976C44.4765 17.688 44.9805 17.544 45.5405 17.544C45.8445 17.544 46.1365 17.592 46.4165 17.688C46.7045 17.784 46.9605 17.916 47.1845 18.084C47.4085 18.244 47.5845 18.424 47.7125 18.624C47.8485 18.824 47.9245 19.032 47.9405 19.248L47.5445 19.296V15.12H49.0205V24H47.5925L47.5565 22.512L47.8445 22.536C47.8365 22.736 47.7645 22.928 47.6285 23.112C47.5005 23.296 47.3325 23.464 47.1245 23.616C46.9165 23.768 46.6725 23.892 46.3925 23.988C46.1205 24.076 45.8405 24.12 45.5525 24.12ZM45.8405 22.872C46.1845 22.872 46.4885 22.784 46.7525 22.608C47.0165 22.432 47.2205 22.192 47.3645 21.888C47.5165 21.584 47.5925 21.232 47.5925 20.832C47.5925 20.44 47.5165 20.092 47.3645 19.788C47.2205 19.476 47.0165 19.236 46.7525 19.068C46.4885 18.892 46.1845 18.804 45.8405 18.804C45.4965 18.804 45.1925 18.892 44.9285 19.068C44.6645 19.236 44.4565 19.476 44.3045 19.788C44.1605 20.092 44.0885 20.44 44.0885 20.832C44.0885 21.232 44.1605 21.584 44.3045 21.888C44.4565 22.192 44.6645 22.432 44.9285 22.608C45.1925 22.784 45.4965 22.872 45.8405 22.872ZM53.24 24.12C52.68 24.12 52.176 23.98 51.728 23.7C51.288 23.412 50.936 23.024 50.672 22.536C50.416 22.04 50.288 21.472 50.288 20.832C50.288 20.2 50.416 19.636 50.672 19.14C50.928 18.644 51.276 18.256 51.716 17.976C52.164 17.688 52.668 17.544 53.228 17.544C53.532 17.544 53.824 17.592 54.104 17.688C54.392 17.784 54.648 17.916 54.872 18.084C55.096 18.244 55.272 18.424 55.4 18.624C55.536 18.824 55.612 19.032 55.628 19.248L55.232 19.296V15.12H56.708V24H55.28L55.244 22.512L55.532 22.536C55.524 22.736 55.452 22.928 55.316 23.112C55.188 23.296 55.02 23.464 54.812 23.616C54.604 23.768 54.36 23.892 54.08 23.988C53.808 24.076 53.528 24.12 53.24 24.12ZM53.528 22.872C53.872 22.872 54.176 22.784 54.44 22.608C54.704 22.432 54.908 22.192 55.052 21.888C55.204 21.584 55.28 21.232 55.28 20.832C55.28 20.44 55.204 20.092 55.052 19.788C54.908 19.476 54.704 19.236 54.44 19.068C54.176 18.892 53.872 18.804 53.528 18.804C53.184 18.804 52.88 18.892 52.616 19.068C52.352 19.236 52.144 19.476 51.992 19.788C51.848 20.092 51.776 20.44 51.776 20.832C51.776 21.232 51.848 21.584 51.992 21.888C52.144 22.192 52.352 22.432 52.616 22.608C52.88 22.784 53.184 22.872 53.528 22.872ZM62.1749 24V15.6H63.5789L68.4509 22.164L68.1869 22.212C68.1549 21.988 68.1269 21.76 68.1029 21.528C68.0789 21.288 68.0549 21.04 68.0309 20.784C68.0149 20.528 67.9989 20.26 67.9829 19.98C67.9749 19.7 67.9669 19.408 67.9589 19.104C67.9509 18.792 67.9469 18.464 67.9469 18.12V15.6H69.4949V24H68.0669L63.1709 17.532L63.4829 17.448C63.5229 17.896 63.5549 18.28 63.5789 18.6C63.6109 18.912 63.6349 19.184 63.6509 19.416C63.6669 19.64 63.6789 19.828 63.6869 19.98C63.7029 20.132 63.7109 20.272 63.7109 20.4C63.7189 20.52 63.7229 20.636 63.7229 20.748V24H62.1749ZM74.4491 24.12C73.7851 24.12 73.1971 23.98 72.6851 23.7C72.1811 23.42 71.7851 23.04 71.4971 22.56C71.2171 22.072 71.0771 21.516 71.0771 20.892C71.0771 20.396 71.1571 19.944 71.3171 19.536C71.4771 19.128 71.6971 18.776 71.9771 18.48C72.2651 18.176 72.6051 17.944 72.9971 17.784C73.3971 17.616 73.8331 17.532 74.3051 17.532C74.7211 17.532 75.1091 17.612 75.4691 17.772C75.8291 17.932 76.1411 18.152 76.4051 18.432C76.6691 18.704 76.8691 19.032 77.0051 19.416C77.1491 19.792 77.2171 20.204 77.2091 20.652L77.1971 21.168H72.0731L71.7971 20.16H75.9611L75.7691 20.364V20.1C75.7451 19.852 75.6651 19.636 75.5291 19.452C75.3931 19.26 75.2171 19.112 75.0011 19.008C74.7931 18.896 74.5611 18.84 74.3051 18.84C73.9131 18.84 73.5811 18.916 73.3091 19.068C73.0451 19.22 72.8451 19.44 72.7091 19.728C72.5731 20.008 72.5051 20.36 72.5051 20.784C72.5051 21.192 72.5891 21.548 72.7571 21.852C72.9331 22.156 73.1771 22.392 73.4891 22.56C73.8091 22.72 74.1811 22.8 74.6051 22.8C74.9011 22.8 75.1731 22.752 75.4211 22.656C75.6691 22.56 75.9371 22.388 76.2251 22.14L76.9571 23.16C76.7411 23.36 76.4931 23.532 76.2131 23.676C75.9411 23.812 75.6531 23.92 75.3491 24C75.0451 24.08 74.7451 24.12 74.4491 24.12ZM79.8872 24L77.7632 17.676L79.2752 17.664L80.6912 22.176L80.4272 22.152L81.9632 18.624H82.8512L84.4232 22.152L84.1112 22.188L85.5392 17.676H87.0512L84.9152 24H83.8592L82.2392 20.208L82.4672 20.232L80.9312 24H79.8872ZM93.6088 24V17.04H91.2088V15.6H97.6408V17.04H95.1688V24H93.6088ZM100.676 24.12C100.012 24.12 99.4237 23.98 98.9117 23.7C98.4077 23.42 98.0117 23.04 97.7237 22.56C97.4437 22.072 97.3037 21.516 97.3037 20.892C97.3037 20.396 97.3837 19.944 97.5437 19.536C97.7037 19.128 97.9237 18.776 98.2037 18.48C98.4917 18.176 98.8317 17.944 99.2237 17.784C99.6237 17.616 100.06 17.532 100.532 17.532C100.948 17.532 101.336 17.612 101.696 17.772C102.056 17.932 102.368 18.152 102.632 18.432C102.896 18.704 103.096 19.032 103.232 19.416C103.376 19.792 103.444 20.204 103.436 20.652L103.424 21.168H98.2997L98.0237 20.16H102.188L101.996 20.364V20.1C101.972 19.852 101.892 19.636 101.756 19.452C101.62 19.26 101.444 19.112 101.228 19.008C101.02 18.896 100.788 18.84 100.532 18.84C100.14 18.84 99.8077 18.916 99.5357 19.068C99.2717 19.22 99.0717 19.44 98.9357 19.728C98.7997 20.008 98.7317 20.36 98.7317 20.784C98.7317 21.192 98.8157 21.548 98.9837 21.852C99.1597 22.156 99.4037 22.392 99.7157 22.56C100.036 22.72 100.408 22.8 100.832 22.8C101.128 22.8 101.4 22.752 101.648 22.656C101.896 22.56 102.164 22.388 102.452 22.14L103.184 23.16C102.968 23.36 102.72 23.532 102.44 23.676C102.168 23.812 101.88 23.92 101.576 24C101.272 24.08 100.972 24.12 100.676 24.12ZM104.743 24V17.676H106.183L106.207 18.972L105.931 19.116C106.011 18.828 106.167 18.568 106.399 18.336C106.631 18.096 106.907 17.904 107.227 17.76C107.547 17.616 107.875 17.544 108.211 17.544C108.691 17.544 109.091 17.64 109.411 17.832C109.739 18.024 109.983 18.312 110.143 18.696C110.311 19.08 110.395 19.56 110.395 20.136V24H108.919V20.244C108.919 19.924 108.875 19.66 108.787 19.452C108.699 19.236 108.563 19.08 108.379 18.984C108.195 18.88 107.971 18.832 107.707 18.84C107.491 18.84 107.291 18.876 107.107 18.948C106.931 19.012 106.775 19.108 106.639 19.236C106.511 19.356 106.407 19.496 106.327 19.656C106.255 19.816 106.219 19.992 106.219 20.184V24H105.487C105.343 24 105.207 24 105.079 24C104.959 24 104.847 24 104.743 24ZM114.443 24.12C113.923 24.12 113.451 23.976 113.027 23.688C112.603 23.4 112.263 23.008 112.007 22.512C111.751 22.016 111.623 21.452 111.623 20.82C111.623 20.188 111.751 19.624 112.007 19.128C112.263 18.632 112.611 18.244 113.051 17.964C113.491 17.684 113.987 17.544 114.539 17.544C114.859 17.544 115.151 17.592 115.415 17.688C115.679 17.776 115.911 17.904 116.111 18.072C116.311 18.24 116.475 18.432 116.603 18.648C116.739 18.864 116.831 19.096 116.879 19.344L116.555 19.26V17.676H118.043V24H116.543V22.488L116.891 22.428C116.835 22.644 116.731 22.856 116.579 23.064C116.435 23.264 116.251 23.444 116.027 23.604C115.811 23.756 115.567 23.88 115.295 23.976C115.031 24.072 114.747 24.12 114.443 24.12ZM114.851 22.812C115.195 22.812 115.499 22.728 115.763 22.56C116.027 22.392 116.231 22.16 116.375 21.864C116.527 21.56 116.603 21.212 116.603 20.82C116.603 20.436 116.527 20.096 116.375 19.8C116.231 19.504 116.027 19.272 115.763 19.104C115.499 18.936 115.195 18.852 114.851 18.852C114.507 18.852 114.203 18.936 113.939 19.104C113.683 19.272 113.483 19.504 113.339 19.8C113.195 20.096 113.123 20.436 113.123 20.82C113.123 21.212 113.195 21.56 113.339 21.864C113.483 22.16 113.683 22.392 113.939 22.56C114.203 22.728 114.507 22.812 114.851 22.812ZM119.755 24V17.676H121.195L121.219 18.972L120.943 19.116C121.023 18.828 121.179 18.568 121.411 18.336C121.643 18.096 121.919 17.904 122.239 17.76C122.559 17.616 122.887 17.544 123.223 17.544C123.703 17.544 124.103 17.64 124.423 17.832C124.751 18.024 124.995 18.312 125.155 18.696C125.323 19.08 125.407 19.56 125.407 20.136V24H123.931V20.244C123.931 19.924 123.887 19.66 123.799 19.452C123.711 19.236 123.575 19.08 123.391 18.984C123.207 18.88 122.983 18.832 122.719 18.84C122.503 18.84 122.303 18.876 122.119 18.948C121.943 19.012 121.787 19.108 121.651 19.236C121.523 19.356 121.419 19.496 121.339 19.656C121.267 19.816 121.231 19.992 121.231 20.184V24H120.499C120.355 24 120.219 24 120.091 24C119.971 24 119.859 24 119.755 24ZM127.751 24V16.068H129.227V24H127.751ZM126.503 19.044V17.676H130.607V19.044H126.503Z" fill="white" />
            </svg>

        </span>
    </div>
</div>

<div class="TenantMasterTable">
    <div class="TenantMasterTableHeader">
        <div class="SearchBar">
            @* Search Symbol *@
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="searchicon">
                <path d="M16.6673 16.6654L15.334 15.332M3.33398 9.66537C3.33398 6.16756 6.16951 3.33203 9.66732 3.33203C13.1651 3.33203 16.0006 6.16756 16.0006 9.66537C16.0006 13.1632 13.1651 15.9987 9.66732 15.9987C6.16951 15.9987 3.33398 13.1632 3.33398 9.66537Z" stroke="#BBBBBB" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <input type="text" placeholder=" Press Enter to Search" class="Searchbarbar" @bind-value="Filter.TenantId" @oninput="e => Filter.TenantId = e.Value?.ToString()" @onkeydown="HandleKeyDown">
            <span class="searchButton" @onclick="GetTenantsAsync">
                Search
                </span>
        </div>
        <div class="buttons">
            @* <span class="buttonbutton" @onclick="@(args => popup.ToggleAsync(button.Element))">Filter</span> *@
            <span class="buttonbutton" @onclick="@OpenFilter">

                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1878 13.3346C11.2074 13.3346 12.064 12.6062 12.3069 11.6203H14.5639C14.866 11.6203 15.1109 11.3645 15.1109 11.0489C15.1109 10.7333 14.866 10.4775 14.5639 10.4775H12.3069C12.064 9.49165 11.2074 8.76321 10.1878 8.76321C8.9794 8.76321 7.99978 9.78656 7.99978 11.0489C7.99978 12.3113 8.9794 13.3346 10.1878 13.3346ZM1.43568 10.4775C1.13358 10.4775 0.888672 10.7333 0.888672 11.0489C0.888672 11.3645 1.13358 11.6203 1.43568 11.6203H5.81175C6.11385 11.6203 6.35876 11.3645 6.35876 11.0489C6.35876 10.7333 6.11385 10.4775 5.81175 10.4775H1.43568Z" fill="#292A2B" />
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.99978 4.95368C7.99978 6.21605 7.02017 7.2394 5.81175 7.2394C4.79221 7.2394 3.93554 6.51096 3.69265 5.52511H1.43568C1.13358 5.52511 0.888672 5.26927 0.888672 4.95368C0.888672 4.63809 1.13358 4.38225 1.43568 4.38225H3.69265C3.93554 3.39641 4.79221 2.66797 5.81175 2.66797C7.02017 2.66797 7.99978 3.69132 7.99978 4.95368ZM10.1878 4.38225C9.88571 4.38225 9.64081 4.63809 9.64081 4.95368C9.64081 5.26927 9.88571 5.52511 10.1878 5.52511H14.5639C14.866 5.52511 15.1109 5.26927 15.1109 4.95368C15.1109 4.63809 14.866 4.38225 14.5639 4.38225H10.1878Z" fill="#292A2B" />
                </svg>

                Filter
            </span>
            <span class="buttonbutton" @onclick="@DownloadTenantAsync">Export Tenant</span>
            <span class="buttonbutton" @onclick="@DownloadCompleteTenantDataAsync"> Complete Tenant Data</span>
            <span class="buttonbutton" title="Page Size"> 
                <select id="pagesizeinput" @bind="PageSize" @bind:after="GetTenantsAsync">
                    @foreach (var dv in DropdownValues)
                    {
                        <option value="@dv">@dv</option>
                    }
                </select>
            </span>

        </div>
    </div>
    @if (!IsDataFound)
    {
        <span>No Data Found</span>
    }
    else
    {
        @if (Collection.Any(i => i != null))
        {
            <div class="all-tables">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Sl.No</th>
                                <th>TenantId</th>
                                <th>Tenant Name</th>
                                <th>Date Of OnBoard</th>
                                <th>License Validity</th>
                                <th>Payment Date</th>
                                @* <th>City</th> *@
                                <th>Vendors</th>
                                @* <th>No Of Users</th>
                                <th>Active Users</th>
                                <th>Inactive Users</th> *@
                                <th>No of Licenses</th>
                                <th>Gst Number</th>
                                <th>Subscription Net Amount</th>
                                <th>Subscription GST Amount</th>
                                <th>Subscription Total Amount</th>
                                <th>Subscription Paid Amount</th>
                                <th>Subscription Due Amount</th>
                                <th>Subscription Due Date</th>
                                <th>Addon Total Amount</th>
                                <th>Addon Due Amount</th>
                                <th>Addon Due Date</th>
                                <th>Billing Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var items in Collection.Select((value, i) => new { value, i }))
                            {
                                <tr>
                                    <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                                    <td class="sort-link" @onclick="@(async () => await NavigateToComponent(items.value?.TenantId))" style="cursor:pointer"><u>@items.value?.TenantId</u></td>
                                    <td>@items?.value?.TenantName</td>
                                    <td>@items?.value?.DateOfOnBoard?.ToLocalTime().ToString("dd/MM/yyyy")</td>
                                    <td>@items?.value?.LicenseValidity?.ToString("dd/MM/yyyy")</td>
                                    <td>@items?.value?.PaymentDate?.ToString("dd/MM/yyyy")</td>
                                   @*  <td>@items?.value?.City</td> *@
                                    <td>
                                        @{
                                            var vendorsList = items?.value?.Vendors?.Split(',').Select(v => v.Trim()).ToList();
                                            var displayVendors = vendorsList?.Take(2);
                                            var vendorsText = displayVendors != null ? string.Join(", ", displayVendors) : "";
                                        }
                                        @if (vendorsList?.Count > 2)
                                        {
                                            @($"{vendorsText}, . . .")
                                        }
                                        else
                                        {
                                            @vendorsText
                                        }
                                    </td>

                                    @* <td>@items?.value?.NoOfUsers</td>
                                    <td>@items?.value?.ActiveUsers</td>
                                    <td>@items?.value?.InActiveUser</td> *@
                                    <td>@((items?.value?.SoldLicenses ?? 0) + (items?.value?.AddonSoldLicenses ?? 0))</td>
                                    <td>@items?.value?.GSTNumber</td>
                                    <td>@items?.value?.NetAmount</td>
                                    <td>@items?.value?.GSTAmount</td>
                                    <td>@items?.value?.TotalAmount</td>
                                    <td>@items?.value?.PaidAmount</td>
                                    <td>@items?.value?.DueAmount</td>
                                    @if (items?.value?.DueAmount == 0 || items?.value?.DueAmount == null)
                                    {
                                        <td> </td>
                                    }
                                    else
                                    {
                                        <td>@items?.value?.DueDate?.ToString("dd/MM/yyyy")</td>
                                    }
                                    <td>@items?.value?.AddonTotalAmount</td>
                                    <td>@items?.value?.AddonDueAmount</td>
                                    <td>@items?.value?.AddOnDueDate</td>
                                    <td>@items?.value?.BillingType</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

        }
        else
        {
            if (!IsDataFound)
            {
                <span>No Data Found</span>
            }
        }
    }

    <div class="tenantmasterfooter">
        <div class="pagesize">
            <div class="pagesizebox">
                @* <select id="pagesizeinput" @bind="PageSize" @bind:after="GetTenantsAsync">
                    @foreach (var dv in DropdownValues)
                    {
                        <option value="@dv">@dv</option>
                    }
                </select> *@
                <span> @ShowingCount of @TotalCount</span>
            </div>
        </div>
        <div class="pagenumber">
            
            @if (Collection.Count < PageSize)
            {
                <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()">Back</span>
            }
            else
            {
                @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                {
                    if (i < PageNumber || i == 0)
                    {
                        <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()"> &lt;&lt; </span>
                    }
                    else if (i == PageNumber)
                    {
                        <span id="pagenumbercircle">@i</span>
                    }
                    else if (i > PageNumber)
                    {
                        <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber()">&gt;&gt;</span>
                    }
                }
            }



        </div>
    </div>
</div>