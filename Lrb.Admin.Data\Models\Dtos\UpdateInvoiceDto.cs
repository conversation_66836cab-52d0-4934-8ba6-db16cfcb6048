﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class UpdateInvoiceDto
    {
        public Guid Id { get; set; }
        public string TenantId { get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public bool? IsDeleted { get; set; }
        public DateTime LastModifiedOn { get; set; }
        public double? TDSPercent { get; set; }
        public string? TDSCertificateURL { get; set; }
        public string? TransactionId { get; set; }
        public Guid PaymentId { get; set; }
        public int? Status { get; set; }
        public string InvoiceNo { get; set; }
        public int? SubscriptionType { get; set; }
        public string? InvoiceURL { get; set; }
    }
}
