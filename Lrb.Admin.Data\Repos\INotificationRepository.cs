﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface INotificationRepository
    {
        Task<NotificationDto> CheckNotificationTokenOfUser(string tenantId, Guid userId);
        Task<Response<bool>> CaptureNotificationDetailOfUser(Notifications model);
        Task<PagedResponse<NotificationViewModel, string>> GetAllNotificationDetailsAsync(string tenantId, int pageNumber, int pageSize, NotificationFilter filter);
        Task<int> GetNotificationCountAsync(string tenantId);
    }
}
