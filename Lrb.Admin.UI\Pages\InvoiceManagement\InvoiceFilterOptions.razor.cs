﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.InvoiceManagement
{
    public partial class InvoiceFilterOptions
    {
        [Parameter]
        public int PageNumber { get; set; }
        [Parameter]
        public int PageSize { get; set; }
        [Parameter]
        public List<int> Count { get; set; }
        //[Parameter]
        //public List<string> LicenseMonitor { get; set; }
        [Parameter]
        public InvoiceFilter Filter { get; set; } = new();
        [Parameter]
        public Func<Task> GetInvoiceAsync { get; set; }

        private async Task CallGetInvoiceAsync()
        {
            await GetInvoiceAsync.Invoke();
            //StateHasChanged();

        }

        private async Task ClearFilter()
        {
            Filter.OnboardingFromDate = null;
            Filter.OnboardingToDate = null;
            Filter.SubscriptionFromDate = null;
            Filter.SubscriptionToDate = null;
            Filter.PaymentFromDate = null;
            Filter.PaymentToDate = null;
            Filter.LicenseValidityFromDate = null;
            Filter.LicenseValidityToDate = null;
            Filter.TotalPackageAmountfrom = null;
            Filter.TotalPackageAmountto = null;
            Filter.TotalAmountPaidto = null;
            Filter.TotalAmountPaidfrom = null;
            Filter.NetDueAmountfrom = null;
            Filter.NetDueAmountto = null;
            Filter.PaymentMode = default!;
            Filter.BillingType = default!;
        }
    }
}
