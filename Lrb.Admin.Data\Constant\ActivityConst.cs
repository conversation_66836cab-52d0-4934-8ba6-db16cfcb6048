﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Constant
{
    public static class ActivityConst
    {
        public const string Login = "LogIn";
        public const string Logout = "LogOut";
        public const string AddTenant = "Added New Tenant";
        public const string TenantMasterProfile ="Opened Tenant Master Profile";
        public const string TenantProfile = "Opened Tenant Profile";
        public const string UpdateTenantProfile = "Updated Tenant Profile";
        public const string AddUser = "Added New User";
        public const string BulkUser = "Added New Bulk User";
        public const string Integration = "Opened Integration Table";
        public const string AnalyticsTable = "Opened Analytics Table";
        public const string VendorTable = "Opened Vendor Table";
        public const string AddVendor = " Added New Vendor";
        public const string UpdateVendor = "Updated New Vendor";
        public const string DeleteVendor = "Removed Vendor";
        public const string RegisterUser = "Register New User";
        public const string DuplicateFeature = "Opened Duplicate Feature Table";
        public const string AddedSubscription = " Added Subscription Details for";
        public const string AddedAddonSubscription = "Added Addon Subscription for";
        public const string DeleteSubscription = "Delete Subscription for";
        public const string SentNotification = "Sent Notification To";
        public const string AddedPayment = " Added Payment Details for";
        public const string UpdatePayment = "Updated Payment Details for";
        public const string DeletePayment = "Deleted Payment for";
        public const string UpdateSubscription = "Updated Subscription Details for";
        public const string UpdateInvoice = "Update Invoice Detail for";
        public const string InvoiceProfile = "Opened Invoice Profile";
    }
}
