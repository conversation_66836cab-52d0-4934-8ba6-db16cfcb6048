﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;
using Dapper;
using System.Text.Json.Serialization;
using Lrb.Admin.Data.Models.Dtos;

namespace Lrb.Admin.Data.Repos
{
    public class VendorRepository : IVendorRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string _intervalForIST = "5:30";
        protected readonly string Schema = "tenantmaster";
        protected Query Query;
        public VendorRepository(IDBConnectionFactory dbFactory) 
        {
            _dbFactory = dbFactory;
        }
        public async Task<Response<bool>> AddNewVendor(VendorModel model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                model.Id= Guid.NewGuid();
                model.CreatedOn = DateTime.Now;
                model.ModifiedOn = model.CreatedOn;
                model.IsDeleted = false;
                model.UserLimit = 200;
                model.TenantId = null;
                string firstTwo = null;
                string middleTwo = null;
                string lastFour = null;
                if (model.LastName.Length >= 2)
                {
                    firstTwo = model.FirstName[..2];
                    middleTwo = model.LastName[..2];
                    lastFour = model.PhoneNumber[..4];
                }
                if (model.LastName.Length < 2)
                {
                    firstTwo = model.FirstName[..3];
                    middleTwo = model.LastName[..1];
                    lastFour = model.PhoneNumber[..4];
                }
                model.ReferralCode = firstTwo + middleTwo + lastFour;
                model.ReferralCode = model.ReferralCode.ToUpper();
                model.GharOfficeTenant = true;
                var query = await db.Query($"{Schema}.Vendor").InsertAsync(model);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<VendorModel, string>> GetVendorAsync(int pageNumber,int pageSize,GetAllVendorFilter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await GetTotalCount();
                if(filter != default)
                {
                    Query = db.Query($"{Schema}.Vendor").Where("IsDeleted" ,"=",false).
                        Select("Id","CreatedOn","TenantId" ,"FirstName", "LastName", "PhoneNumber", "ReferralCode", "IsDeleted");
                }
                if(filter.SearchByContact !=  null)
                {
                    Query = db.Query($"{Schema}.Vendor").Where("IsDeleted", "=", false).WhereLike("PhoneNumber",$"%{filter.SearchByContact}%",true).
                        Select("Id","CreatedOn", "TenantId","FirstName", "LastName", "PhoneNumber", "ReferralCode", "IsDeleted");
                }
                Query = Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                IEnumerable<VendorModel> vendorData = await Query.GetAsync<VendorModel>();
                connection.Close();
                return new PagedResponse<VendorModel, string>(vendorData,count);

            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetTotalCount()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.Vendor").Where("IsDeleted","=",false).Select("count(\"TenantId\")");
                connection.Close();
                return await query.CountAsync<int>();
            }
            catch(Exception ex) 
            { 
                throw new Exception(ex.Message);
            }
        }

        public async Task<VendorModel> GetVendorById(Guid id)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection , new PostgresCompiler());
                var query = db.Query($"{Schema}.Vendor").Where("Id","=",id).
                    Select("Id","TenantId","CreatedOn", "FirstName", "LastName", "PhoneNumber", "ReferralCode", "IsDeleted");
                connection.Close();
                return await query.FirstOrDefaultAsync<VendorModel>();
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateVendor(Guid id, VendorModel model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var vendor = await GetVendorById(id);
                if(vendor == null)
                {
                    throw new Exception("Vendor Not Found");
                }
                var result = await db.Query($"{Schema}.Vendor").Where("Id","=",id).
                    UpdateAsync(new { FirstName = model.FirstName,LastName = model.LastName,PhoneNumber = model.PhoneNumber,ModifiedOn = DateTime.UtcNow });
                connection.Close();
                return new Response<bool>(result > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> DeleteVendor(Guid id)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var result = await db.Query($"{Schema}.Vendor").Where("Id","=",id).UpdateAsync(new { IsDeleted = true });
              //  var result = await db.Query($"{Schema}.Vendor").Where("Id","=",id).DeleteAsync();
                connection.Close();
                return new Response<bool>(result > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateUserLimit(string  tenantId,VendorModel model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var result = await db.Query($"{Schema}.Vendor").Where("TenantId", "=", tenantId).UpdateAsync(new { UserLimit = model.UserLimit });
                connection.Close();
                return new Response<bool>(result > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetUserLimit(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.Vendor").Where("TenantId","=",tenantId).Select("UserLimit");
                connection.Close();
                return await query.FirstOrDefaultAsync<int>();

            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<VendorModel>> GetAccountManager(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                //var query = db.Query($"{Schema}.Vendor").Where("TenantIdList","=",tenantId).Select("FirstName", "LastName");
                var query = $"SELECT * FROM \"tenantmaster\".\"Vendor\" WHERE \"TenantIdList\" @> '[\"{tenantId}\"]'::jsonb";
                var result = db.Connection.Query<VendorModel>(query).ToList();
                return result;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateAccountManager(string tenantId, VendorModel model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var lastName = model.FirstName.Split(" ");
                var query = await db.Query($"{Schema}.Vendor").Where("TenantId", "=", tenantId).
                    UpdateAsync(new { FirstName = model.FirstName, LastName = model.LastName });
                connection.Close(); 
                return new Response<bool>(query > 0);
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<VendorModel> GetVendorByTenantId(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.Vendor").Where("TenantId", "=", tenantId).
                    Select("Id", "TenantId", "CreatedOn", "FirstName", "LastName", "PhoneNumber", "ReferralCode", "IsDeleted");
                connection.Close();
                return await query.FirstOrDefaultAsync<VendorModel>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<string>> GetAllVendorNames()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"{Schema}.Vendor").Where("IsDeleted","=",false)
                    .Select("FirstName", "LastName");

                var results = (await query.GetAsync<Vendor>()).ToList();
                connection.Close();

                var vendorNames = new List<string>();
                foreach (var result in results)
                {
                    var firstName = result.FirstName;
                    var lastName = result.LastName;
                    var fullName = $"{firstName} {lastName}";
                    vendorNames.Add(fullName);
                }
                return vendorNames;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> UpdateVendorList(List<string> vendorNames, string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = 0;
                foreach (var vendor in vendorNames)
                {
                    var splitData = vendor.Split(" ");

                    //// Construct the JSONB data directly without manual serialization
                    //string jsonbFormatted = $"\"TenantIdList\" || '[\"{tenantId}\"]'::jsonb";
                    count += db.Connection.Execute($"UPDATE \"tenantmaster\".\"Vendor\"\r\nSET " +
                            $"\"TenantIdList\" = COALESCE(\"TenantIdList\", '[]'::jsonb) || ('[\"{tenantId}\"]')::jsonb\r\nWHERE \"FirstName\" = '{splitData[0]}'" +
                            $"\r\nAND \"LastName\" = '{splitData[splitData.Length - 1]}'\r\nAND (\r\n    \"TenantIdList\" IS NULL\r\n    OR NOT ('\"{tenantId}\"')::jsonb <@ " +
                            $"COALESCE(\"TenantIdList\", '[]'::jsonb)\r\n);");
                }

                return count;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> DeleteTenantIdFromVendor(List<string> VendorName, string TenantId)
        {
            try
            {
                var count = 0;
                foreach(var vendor in VendorName)
                {
                    var splitData = vendor.Split(" ");
                    var connection = await _dbFactory.CreateConnectionAsync();
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    var response = await db.Connection.ExecuteAsync($"UPDATE \"tenantmaster\".\"Vendor\"\r\nSET \"TenantIdList\" = \"TenantIdList\" - '{TenantId}'\r\nWHERE \"FirstName\" = '{splitData[0]}' \r\nAND \"LastName\" = '{splitData[splitData.Length - 1]}';");
                    count += response;
                }
                
                return count;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
