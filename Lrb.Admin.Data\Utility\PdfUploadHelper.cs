﻿using Microsoft.AspNetCore.Components.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Utility
{
    public static class PdfUploadHelper
    {
        public static async Task<byte[]> PdfToByteArray(IBrowserFile file)
        {
            string fileName = file.Name;
            long fileSize = file.Size;
            Stream fileStream = file.OpenReadStream(maxAllowedSize: 1024 * 1024 * 15);
            byte[] fileBytes;

            using (var memoryStream = new MemoryStream())
            {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    memoryStream.Write(buffer, 0, bytesRead);
                }
                fileBytes = memoryStream.ToArray();
            }
            return fileBytes;
        }
    }
}
