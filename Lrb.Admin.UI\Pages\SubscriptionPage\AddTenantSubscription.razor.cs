﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Microsoft.AspNetCore.Authorization;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Invoice;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;
using Lrb.Admin.Data.Models.Wrappers;
using Microsoft.AspNetCore.Components.Forms;
using Lrb.Admin.Data.Utility;

namespace Lrb.Admin.UI.Pages.SubscriptionPage
{
    [Authorize(Roles = "Admin")]
    public partial class AddTenantSubscription
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Parameter]
        public DateTime? LicencesValidity { get; set; }
        public DateTime? ValidUpto {  get; set; }
        [Parameter]
        public string TenantId { get; set; }    
        public AddTenantSubscriptionDto SubscriptionsModel { get; set; } = new();
        public string? Token { get; set; }
        public List<int> Parts { get; set; } = new() { 1, 2, 3, 4, 5, 6 };
        public bool PartPayment { get; set; } = false;
        private int SelectedParts { get; set; }
        public GetTenantModel TenantModel { get; set; } = new(); 
        public LRBSubscription Subscription { get; set; } = new();
        private string IsTDSApplicable { get; set; }
        private float? TDSNumber { get; set; }
        List<string> fileData = new List<string>();

        public async Task CheckPartPayment()
        {
            PartPayment = true;
        }
        public async Task<bool> AddSubscriptionAsync()
        {
            try
            {
                TenantModel = await TenantService.GetTenantByIdAsync(TenantId);
                if (TenantModel == null)
                {
                    await jSRuntime.InvokeVoidAsync("alert", "Tenant not found");
                }
                Subscription.Id = Guid.NewGuid();
                Subscription.Name = TenantModel?.TenantName;
                Subscription.TenantName = TenantModel?.TenantName;
                Subscription.CreatedOn = DateTime.UtcNow;
                Subscription.TenantId = TenantId;
                Subscription.NetAmount = SubscriptionsModel.SoldPrice;
                Subscription.SoldLicenses = SubscriptionsModel.SoldLicences;
                if (SubscriptionsModel.Type == PaymentType.Full)
                {
                    Subscription.PaidAmount = SubscriptionsModel.TotalAmount;
                    Subscription.DueAmount = 0;
                    Subscription.DueDate = null;
                }
                else
                {
                    Subscription.PaidAmount = SubscriptionsModel.PaidTotalAmount;
                    Subscription.DueAmount = SubscriptionsModel.PendingAmount;
                    Subscription.DueDate = SubscriptionsModel.NextDueDate;
                }
                Subscription.TotalAmount = SubscriptionsModel.TotalAmount;
                Subscription.PaymentDate = SubscriptionsModel.PaymentDate;
                Subscription.GSTAmount = SubscriptionsModel.GstAmount;
                Subscription.BillingType = SubscriptionsModel.BillingType;
                Subscription.GSTNumber = TenantModel?.GSTNumber?.ToUpper();
                var SubscriptionCount = SubscriptionService.GetSubscriptionCountAsync(TenantId);
                PagedResponse<LRBSubscription, string> allsub = null;
                allsub = await SubscriptionService.GetSubscriptionDetailsAsync(TenantId);
                //LicencesValidity is from Multitenancy
                if (LicencesValidity < DateTime.UtcNow || allsub.ItemsCount == 0 || allsub.Items.Where(i => i.IsDeleted == false).All(i => i.LicenseValidity < DateTime.UtcNow))
                {
                    Subscription.IsActive = true;
                }
                else
                {
                    Subscription.IsActive = false;
                }

                //var SubscriptionCount = SubscriptionService.GetsubscriptioncountAsync(TenantId);
                //Billing Cycle

                if (LicencesValidity > DateTime.UtcNow && allsub.Items.Count(i => i.IsDeleted == false) == 0)
                {
                    Subscription.LicenseValidity = LicencesValidity.Value;
                }
                else if ((LicencesValidity > DateTime.UtcNow && allsub.Items.Where(i => i.IsDeleted == false).All(i => i.LicenseValidity < DateTime.UtcNow)) || LicencesValidity < DateTime.UtcNow)
                {
                    if (Lrb.Admin.Data.Enums.BillingType.Quarterly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = SubscriptionsModel.PaymentDate.AddMonths(3).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Halfyearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = SubscriptionsModel.PaymentDate.AddMonths(6).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Yearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = SubscriptionsModel.PaymentDate.AddYears(1).AddDays(-1);
                    }
                    else
                    {
                        Subscription.LicenseValidity = SubscriptionsModel.PaymentDate.AddMonths(1);
                    }

                    UpdateTenantValidityDto updateTenantValidityDto = new UpdateTenantValidityDto()
                    {
                        TenantId = TenantId ?? string.Empty,
                        ExtendedExpiryDate = Subscription.LicenseValidity.ToUniversalTime()
                    };

                    var response1 = await TenantService.UpdateTenantValidity(TenantId ?? string.Empty, updateTenantValidityDto);
                    if (response1)
                    {
                        await jSRuntime.InvokeVoidAsync("alert", "Subscription Updated Successfully");
                    }
                }
                else if(LicencesValidity > DateTime.UtcNow)
                {
                    var maxLicenseValidity = allsub.Items.Where(i => !i.IsDeleted).Max(i => i.LicenseValidity).AddDays(1);
                    if (Lrb.Admin.Data.Enums.BillingType.Quarterly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = maxLicenseValidity.AddMonths(3).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Halfyearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = maxLicenseValidity.AddMonths(6).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Yearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = maxLicenseValidity.AddYears(1).AddDays(-1);
                    }
                    else
                    {
                        Subscription.LicenseValidity = maxLicenseValidity.AddMonths(1);
                    }
                }
                else
                {
                    if (Lrb.Admin.Data.Enums.BillingType.Quarterly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = DateTime.UtcNow.AddMonths(3).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Halfyearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = DateTime.UtcNow.AddMonths(6).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Yearly == SubscriptionsModel.BillingType && LicencesValidity != null)
                    {
                        Subscription.LicenseValidity = DateTime.UtcNow.AddYears(1).AddDays(-1);
                    }
                    else
                    {
                        Subscription.LicenseValidity = DateTime.UtcNow.AddMonths(1);
                    }
                    UpdateTenantValidityDto updateTenantValidityDto = new UpdateTenantValidityDto()
                    {
                        TenantId = TenantId ?? string.Empty,
                        ExtendedExpiryDate = Subscription.LicenseValidity.ToUniversalTime()
                    };

                    var response1 = await TenantService.UpdateTenantValidity(TenantId ?? string.Empty, updateTenantValidityDto);
                    if (response1)
                    {
                        await jSRuntime.InvokeVoidAsync("alert", "Subscription Updated Successfully");
                    }
                }
                //Adding new subscription
                var response = await SubscriptionService.CreateNewSubscriptionAsync(Subscription);
                
                //Payment data creation
                if (response != null)
                {
                    LRBPayments payment = new LRBPayments();
                    if (SubscriptionsModel.Type == PaymentType.Full)
                    {
                        payment = new()
                        {
                            Id = Guid.NewGuid(),
                            TenantId = TenantId,
                            Type = SubscriptionsModel.Type,
                            //PartPaid = 1,
                            //PartNumber = Model.NoOfParts,
                            NetAmount = SubscriptionsModel.SoldPrice,
                            GSTAmount = SubscriptionsModel.GstAmount,
                            TotalAmount = SubscriptionsModel.TotalAmount,
                            NextDueDate = null,
                            //PaidAmount = Model.TotalAmount,
                            PendingAmount = 0,
                            CreatedOn = SubscriptionsModel.PaymentDate,
                            SubscriptionId = Subscription.Id,
                            Mode = SubscriptionsModel.PaymentMode,
                            Description = SubscriptionsModel.Description,
                            IsDeleted = false,
                            InvoiceNumber = await GenerateInvoiceNumber()
                        };
                    }
                    else
                    {
                        payment = new()
                        {
                            Id = Guid.NewGuid(),
                            TenantId = TenantId,
                            Type = SubscriptionsModel.Type,
                            //PartPaid = 1,
                            //PartNumber = Model.NoOfParts,
                            NetAmount = SubscriptionsModel.PaidNetAmount,
                            GSTAmount = SubscriptionsModel.PaidGSTAmount,
                            TotalAmount = SubscriptionsModel.PaidTotalAmount,
                            NextDueDate = SubscriptionsModel.NextDueDate,
                            //PaidAmount = Model.PaidTotalAmount,
                            PendingAmount = SubscriptionsModel.PendingAmount,
                            CreatedOn = SubscriptionsModel.PaymentDate,
                            SubscriptionId = Subscription.Id,
                            Mode = SubscriptionsModel.PaymentMode,
                            Description = SubscriptionsModel.Description,
                            IsDeleted = false,
                            InvoiceNumber = await GenerateInvoiceNumber()
                        };
                    }
                    var added = await PaymentService.CreatePaymentAsync(payment);
                    //Activity is added
                    if (added != null)
                    {
                        string? TDSUrl = null;
                        if (IsTDSApplicable == "Applicable" && fileData.Count > 0)
                        {
                            string fileName = $"{TenantId}_{SubscriptionsModel.PaymentDate.ToString("ddMMyyHHmm")}";
                            var tdsresponse = await TenantService.UploadTDSCertificate(fileData, fileName);
                            TDSUrl = tdsresponse;
                        }
                        CreateInvoice createInvoice = new()
                        {
                            Id = new Guid(),
                            TenantId = payment.TenantId,
                            CreatedOn = SubscriptionsModel.PaymentDate,
                            PaymentId = payment.Id,
                            InvoiceNo = payment.InvoiceNumber,
                            //SubscriptionType = "Subscription",
                            TDSPercent = TDSNumber ?? null,
                        };
                        await PaymentService.CreateInvoiceAsync(createInvoice);
                        await jSRuntime.InvokeVoidAsync("alert", $"Subscription for {TenantModel?.TenantId} Successfully Created");
                        Token = await LocalStorage.GetItemAsync<string>("token");
                        var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                        AdminUserActivity userActivity = new()
                        {
                            AdminUserId = userActivityHistory.AdminUserId,
                            TenantId = TenantId,
                            ActivityTime = DateTime.UtcNow,
                            Activity = ActivityConst.AddedSubscription + " " + TenantModel?.TenantName,
                            Token = Token
                        };
                        await ActivityService.UserActivityAsync(userActivity);
                        if(TenantModel?.ActiveUsers > Subscription.SoldLicenses)
                        {
                            await jSRuntime.InvokeVoidAsync("alert", $"Active users are greater than new subscription purchased. Please deactivate {TenantModel?.ActiveUsers - Subscription.SoldLicenses} users");
                        }
                    }
                    await jSRuntime.InvokeVoidAsync("location.reload");
                }
                return true;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message, ex);
            }

        }
       
        public async Task<string> GenerateInvoiceNumber()
        {
            if (DateTime.UtcNow.Month >= 4)
            {
                return "DSL/" + DateTime.UtcNow.Year % 100 + "-" + ((DateTime.UtcNow.Year % 100) + 1) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
            else
            {
                return "DSL/" + ((DateTime.UtcNow.Year % 100) - 1) + "-" + (DateTime.UtcNow.Year % 100) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
        }
        private async Task TDSFileUpload(InputFileChangeEventArgs files)
        {
            foreach (var file in files.GetMultipleFiles())
            {
                var data = await PdfUploadHelper.PdfToByteArray(file);
                if (data != null)
                {
                    var base64Data = Convert.ToBase64String(data);
                    fileData.Add(base64Data);
                }
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await jSRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                SubscriptionsModel = new();
            }
        }

    }
}
