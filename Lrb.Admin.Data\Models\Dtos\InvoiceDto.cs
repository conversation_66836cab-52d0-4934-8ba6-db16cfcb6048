﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class InvoiceDto
    {
        public Guid PaymentId { get; set; }
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public int? InvoiceStatus { get; set; }
        public DateTime? DateofOnboard { get; set; }
        public DateTime? SubscriptionDate { get; set; }
        public DateTime? TransactionDate { get; set; }
        public long? NoOfLicenses { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public string? VendorNames { get; set; }
        public string? InvoiceNumber { get; set; }
        public string? GSTNumber { get; set; }
        public double? TotalPackageAmount { get; set; }
        public double? NetAmountPaid { get; set; }
        public double? GSTAmount { get; set; }
        public double? TotalAmountPaid {get;set;}
        public double? DueAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public float TDSDeductions { get; set; }
        public string? Address { get; set; }
        public BillingType? BillingType { get; set; }
        
        public PaymentMode? PaymentMode { get; set; }
        public string? SubscriptionType { get; set; }
    }
}
