﻿using Blazored.Modal.Services;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.SubStatus
{
    public partial class SubStatusDeletion
    {
        [Inject]
        public ICustomSubStatusService CustomSubStatus { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IModalService? Modal { get; set; }
        [Parameter]
        public Guid? SubStatusToDelete { get; set; }
        [Parameter]
        public List<CustomSubStatusDto> ItemList { get; set; }
        public Guid? SelectedItem { get; set; }

        private void SelectItem(Guid? item)
        {
            SelectedItem = item;
        }

        private async Task SubmitSelection()
        {
            await CustomSubStatus.DeleteMasterLeadSubStatusesById(SubStatusToDelete,SelectedItem);
            await JsRuntime.InvokeVoidAsync("alert", $"Successfully Deleted");
            await JsRuntime.InvokeVoidAsync("location.reload");
        }
    }
}

