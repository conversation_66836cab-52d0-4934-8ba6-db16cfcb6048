﻿function createDynamicForm(containerElement, parts) {

    for (let i = 1; i <= parts; i++)
    {
        let formElement = document.createElement("EditForm");

        let labelElement = document.createElement("label");
        labelElement.innerText = "Part Payement Amount";
        formElement.appendChild(labelElement);
        let inputElement = document.createElement("input");
        inputElement.type = "text";
        inputElement.name = "PartPayment";
        formElement.appendChild(inputElement);

        let newlabelElement = document.createElement("label");
        newlabelElement.innerText = "Part Payment Date";
        formElement.appendChild(newlabelElement);
        let newInputElement = document.createElement("input");
        newInputElement.type = "Date";
        newInputElement.name = "PartPaymentDate";
        formElement.appendChild(newInputElement);

        containerElement.appendChild(formElement);
    }
}

window.createTenantPayment = (elementId) => {
    const element = document.getElementById(elementId);
    let data = {};
    let acceptData = () => {
        data["Number"] = inputElement.value;
        Console.log(data);
        return acceptData;
    }
    
}
