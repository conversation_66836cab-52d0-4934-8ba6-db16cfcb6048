﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Notification
{
    public class NotificationModelDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public string? Title { get; set; }
        public string? Body { get; set; }
    }

    public class EmailModelDto 
    {
        public List<Guid> UserIds { get; set; } = new();
        public string? Title { get; set; }
        public string? Body { get; set; }
        public string? Sender { get; set; }
        public string? Subject { get; set; }
        public string? ToRecipients { get; set; }
        public string? CcRecipients { get; set; }
        public string? BccRecipients { get; set; }
        public string? ContentBody { get; set; }
        public string? AttachedFile { get; set; }
    }
}
