﻿using Azure;
using Blazored.LocalStorage;
using DocumentFormat.OpenXml.Vml.Spreadsheet;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Utility;
using Lrb.Admin.UI.Pages.Login;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Radzen.Blazor.Rendering;
using Radzen.Blazor;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using Microsoft.AspNetCore.Components.Web;
using Blazored.Modal;
using Lrb.Admin.UI.Modal.Subscription;
using Blazored.Modal.Services;
using Microsoft.IdentityModel.Tokens;

namespace Lrb.Admin.UI.Pages.Tenant
{
    public partial class TenantMaster
    {
        [Inject]
        public IModalService Modal { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }

        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public SpinnerService SpinnerService { get; set; }
        public GetAllTenantParameter Filter { get; set; } = new();
        public int PageSize { get; set; } = 10;
        public int PageNumber { get; set; } = 1;
        public double MaxPageNumber { get; set; }
        public int? TotalCount { get; set; }
        public int UserLimit { get; set; }

        public List<int> Count = new() { 10, 20, 30, 40, 50, 100 };
        public ObservableCollection<GetTenantModel> Cities { get; set; } = new();
        public int ShowingCount { get; set; }
        public ObservableCollection<GetTenantModel> Collection { get; set; } = new();
        public List<GetTenantModel> List { get; set; } = new();
        public List<string> LicenseMonitor { get; set; } = new() { "Licenses > Users", "Licenses < Users", "Licenses = Users" };
        public string? Token { get; set; }
        public LogoutButton ForceLogout { get; set; } = new();
        public RadzenButton? button { get; set; }
        public Popup popup;
        public List<int> DropdownValues { get; set; } = new List<int> { 10, 25, 50, 100, 500 };
        private ElementReference spanRef;
        public bool IsDataFound { get; set; } = true;
        protected override async Task OnInitializedAsync()
        {
            try
            {
                //await TenantService.UpdateIsDormentedAsync();
                await GetTenantsAsync();
                UserLimit = await VendorService.GetUserLImitAsync(Filter.TenantId);
            }
            catch (Exception ex)
            {

            }
        }

        private async Task HandleKeyDown(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                await GetTenantsAsync();
            }
        }

        public async Task OpenFilter()
        {
            var parameter = new ModalParameters
            {
                {nameof(TenantMasterFilter.PageSize),PageSize },
                {nameof(TenantMasterFilter.PageNumber),PageNumber },
                {nameof(TenantMasterFilter.Filter),Filter },
                {nameof(TenantMasterFilter.Count),Count },
                {nameof(TenantMasterFilter.LicenseMonitor),LicenseMonitor },
                {nameof(TenantMasterFilter.GetTenantsAsync),GetTenantsAsync}
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var model = Modal.Show<TenantMasterFilter>("Tenant Master Filter", parameter, options);
        }
        

        public async Task GetTenantsAsync()
        {
            try
            {
                if (PageSize > Filter.PageSize)
                {
                    PageNumber = 1;
                }
                await GetTrueFilter();
                PagedResponse<GetTenantModel, string> response = null;
                if (Filter == null)
                {
                    TotalCount = 0;
                }
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await TenantService.GetAllDataAsync(Filter);
                });
                while (!fetchTask.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    SpinnerService.Hide();
                    if(Collection == null)
                    {
                        IsDataFound = false;
                    }
                    else
                    {
                        IsDataFound = true;
                    }
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetTenantModel>(response.Items);
                    if (Collection.Count() <= 0)
                    {
                        IsDataFound = false;
                    }
                    else
                    {
                        IsDataFound = true;
                    }
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = Collection.Count;
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    if (userActivityHistory == null)
                    {
                        await ForceLogout.LogoutTenant();
                    }
                    StateHasChanged();
                    AdminUserActivity userActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = userActivityHistory.TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.TenantMasterProfile,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(userActivity);
                    //await popup.CloseAsync();
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        protected async Task GetTrueFilter()
        {
            var tenants = Filter.GetType().GetProperties();
            foreach (var tenant in tenants)
            {
                if (tenant.GetValue(Filter) != null && tenant.GetValue(Filter).ToString() == "Select")
                {
                    tenant.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }

        public void Paginate(int page)
        {
            PageNumber = page;
        }
        private async Task IncreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber >= (Math.Ceiling(MaxPageNumber)))
            {
                PageNumber++;
            }
            await GetTenantsAsync();
        }

        private async Task DecreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetTenantsAsync();
        }

        public async Task SortColumnAsync()
        {
            PagedResponse<GetTenantModel, string> response = await TenantService.GetAllDataAsync(Filter);
            if (response != null && response.Items != null)
            {
                Collection.Clear();
                Collection = new ObservableCollection<GetTenantModel>(response.Items);
                TotalCount = (int)response.TotalCount;
                MaxPageNumber = (double)TotalCount / (double)PageSize;
                ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
            }
        }

        private string GetSortStyle(string columnName)
        {
            if (Filter.Sort != columnName)
            {
                return string.Empty;
            }
            if (Filter.IsDecending)
            {
                return "sort up";
            }
            else
            {
                return "sort down";
            }
        }
        private async Task NavigateToComponent(string tenantId)
        {
            JsRuntime.InvokeVoidAsync("open", $"/tenant/{tenantId}", "_blank");
        }


        private async Task DownloadTenantAsync()
        {
            try
            {
                List<TenantViewModelExport> list = await TenantService.GetExportAllTenantAsync(Filter);

                foreach (var item in list)
                {
                    if (DateTime.TryParseExact(item.DateOfOnBoard, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime onBoardDate))
                    {
                        item.DateOfOnBoard = onBoardDate.ToString("dd/MM/yyyy");
                    }

                    if (DateTime.TryParseExact(item.LicenseValidity, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime licenseValidityDate))
                    {
                        item.LicenseValidity = licenseValidityDate.ToString("dd/MM/yyyy");
                    }
                }


                byte[] data = ExcelHelper.CreateExcelfromList(list);
                string fileName = "tenant_data.xlsx";
                string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }



        private async Task DownloadCompleteTenantDataAsync()
        {
            try
            {
                List<TenantCompleteDetailsDto> list = await TenantService.GetCompleteTenantData();
                byte[] data = ExcelHelper.CreateExcelfromList(list);
                string fileName = "complete_tenant_data.xlsx";
                string contentType = "Data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            catch (Exception ex) { }


        }
    }
}