﻿using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Common;
using Lrb.Admin.Data.Models.Roles;
using Lrb.Admin.Data.Models.User;
using System.ComponentModel.DataAnnotations;

namespace Lrb.Admin.Data.Models.Tenant
{
    public class CreateTenantWithAdminUserModel : CreateTenantModel
    {
        [Required]
        public CreateUserModel AdminUser { get; set; }
        [Required]
        public List<UserRoleModel>? UserRoles { get; set; }
        public CreateTenantWithAdminUserModel()
        {
            UserRoles = new List<UserRoleModel>();
            AdminUser = new CreateUserModel();
        }
    }

    public class CreateTenantModel  
    {
        [Required(ErrorMessage ="Enter Company Domain Id")]
        public string Id { get; set; } = default!;

        [Required(ErrorMessage ="Enter Company Name")]
        public string Name { get; set; } = default!;
        public string? Identifier { get; set; } = string.Empty;
        public string? ConnectionString { get; set; } = string.Empty;
        public string? ReadReplicaConnectionString { get; set; } = string.Empty;
        public string? IsActive { get; set; } = default!;
        public string? DisplayPrefix { get; set; } = string.Empty;
        public string? ResolutionKeys { get; set;} = string.Empty;

        [Required(ErrorMessage ="Enter Email Id")]
        [EmailAddress(ErrorMessage ="Enter Valid email address")]
        public string? AdminEmail { get; set; }
        public DateTime? ValidUpto { get; set; }
        public string? Issuer { get; set; }
        [Required]
        public OrganizationModel OrganizationInfo { get; set; }
        public CreateTenantModel()
        {
            OrganizationInfo = new();
        }
        //public bool? IsDubaiTenant { get; set; }
    }
}
public class OrganizationModel 
{
    [Required (ErrorMessage ="Enter OgranizationType")]
    public OrganizationType OrganizationType { get; set; }
    public string? LogoImgUrl { get; set; }
    public string? BanerImgUrl { get; set; }
    public string? RERANumber { get; set; }

    [Required]
    [MinLength(10, ErrorMessage = "Contact Number Must be atleast 10 digits")]
    public string? PhoneNumber { get; set; }
    public string? GSTNumber { get; set; }
    public bool? ShouldHideSubscription { get; set; } = false;

    [Required(ErrorMessage = "Enter Address")]
    public AddressModel? Address { get; set; }
    public OrganizationModel()
    {
        Address = new();
    }
}
