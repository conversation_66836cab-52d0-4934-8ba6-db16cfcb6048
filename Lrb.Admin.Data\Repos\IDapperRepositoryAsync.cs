﻿using System.Data;

namespace Lrb.Admin.Data.Repos
{
    public interface IDapperRepositoryAsync
    {

        /// <summary>
        /// Get an <see cref="IReadOnlyList{T}"/> using raw sql string with parameters.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="sql">The sql string.</param>
        /// <param name="param">The paramters in the sql string.</param>
        /// <param name="transaction">The transaction to be performed.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
        /// <returns>Returns <see cref="Task"/> of <see cref="IReadOnlyCollection{T}"/>.</returns>
        Task<IReadOnlyList<T>> QueryAsync<T>(string sql,string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a <typeparamref name="T"/> using raw sql string with parameters.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="sql">The sql string.</param>
        /// <param name="param">The paramters in the sql string.</param>
        /// <param name="transaction">The transaction to be performed.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
        /// <returns>Returns <see cref="Task"/> of <typeparamref name="T"/>.</returns>
        Task<T?> QueryFirstOrDefaultAsync<T>(string sql, string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a <typeparamref name="T"/> using raw sql string with parameters.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="sql">The sql string.</param>
        /// <param name="param">The paramters in the sql string.</param>
        /// <param name="transaction">The transaction to be performed.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
        /// <returns>Returns <see cref="Task"/> of <typeparamref name="T"/>.</returns>
        Task<T> QuerySingleAsync<T>(string sql, string? connectionString, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Call any Stored Procedure
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="schemaName"></param>
        /// <param name="spName"></param>
        /// <param name="param"></param>
        /// <returns>Enumerable of "T"</returns>
        Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string schemaName, string? connectionString, string spName, object param);
    }
}
