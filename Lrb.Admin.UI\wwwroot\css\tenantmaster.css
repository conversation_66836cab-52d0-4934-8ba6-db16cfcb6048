﻿/*@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
@import url('c:\users\<USER>\source\repos\leadrat-black-admin\lrb.admin.ui\shared\mainlayout.razor.css');
@import url('tenantmaster.css');*/



/*     UI For Mobile Devices     */
/*@media (min-width:320px) and (max-width: 476px) {
    * {
        font-family: 'Helvetica';
    }

    .filters {
        border: 2px solid black;
        width: 100%;
        padding: 1% 1%;
        overflow: scroll;
    }

    .filters-left {
        float: left;
        width: 80%;
    }

    .form-group {
        margin: 10px;
        display: inline-grid;
    }

    .filters-right {
        width: 25%;
        padding-top: 6%;
    }

    .table-header {
        border: 1px solid black;
        display: flex;
        padding: 1% 0;
        width: 100%;
        margin: auto;
        align-items: end;
        flex-wrap: wrap;
    }

    .table-refresh {
        width: 24%;
    }
    .add-new{
        width:60%;
    }

    .table-serach {*/
        /*padding-top:2%;*/
        /*width: 51%
    }

    .table-page {
        padding-top: 2%;
        padding-left: 2%;
    }

    .table-refresh {
        padding: 0 1%;
    }

    .table-serach {
        padding-left: 1%
    }

        .table-serach > input {
            width: 80%
        }

    .properties {
        border: 1px solid black;
        overflow: auto;
    }

    .sort-link {
        text-decoration: underline;
        text-decoration-color: blue;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    td {
        padding: 10px;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    th {
        background: #009879;
        color: white;
        font-weight: bold;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-1 {
        display: -webkit-inline-box;
        width: 100%;
        margin-top: 2%;
    }

    .table-count {
        border: 2px solid black;
        font-weight: bold;
        border-radius: 10px;
        padding: 10px;
    }

    .table-pagination-filter {
        border: 2px solid black;
        border-radius: 10px;
        margin-left: 72%;
    }

    .table table-bordered{
        
    }
}*/

/*----------------------------------------------------------------------------------------*/
/*     UI For IPAD  Devices     */

/*@media (min-width:477px) and (max-width:768px) {
    * {
        font-family: 'Helvetica';
    }

    .filters {
        border: 2px solid black;
        width: 100%;
        padding: 1% 1%;
        overflow: scroll;
    }

    .filters-left {
        float: left;
    }

    .form-group {
        margin: 10px;
        display: inline-grid;
    }

    .filters-right {
        width: 15%;
        padding-top: 10px;
    }

    .table-header {
        border: 1px solid black;
        display: flex;
        width: 100%;
        padding: 1% 0;
        margin: auto;
        align-items: end;
    }

    .add-new {
        padding: 0 1%;
    }

    .table-refresh {
        padding: 0 1%;
    }

    .table-serach {
        padding: 0 1%;
    }

        .table-serach > input {
            width: 80%
        }

    .properties {
        border: 1px solid black;
        overflow: auto;
    }

    .sort-link {
        text-decoration: underline;
        text-decoration-color: blue;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    td {
        padding: 10px;
    }
    
    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table th {
        background: #333;
        color: white;
        font-weight: bold;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-1 {
        display: flex;
        width: 100%;
        margin-top: 2%;
    }

    .table-count {
        border: 2px solid black;
        font-weight: bold;
    }

    .table-pagination-filter {
        border: 2px solid black;
        margin-left: 52%
    }
}*/


/*----------------------------------------------------------------------------------------*/
/*     UI For Laptop     */

/*@media (min-width:769px) {
    * {
        font-family: 'Helvetica';
    }

    .filters {
        border: 2px solid black;
        width: 100%;
        padding: 1% 1%;
        overflow: scroll;
    }

    .filters-left {
        float: left;
        width: 90%;
    }

    .form-group {
        margin: 10px;
        display: inline-grid;
    }

    .filters-right {
        width: 10%;
        padding-top: 10px;
    }

    .table-header {
        border: 1px solid black;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        width: 100%;
        padding: 1% 0;
        align-items: end;
    }
    .table-header-1 {*/
        /*border: 1px solid black;*/
        /*display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        width: 100%;*/
        /*padding: 1% 0;*/
        /*align-items: end;
        gap:1px;
    }
    .add-new {
        padding: 0 0.2%;
        width:auto;
    }

    .table-refresh {
        padding: 0 0.2%;
        width: auto;
    }

    .download-tenant {
        padding: 0 0.2%;
        width: auto;
    }

    .download-complete-tenant {
        padding: 0 0.2%;
        width: auto;
    }

    .table-serach {
        padding-left: 0.2%;
        width: auto;
    }

        .table-serach > input {
            width: 100%
        }

    .table-page {
        margin: auto;
        padding-right:1%;
        width: auto;
    }

    .properties {
        border: 1px solid black;
        overflow: scroll;
    }

    .sort-link {
        text-decoration: underline;
        text-decoration-color: blue;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    td {
        padding: 10px;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    th {
        background: #009879;
        color: white;
        font-weight: bold;
        width: 150px;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-1 {
        display: flex;
        text-align:center;
        border-radius:15px;
        align-content:center;
        align-items:center;
        width: 100%;
    }

    .table-count {
        border: 2px solid black;
        font-weight: bold;
        border-radius:10px;
        padding:10px;
    }

    .table-pagination-filter {
        border: 2px solid black;
        border-radius:10px;
        margin-left: 72%;
    }
}*/
