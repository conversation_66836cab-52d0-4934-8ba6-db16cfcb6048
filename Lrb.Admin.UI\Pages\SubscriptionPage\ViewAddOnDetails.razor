﻿<div class="View-AddOnSubs">
    <svg width="114" height="33" viewBox="0 0 114 33" fill="none" xmlns="http://www.w3.org/2000/svg" class="AddonButton" @onclick=" async () => { await AddOnSubscriptionDataById();}">
        <rect width="114" height="33" rx="4" fill="#343739" />
        <path d="M13.176 20.5L16.179 12.8H17.521L20.502 20.5H19.017L17.367 16.122C17.3303 16.034 17.279 15.8947 17.213 15.704C17.1543 15.5133 17.0883 15.308 17.015 15.088C16.9417 14.8607 16.8757 14.6517 16.817 14.461C16.7583 14.263 16.7143 14.12 16.685 14.032L16.96 14.021C16.916 14.1677 16.8647 14.3327 16.806 14.516C16.7473 14.6993 16.685 14.89 16.619 15.088C16.553 15.286 16.487 15.4767 16.421 15.66C16.3623 15.8433 16.3073 16.0083 16.256 16.155L14.606 20.5H13.176ZM14.54 18.74L15.035 17.497H18.544L19.072 18.74H14.54ZM23.5898 20.61C23.0765 20.61 22.6145 20.4817 22.2038 20.225C21.8005 19.961 21.4778 19.6053 21.2358 19.158C21.0012 18.7033 20.8838 18.1827 20.8838 17.596C20.8838 17.0167 21.0012 16.4997 21.2358 16.045C21.4705 15.5903 21.7895 15.2347 22.1928 14.978C22.6035 14.714 23.0655 14.582 23.5788 14.582C23.8575 14.582 24.1252 14.626 24.3818 14.714C24.6458 14.802 24.8805 14.923 25.0858 15.077C25.2912 15.2237 25.4525 15.3887 25.5698 15.572C25.6945 15.7553 25.7642 15.946 25.7788 16.144L25.4158 16.188V12.36H26.7688V20.5H25.4598L25.4268 19.136L25.6908 19.158C25.6835 19.3413 25.6175 19.5173 25.4928 19.686C25.3755 19.8547 25.2215 20.0087 25.0308 20.148C24.8402 20.2873 24.6165 20.401 24.3598 20.489C24.1105 20.5697 23.8538 20.61 23.5898 20.61ZM23.8538 19.466C24.1692 19.466 24.4478 19.3853 24.6898 19.224C24.9318 19.0627 25.1188 18.8427 25.2508 18.564C25.3902 18.2853 25.4598 17.9627 25.4598 17.596C25.4598 17.2367 25.3902 16.9177 25.2508 16.639C25.1188 16.353 24.9318 16.133 24.6898 15.979C24.4478 15.8177 24.1692 15.737 23.8538 15.737C23.5385 15.737 23.2598 15.8177 23.0178 15.979C22.7758 16.133 22.5852 16.353 22.4458 16.639C22.3138 16.9177 22.2478 17.2367 22.2478 17.596C22.2478 17.9627 22.3138 18.2853 22.4458 18.564C22.5852 18.8427 22.7758 19.0627 23.0178 19.224C23.2598 19.3853 23.5385 19.466 23.8538 19.466ZM30.6367 20.61C30.1234 20.61 29.6614 20.4817 29.2507 20.225C28.8474 19.961 28.5247 19.6053 28.2827 19.158C28.048 18.7033 27.9307 18.1827 27.9307 17.596C27.9307 17.0167 28.048 16.4997 28.2827 16.045C28.5174 15.5903 28.8364 15.2347 29.2397 14.978C29.6504 14.714 30.1124 14.582 30.6257 14.582C30.9044 14.582 31.172 14.626 31.4287 14.714C31.6927 14.802 31.9274 14.923 32.1327 15.077C32.338 15.2237 32.4994 15.3887 32.6167 15.572C32.7414 15.7553 32.811 15.946 32.8257 16.144L32.4627 16.188V12.36H33.8157V20.5H32.5067L32.4737 19.136L32.7377 19.158C32.7304 19.3413 32.6644 19.5173 32.5397 19.686C32.4224 19.8547 32.2684 20.0087 32.0777 20.148C31.887 20.2873 31.6634 20.401 31.4067 20.489C31.1574 20.5697 30.9007 20.61 30.6367 20.61ZM30.9007 19.466C31.216 19.466 31.4947 19.3853 31.7367 19.224C31.9787 19.0627 32.1657 18.8427 32.2977 18.564C32.437 18.2853 32.5067 17.9627 32.5067 17.596C32.5067 17.2367 32.437 16.9177 32.2977 16.639C32.1657 16.353 31.9787 16.133 31.7367 15.979C31.4947 15.8177 31.216 15.737 30.9007 15.737C30.5854 15.737 30.3067 15.8177 30.0647 15.979C29.8227 16.133 29.632 16.353 29.4927 16.639C29.3607 16.9177 29.2947 17.2367 29.2947 17.596C29.2947 17.9627 29.3607 18.2853 29.4927 18.564C29.632 18.8427 29.8227 19.0627 30.0647 19.224C30.3067 19.3853 30.5854 19.466 30.9007 19.466ZM38.827 20.5V12.8H40.114L44.58 18.817L44.338 18.861C44.3086 18.6557 44.283 18.4467 44.261 18.234C44.239 18.014 44.217 17.7867 44.195 17.552C44.1803 17.3173 44.1656 17.0717 44.151 16.815C44.1436 16.5583 44.1363 16.2907 44.129 16.012C44.1216 15.726 44.118 15.4253 44.118 15.11V12.8H45.537V20.5H44.228L39.74 14.571L40.026 14.494C40.0626 14.9047 40.092 15.2567 40.114 15.55C40.1433 15.836 40.1653 16.0853 40.18 16.298C40.1946 16.5033 40.2056 16.6757 40.213 16.815C40.2276 16.9543 40.235 17.0827 40.235 17.2C40.2423 17.31 40.246 17.4163 40.246 17.519V20.5H38.827ZM50.0783 20.61C49.4697 20.61 48.9307 20.4817 48.4613 20.225C47.9993 19.9683 47.6363 19.62 47.3723 19.18C47.1157 18.7327 46.9873 18.223 46.9873 17.651C46.9873 17.1963 47.0607 16.782 47.2073 16.408C47.354 16.034 47.5557 15.7113 47.8123 15.44C48.0763 15.1613 48.388 14.9487 48.7473 14.802C49.114 14.648 49.5137 14.571 49.9463 14.571C50.3277 14.571 50.6833 14.6443 51.0133 14.791C51.3433 14.9377 51.6293 15.1393 51.8713 15.396C52.1133 15.6453 52.2967 15.946 52.4213 16.298C52.5533 16.6427 52.6157 17.0203 52.6083 17.431L52.5973 17.904H47.9003L47.6473 16.98H51.4643L51.2883 17.167V16.925C51.2663 16.6977 51.193 16.4997 51.0683 16.331C50.9437 16.155 50.7823 16.0193 50.5843 15.924C50.3937 15.8213 50.181 15.77 49.9463 15.77C49.587 15.77 49.2827 15.8397 49.0333 15.979C48.7913 16.1183 48.608 16.32 48.4833 16.584C48.3587 16.8407 48.2963 17.1633 48.2963 17.552C48.2963 17.926 48.3733 18.2523 48.5273 18.531C48.6887 18.8097 48.9123 19.026 49.1983 19.18C49.4917 19.3267 49.8327 19.4 50.2213 19.4C50.4927 19.4 50.742 19.356 50.9693 19.268C51.1967 19.18 51.4423 19.0223 51.7063 18.795L52.3773 19.73C52.1793 19.9133 51.952 20.071 51.6953 20.203C51.446 20.3277 51.182 20.4267 50.9033 20.5C50.6247 20.5733 50.3497 20.61 50.0783 20.61ZM55.0632 20.5L53.1162 14.703L54.5022 14.692L55.8002 18.828L55.5582 18.806L56.9662 15.572H57.7802L59.2212 18.806L58.9352 18.839L60.2442 14.703H61.6302L59.6722 20.5H58.7042L57.2192 17.024L57.4282 17.046L56.0202 20.5H55.0632ZM65.2434 20.5L68.2464 12.8H69.5884L72.5694 20.5H71.0844L69.4344 16.122C69.3977 16.034 69.3464 15.8947 69.2804 15.704C69.2217 15.5133 69.1557 15.308 69.0824 15.088C69.009 14.8607 68.943 14.6517 68.8844 14.461C68.8257 14.263 68.7817 14.12 68.7524 14.032L69.0274 14.021C68.9834 14.1677 68.932 14.3327 68.8734 14.516C68.8147 14.6993 68.7524 14.89 68.6864 15.088C68.6204 15.286 68.5544 15.4767 68.4884 15.66C68.4297 15.8433 68.3747 16.0083 68.3234 16.155L66.6734 20.5H65.2434ZM66.6074 18.74L67.1024 17.497H70.6114L71.1394 18.74H66.6074ZM75.6572 20.61C75.1439 20.61 74.6819 20.4817 74.2712 20.225C73.8679 19.961 73.5452 19.6053 73.3032 19.158C73.0685 18.7033 72.9512 18.1827 72.9512 17.596C72.9512 17.0167 73.0685 16.4997 73.3032 16.045C73.5379 15.5903 73.8569 15.2347 74.2602 14.978C74.6709 14.714 75.1329 14.582 75.6462 14.582C75.9249 14.582 76.1925 14.626 76.4492 14.714C76.7132 14.802 76.9479 14.923 77.1532 15.077C77.3585 15.2237 77.5199 15.3887 77.6372 15.572C77.7619 15.7553 77.8315 15.946 77.8462 16.144L77.4832 16.188V12.36H78.8362V20.5H77.5272L77.4942 19.136L77.7582 19.158C77.7509 19.3413 77.6849 19.5173 77.5602 19.686C77.4429 19.8547 77.2889 20.0087 77.0982 20.148C76.9075 20.2873 76.6839 20.401 76.4272 20.489C76.1779 20.5697 75.9212 20.61 75.6572 20.61ZM75.9212 19.466C76.2365 19.466 76.5152 19.3853 76.7572 19.224C76.9992 19.0627 77.1862 18.8427 77.3182 18.564C77.4575 18.2853 77.5272 17.9627 77.5272 17.596C77.5272 17.2367 77.4575 16.9177 77.3182 16.639C77.1862 16.353 76.9992 16.133 76.7572 15.979C76.5152 15.8177 76.2365 15.737 75.9212 15.737C75.6059 15.737 75.3272 15.8177 75.0852 15.979C74.8432 16.133 74.6525 16.353 74.5132 16.639C74.3812 16.9177 74.3152 17.2367 74.3152 17.596C74.3152 17.9627 74.3812 18.2853 74.5132 18.564C74.6525 18.8427 74.8432 19.0627 75.0852 19.224C75.3272 19.3853 75.6059 19.466 75.9212 19.466ZM82.7041 20.61C82.1907 20.61 81.7287 20.4817 81.3181 20.225C80.9147 19.961 80.5921 19.6053 80.3501 19.158C80.1154 18.7033 79.9981 18.1827 79.9981 17.596C79.9981 17.0167 80.1154 16.4997 80.3501 16.045C80.5847 15.5903 80.9037 15.2347 81.3071 14.978C81.7177 14.714 82.1797 14.582 82.6931 14.582C82.9717 14.582 83.2394 14.626 83.4961 14.714C83.7601 14.802 83.9947 14.923 84.2001 15.077C84.4054 15.2237 84.5667 15.3887 84.6841 15.572C84.8087 15.7553 84.8784 15.946 84.8931 16.144L84.5301 16.188V12.36H85.8831V20.5H84.5741L84.5411 19.136L84.8051 19.158C84.7977 19.3413 84.7317 19.5173 84.6071 19.686C84.4897 19.8547 84.3357 20.0087 84.1451 20.148C83.9544 20.2873 83.7307 20.401 83.4741 20.489C83.2247 20.5697 82.9681 20.61 82.7041 20.61ZM82.9681 19.466C83.2834 19.466 83.5621 19.3853 83.8041 19.224C84.0461 19.0627 84.2331 18.8427 84.3651 18.564C84.5044 18.2853 84.5741 17.9627 84.5741 17.596C84.5741 17.2367 84.5044 16.9177 84.3651 16.639C84.2331 16.353 84.0461 16.133 83.8041 15.979C83.5621 15.8177 83.2834 15.737 82.9681 15.737C82.6527 15.737 82.3741 15.8177 82.1321 15.979C81.8901 16.133 81.6994 16.353 81.5601 16.639C81.4281 16.9177 81.3621 17.2367 81.3621 17.596C81.3621 17.9627 81.4281 18.2853 81.5601 18.564C81.6994 18.8427 81.8901 19.0627 82.1321 19.224C82.3741 19.3853 82.6527 19.466 82.9681 19.466ZM90.07 20.61C89.4906 20.61 88.9736 20.4817 88.519 20.225C88.0643 19.961 87.705 19.6017 87.441 19.147C87.177 18.6923 87.045 18.1753 87.045 17.596C87.045 17.0167 87.177 16.4997 87.441 16.045C87.705 15.5903 88.0643 15.2347 88.519 14.978C88.9736 14.714 89.4906 14.582 90.07 14.582C90.642 14.582 91.1553 14.714 91.61 14.978C92.0646 15.2347 92.4203 15.5903 92.677 16.045C92.941 16.4997 93.073 17.0167 93.073 17.596C93.073 18.1753 92.941 18.6923 92.677 19.147C92.4203 19.6017 92.0646 19.961 91.61 20.225C91.1553 20.4817 90.642 20.61 90.07 20.61ZM90.07 19.411C90.3853 19.411 90.664 19.3303 90.906 19.169C91.1553 19.0077 91.3496 18.7913 91.489 18.52C91.6356 18.2487 91.7053 17.9407 91.698 17.596C91.7053 17.244 91.6356 16.9323 91.489 16.661C91.3496 16.3897 91.1553 16.177 90.906 16.023C90.664 15.8617 90.3853 15.781 90.07 15.781C89.7546 15.781 89.4686 15.8617 89.212 16.023C88.9626 16.177 88.7683 16.3933 88.629 16.672C88.4896 16.9433 88.42 17.2513 88.42 17.596C88.42 17.9407 88.4896 18.2487 88.629 18.52C88.7683 18.7913 88.9626 19.0077 89.212 19.169C89.4686 19.3303 89.7546 19.411 90.07 19.411ZM94.2832 20.5V14.703H95.6032L95.6252 15.891L95.3722 16.023C95.4455 15.759 95.5885 15.5207 95.8012 15.308C96.0139 15.088 96.2669 14.912 96.5602 14.78C96.8535 14.648 97.1542 14.582 97.4622 14.582C97.9022 14.582 98.2689 14.67 98.5622 14.846C98.8629 15.022 99.0865 15.286 99.2332 15.638C99.3872 15.99 99.4642 16.43 99.4642 16.958V20.5H98.1112V17.057C98.1112 16.7637 98.0709 16.5217 97.9902 16.331C97.9095 16.133 97.7849 15.99 97.6162 15.902C97.4475 15.8067 97.2422 15.7627 97.0002 15.77C96.8022 15.77 96.6189 15.803 96.4502 15.869C96.2889 15.9277 96.1459 16.0157 96.0212 16.133C95.9039 16.243 95.8085 16.3713 95.7352 16.518C95.6692 16.6647 95.6362 16.826 95.6362 17.002V20.5H94.9652C94.8332 20.5 94.7085 20.5 94.5912 20.5C94.4812 20.5 94.3785 20.5 94.2832 20.5Z" fill="white" />
    </svg>
</div>
<div class="all-tables">
    <table>
            <tr>
                <th>Sl No</th>
                <th>Payment Date</th>
                <th>No Of License</th>
                <th>Net Amount</th>
                <th>GST Amount</th>
                <th>Total Amount</th>
                <th>Paid Amount</th>
                <th>Pending Amount</th>
                <th>Action</th>
            </tr>
        @foreach (var items in AddOnSubs.Select((value, i) => new { value, i }))
        {
            <tr class=@(items.value.IsDeleted ? "Isdeleted" : "")>
                <td>@(items.i + 1)</td>
                <td>@items.value?.PaymentDate?.ToString("dd/MM/yyyy")</td>
                <td>
                    @if (!@items.value.IsActive || items.value.IsDeleted)
                    {
                        <div>
                            @items.value?.SoldLicenses
                                @if (LoginAdminUserInfo.Role == Admin.Data.Models.Identity.Role.Admin)
                                {
                                <button class ="ActiveInactive-button" @onclick="() =>ToggleSubscriptionAddonIsActive(items.value.Id,true)">
                                        <b>X</b> 
                                    </button>
                                }

                        </div>
                    }
                    else
                    {
                        <div>
                            @items.value?.SoldLicenses
                                @if (LoginAdminUserInfo.Role == Admin.Data.Models.Identity.Role.Admin)
                                {
                                <button class="ActiveInactive-button" title="Active/InActive" @onclick="() =>ToggleSubscriptionAddonIsActive(items.value.Id,false)">
                                    <b>&#10004;</b>
                                    </button>
                                }

                        </div>
                    }
                </td>
                <td>@items.value?.NetAmount</td>
                <td>@items.value?.GSTAmount</td>
                <td>@items.value?.TotalAmount</td>
                <td>@items.value?.PaidAmount</td>
                <td>@items.value?.DueAmount</td>
                <td>
                    @* Payment *@
                    <span @onclick="@(async () => await GetPaymentsOnSubscription(@items.value?.Id))">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="20" height="20" rx="4" fill="#59A7EF" />
                            <g clip-path="url(#clip0_324_17806)">
                                <path d="M5.00056 10.0017C5.00056 8.68997 5.00056 7.37845 5.00056 6.06712C5.00056 5.42712 5.42235 5.00334 6.06019 5.00306C6.6727 5.00306 7.28214 5.00584 7.89298 5C7.99014 5 8.0024 5.0295 8.00212 5.11492C7.99878 5.94747 7.99989 6.78029 8.00017 7.61311C8.00017 8.05832 8.27607 8.33408 8.7193 8.33435H11.2807C11.7234 8.33435 11.9993 8.0561 11.9993 7.61256C11.9993 6.77649 11.9993 5.94032 11.9993 5.10407C11.9993 5.02699 12.0121 4.99694 12.0981 5.00056C12.2782 5.00835 12.46 5.00501 12.6393 5.00223C12.6914 5.00017 12.7433 5.00914 12.7916 5.02857C12.8399 5.04799 12.8836 5.07742 12.9197 5.11492C13.5745 5.77049 14.2299 6.4256 14.8859 7.08025C14.9233 7.11613 14.9529 7.15939 14.9727 7.20729C14.9925 7.25519 15.0022 7.30668 15.0011 7.35851C15.0011 9.56601 15.0011 11.7727 15.0011 13.9785C15.0011 14.5629 14.5646 14.993 13.9793 14.9964C13.7953 14.9964 13.6113 14.9917 13.4275 14.9989C13.344 15.0022 13.3337 14.9747 13.3337 14.9009C13.3361 13.9432 13.3361 12.9856 13.3337 12.0282C13.3337 11.5427 13.0439 11.1584 12.5898 11.0332C12.4958 11.0088 12.399 10.9972 12.3019 10.9987C10.7675 10.9987 9.23316 10.9987 7.69892 10.9987C7.10201 10.9987 6.6688 11.4322 6.6688 12.0282C6.66769 12.9823 6.66834 13.9364 6.67075 14.8906C6.67075 14.9761 6.65293 15.0019 6.56467 14.9972C6.32246 14.9833 6.07829 15.0178 5.83747 14.978C5.34746 14.897 5.00223 14.4897 5.00223 13.9863C5 12.6587 4.99944 11.3304 5.00056 10.0017Z" fill="white" />
                                <path d="M9.99421 14.9993C9.14032 14.9993 8.28643 14.9993 7.43282 15.0012C7.35096 15.0012 7.33176 14.9809 7.33203 14.9002C7.33482 13.9494 7.33371 12.9989 7.33399 12.0483C7.33399 11.7979 7.46317 11.668 7.71012 11.668H12.2917C12.5319 11.668 12.6664 11.801 12.6664 12.0397C12.6664 12.9972 12.6664 13.9547 12.6686 14.9122C12.6686 14.9873 12.6472 15.0012 12.5765 15.0009C11.716 14.9978 10.8552 14.9972 9.99421 14.9993Z" fill="white" />
                                <path d="M10.0097 5.00181C10.4229 5.00181 10.8361 5.00181 11.2492 5.00014C11.3172 5.00014 11.3344 5.01739 11.3341 5.08362C11.3315 5.91635 11.3315 6.74917 11.3341 7.58209C11.3341 7.64998 11.3169 7.66724 11.249 7.66696C10.4159 7.66436 9.58284 7.66436 8.74965 7.66696C8.68171 7.66696 8.66445 7.6497 8.66612 7.58209C8.66835 6.74917 8.66835 5.91635 8.66612 5.08362C8.66612 5.01572 8.68339 4.99819 8.75132 5.00014C9.16977 5.0032 9.58989 5.00181 10.0097 5.00181Z" fill="white" />
                            </g>
                            <defs>
                                <clipPath id="clip0_324_17806">
                                    <rect width="10" height="10" fill="white" transform="translate(5 5)" />
                                </clipPath>
                            </defs>
                            <title>Payment Details</title>
                        </svg>

                    </span>
                    <span @onclick="@(async () => await UpdateSubscriptionDataById(items.value?.Id))">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="20" height="20" rx="4" fill="#78AECC" />
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9889 5.68311C11.4543 5.215 12.2089 5.215 12.6743 5.68311L13.5171 6.5307C13.9825 6.9988 13.9825 7.75776 13.5171 8.22586L12.7699 8.97736C12.7112 8.94815 12.6495 8.91656 12.5855 8.88268C12.1548 8.65462 11.6437 8.33455 11.2623 7.95091C10.8808 7.56727 10.5626 7.05323 10.3358 6.62005C10.3021 6.55568 10.2707 6.4936 10.2417 6.4346L10.9889 5.68311ZM10.7154 8.50088C11.1668 8.95485 11.7408 9.31215 12.1955 9.55506L9.2571 12.5103C9.07476 12.6937 8.83819 12.8127 8.58291 12.8494L6.81315 13.1036C6.41987 13.1601 6.08278 12.8211 6.13896 12.4256L6.39178 10.6456C6.42825 10.3889 6.54654 10.151 6.72888 9.96757L9.66725 7.0123C9.90878 7.46954 10.264 8.0469 10.7154 8.50088ZM8.96837 13.8876C8.75482 13.8876 8.5817 14.0617 8.5817 14.2765C8.5817 14.4913 8.75482 14.6654 8.96837 14.6654H13.4795C13.693 14.6654 13.8661 14.4913 13.8661 14.2765C13.8661 14.0617 13.693 13.8876 13.4795 13.8876H8.96837Z" fill="white" />
                            <title>Update AddOn Subscription</title>
                        </svg>

                    </span>
                    <span @onclick="@(async () => await DeleteSubscription(items.value?.Id))">
                    @if (LoginAdminUserInfo.Role == Admin.Data.Models.Identity.Role.Admin)
                    {
                            
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="20" height="20" rx="4" fill="#EF595C" />
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0502 14.6654C12.0188 14.6654 12.8227 13.9085 12.8917 12.9315L13.2093 8.43469C12.2321 8.09011 11.1452 7.89804 10 7.89804C8.85484 7.89804 7.76794 8.09011 6.79073 8.43469L7.10837 12.9315C7.17737 13.9085 7.98125 14.6654 8.9498 14.6654H11.0502ZM9.07696 9.1815C9.26813 9.1815 9.42311 9.33821 9.42311 9.53153V12.3318C9.42311 12.5251 9.26813 12.6818 9.07696 12.6818C8.88579 12.6818 8.73081 12.5251 8.73081 12.3318V9.53153C8.73081 9.33821 8.88579 9.1815 9.07696 9.1815ZM11.2692 9.53153C11.2692 9.33821 11.1143 9.1815 10.9231 9.1815C10.7319 9.1815 10.5769 9.33821 10.5769 9.53153V12.3318C10.5769 12.5251 10.7319 12.6818 10.9231 12.6818C11.1143 12.6818 11.2692 12.5251 11.2692 12.3318V9.53153Z" fill="white" />
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10 5.33203C9.29904 5.33203 8.7308 5.90666 8.7308 6.61549V6.6928C7.63006 6.82826 6.60172 7.13726 5.69459 7.58429C5.52274 7.66898 5.45131 7.87851 5.53506 8.05229C5.61881 8.22607 5.82601 8.2983 5.99786 8.21361C7.14783 7.64691 8.52114 7.31556 10 7.31556C11.4789 7.31556 12.8522 7.64691 14.0021 8.21361C14.174 8.2983 14.3812 8.22607 14.4649 8.05229C14.5487 7.87851 14.4773 7.66898 14.3054 7.58429C13.3983 7.13726 12.37 6.82826 11.2692 6.6928V6.61549C11.2692 5.90666 10.701 5.33203 10 5.33203Z" fill="white" />
                                <title>Delete AddOn Subscription</title>
                            </svg>
                            
                    }
                    </span>
                </td>
            </tr>
        }
    </table>
</div>

