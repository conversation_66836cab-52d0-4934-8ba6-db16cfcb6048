﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class CustomMainStatusesDTO
    {
        public Guid Id { get; set; }
        public Guid BaseId { get; set; }
        public int Level {  get; set; }
        public string? Status { get; set; }
        public string? DisplayName {  get; set; }
        public string? ActionName { get; set; }
        public bool IsDeleted {  get; set; }
        public Guid CreatedBy {  get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid DeletedBy { get; set; }
        public DateTime? DeletedOn { get;set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public int OrderRank { get; set; }
        public string? StatusOrder {  get; set; }
    }
}
