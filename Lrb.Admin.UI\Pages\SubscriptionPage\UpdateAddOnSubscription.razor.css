﻿.form-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    background: #fff;
    padding: 1%;
    border-radius: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

    .form-group label {
        font-size: 14px;
        font-weight: 400;
    }

    .form-group input,
    .form-group select {
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 16px;
        height: 35px;
        width: 200px;
    }

        .form-group input:disabled {
            background-color: #f0f0f0;
        }

        .form-group input::placeholder,
        .form-group select::placeholder {
            color: #aaa;
            font-size: 14px;
        }

.rupeessymbol {
    position: relative;
}

    .rupeessymbol span {
        position: absolute;
        left: 10px;
        top: 27px;
        font-size: 16px;
    }

    .rupeessymbol input {
        padding-left: 20px;
        width: 100%;
    }

.percentagesymbol {
    position: relative;
}

    .percentagesymbol span {
        position: absolute;
        left: 170px;
        top: 28px;
        font-size: 16px;
    }

.OrganizationInfo {
    margin-bottom: 1%;
}

.AdminInfo {
    margin-bottom: 1%;
}

.PlanFinance {
    margin-bottom: 1%;
}

.Miscellaneous {
    margin-bottom: 1%;
}

.footer {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 14px;
    justify-content: flex-end;
}

    .footer span {
        cursor: pointer;
    }

@media only screen and (max-width: 700px){
    .form-container {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 10px;
        background: #fff;
        padding: 1%;
        border-radius: 10px;
    }
}