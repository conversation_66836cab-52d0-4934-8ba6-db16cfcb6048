﻿using DocumentFormat.OpenXml.Drawing.Diagrams;
using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class LRBPayments
    {
        public Guid Id { get; set; }
        public string? Description { get; set; }
        public PaymentType Type { get; set; }
        public double NetAmount { get; set; }
        public double GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public double PendingAmount { get; set; }
        public DateTime? NextDueDate { get; set; }
        public PaymentMode Mode { get; set; }
        public string? InvoiceNumber { get; set; }
        public string? InvoiceUrl { get; set; }
        public Guid? SubscriptionId { get; set; }
        public Guid? SubscriptionAddOnId { get; set; }
        public string TenantId { get; set; }
        public bool IsDeleted { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public double PaidAmount { get; set; }
        public double? TDS { get; set; }
        public int TDSUnit { get; set; }
        public string? OrderId { get; set; }
        public string? PaymentMode { get; set; }
    }
}
