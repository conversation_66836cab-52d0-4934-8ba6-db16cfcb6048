﻿using Blazored.Modal.Services;
using Blazored.Modal;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Modal.Subscription;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Blazored.LocalStorage;

namespace Lrb.Admin.UI.Pages.SubscriptionPage
{
    
    public partial class ViewAddOnDetails
    {
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public ITenantService TenantServices { get; set; }
        [Inject]
        public IModalService Modal { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public IAuthService AuthService { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        [Parameter]
        public List<LRBSubscriptionAddOns> AddOnSubs { get; set; }
        [Parameter]
        public Guid SubscriptionId { get; set; }
        public UserModelDto LoginAdminUserInfo { get; set; } = new();
        public AdminUserActivity AdminUser { get; set; } = new();
        public string? UserLoginToken { get; set; }
        public string? Token { get; set; }

        protected override async Task OnInitializedAsync()
        {
            Token = await LocalStorage.GetItemAsync<string>("token");
            AdminUser = await ActivityService.GetUserByTokenAsync(Token);
            LoginAdminUserInfo = await AuthService.GetUser(AdminUser.AdminUserId);

        }
        public async Task Close()
        {
            await JsRuntime.InvokeVoidAsync("location.reload");
        }



        //private async Task AddPayments()
        //{
        //    var parameter = new ModalParameters
        //    {
        //        { nameof(AddPayments.TenantId), TenantId},
        //        { nameof(AddPayments.SubscriptionId), SubscriptionId },
        //        { nameof(AddPayments.PaymentCount),AddOnSubs.Count() }
        //    };
        //    var option = new ModalOptions
        //    {
        //        Size = ModalSize.Automatic
        //    };
        //    var modal = Modal.Show<AddPayments>("Update Payment", parameter, option);
        //}

        public async Task UpdatePayments(LRBPayments model)
        {

            var parameter = new ModalParameters();
            parameter.Add(nameof(UpdatePayment.TenantId), TenantId);
            parameter.Add(nameof(UpdatePayment.SubscriptionId), SubscriptionId);
            parameter.Add(nameof(UpdatePayment.PaymentModel), model);
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<UpdatePayment>("Update Payment", parameter, options);
        }

        public async Task AddOnSubscriptionDataById()
        {
            var model = await SubscriptionService.GetTenantSubscriptionById(SubscriptionId, false);
            var parameter = new ModalParameters();
            parameter.Add(nameof(AddOnSubscription.TenantId), TenantId);
            parameter.Add(nameof(AddOnSubscription.SubscriptionModel), model);

            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<AddOnSubscription>("Add On Subscription", parameter, options);
        }
        public async Task GetPaymentsOnSubscription(Guid? subscriptionId)
        {
            var allPayments = SubscriptionService.GetTenantAllPaymentAsync(subscriptionId).Result.ToList();
            if (allPayments != null)
            {
                var parameter = new ModalParameters
                {
                    { nameof(PaymentDetails.TenantId), TenantId},
                    { nameof(PaymentDetails.Payments), allPayments},
                    { nameof(PaymentDetails.SubscriptionId), subscriptionId },
                    { nameof(PaymentDetails.IsAddOn), true }
                };
                var options = new ModalOptions
                {
                    Size = ModalSize.ExtraLarge
                };
                var model = Modal.Show<PaymentDetails>("AddOn Payment Details", parameter, options);
            }
        }
        public async Task UpdateSubscriptionDataById(Guid? subscriptionId)
        {
            var model = await SubscriptionService.GetAddOnSubscriptionById(subscriptionId);
            var parameter = new ModalParameters();
            parameter.Add(nameof(UpdateAddOnSubscription.TenantId), TenantId);
            parameter.Add(nameof(UpdateAddOnSubscription.SubscriptionId), subscriptionId);
            parameter.Add(nameof(UpdateAddOnSubscription.SubscriptionModel), model);
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<UpdateAddOnSubscription>("Update Addon Subscription", parameter, options);
        }
        public async Task ToggleSubscriptionAddonIsActive(Guid SubscriptionId, bool value)
        {
            if (await JsRuntime.InvokeAsync<bool>("confirm", "This Change will Affect Subscription, Are you Sure"))
            {
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity adminUserActivity = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = $"{TenantId} SubscriptionAddon changed to {value}",
                    Token = Token
                };
                await ActivityService.UserActivityAsync(adminUserActivity);

                bool result = await TenantServices.ToggleSubscriptionAddonIsActiveAsync(SubscriptionId, value);
                if (result)
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"AddOnSubscription Activated Successfully");
                    await JsRuntime.InvokeVoidAsync("location.reload");
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", "Something Went Wrong {ViewAddOn : 114}");
                }
            }
            else
            {
                await OnInitializedAsync();
            }

        }

        public async Task DeleteSubscription(Guid? AddonId)
        {
            var response = await SubscriptionService.DeleteAddonSubscriptionAsync(AddonId);
            if (response)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"AddOnSubscription Deleted Successfully");
                await JsRuntime.InvokeVoidAsync("location.reload");

            }
            else
            {
                await JsRuntime.InvokeVoidAsync("alert", "Something Went Wrong {ViewAddOn : 114}");
            }
        }
    }
}
