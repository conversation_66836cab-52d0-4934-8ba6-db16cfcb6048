﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Roles;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.User;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using PhoneNumbers;
using System.Text.RegularExpressions;

namespace Lrb.Admin.UI.Pages.User
{
    [Authorize(Roles = "Admin,Manager")]
    public partial class AddUser
    {
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public IUserService UserService { get; set; }
        [Inject]
        public IJSRuntime _jsRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public IRoleService RoleService { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        public GetTenantModel TenantModel { get; set; } = new();
        public string? ConnectionString { get; set; }
        public string? ReadConnectionString { get; set; }
        public string Token { get; set; }
        public CreateUserModel Model { get; set; } = new CreateUserModel();

        protected override async Task OnInitializedAsync()
        {
            TenantModel = await TenantService.GetTenantByIdAsync(TenantId);
        }

        public async Task AddUserAsync()
        {
            Model.TenantId = TenantId;
            try
            {
                
                ApplicationRole role = new();
                if (UserRole.Admin == Model.UserRole)
                {
                    role = await RoleService.GetAdminRole(Model.TenantId, TenantModel.ConnectionString);
                }
                else if (UserRole.Basic == Model.UserRole)
                {
                    role = await RoleService.GetBasicRoleAsync(Model.TenantId, TenantModel.ConnectionString);
                }
                else if (UserRole.SalesExecutive == Model.UserRole)
                {
                    role = await RoleService.GetSalesExecutiveRoleAsync(Model.TenantId, TenantModel.ConnectionString);
                }
                else if (UserRole.Manager == Model.UserRole)
                {
                    role = await RoleService.GetManagerRoleAsync(Model.TenantId, TenantModel.ConnectionString);
                }
                else if (UserRole.HR == Model.UserRole)
                {
                    role = await RoleService.GetHRRoleAsync(Model.TenantId, TenantModel.ConnectionString);
                }
                Model.UserRoles = new List<UserRoleModel>()
                {
                    new UserRoleModel()
                    {
                        Description = role.Description,
                        RoleId = role.Id,
                        RoleName = role.Name,
                        Enabled = true,
                    }
                };
                Model.UserName = Model.UserName.ToLower().Trim();
                if (await UserService.CheckIfEmailAlreadyExistsAsync(Model.Email,TenantModel.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Email already exists");
                    return;
                }
                if (await UserService.CheckIfUserNameExistsAsync(Model.UserName, TenantModel.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"UserName already exists");
                    return;
                }
                if (Model.UserName.Length < 6)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"UserName should contain atleast 6 letters");
                    return;
                }
                if (!await CheckingPhoneNumber(TenantModel.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Phone Number Is not valid");
                    return;
                }
                Model.Password = Model.Password.Trim();
                Model.ConfirmPassword = Model.ConfirmPassword.Trim();
                var response = await UserService.CreateUserAsync(Model);
                if (response != false)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", "User Added Successfully");
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity UserActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.AddUser + " " + Model.FirstName + " " + Model.LastName,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(UserActivity);
                }
                else
                {
                    await _jsRuntime.InvokeVoidAsync("alert", "Something went Wrong Try Again");
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<bool> CheckingPhoneNumber(string connectionString)
        {
            var phoneutil = PhoneNumberUtil.GetInstance();
            var DefaultCountryCode = await UserService.GetDefaultCountryCodeAsync(Model.TenantId, connectionString);
            int DefaultCountryCodeInt = int.Parse(DefaultCountryCode.TrimStart('+'));
            var DefaultCountryRegion = phoneutil.GetRegionCodeForCountryCode(DefaultCountryCodeInt);
            PhoneNumber phoneNumber = phoneutil.Parse(Model.PhoneNumber, DefaultCountryRegion);
            string phoneNumberee = ValidateContactNumbers(Model.PhoneNumber, DefaultCountryRegion);
            bool isValid = false;
            if(phoneNumberee != string.Empty)
            {
                isValid = true;
            }
            if (isValid)
            {
                Model.PhoneNumber = phoneutil.Format(phoneNumber, PhoneNumberFormat.E164);
            }
            return isValid;
        }

        private static string? ValidateContactNumbers(string? conactnum, string? code)
        {

            string defaultRegion = code;
            //string countrycode = globalsettings?.Countries?.FirstOrDefault()?.DefaultCallingCode;

            string mobileNumber = Regex.Replace(conactnum, "[^0-9]", "");
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            if (conactnum.StartsWith("0") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                mobileNumber = "+91" + mobileNumber.Substring(1);
                return mobileNumber;
            }
            if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                PhoneNumber number = phoneUtil.Parse("+" + mobileNumber, null);
                defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
            }
            //if (string.IsNullOrWhiteSpace(defaultRegion))
            //{
            //    List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countrycodes, new List<string>());
            //    defaultRegion = regionCodes.FirstOrDefault();
            //    if (string.IsNullOrWhiteSpace(defaultRegion))
            //    {
            //        defaultRegion = globalsettings.Countries?.FirstOrDefault()?.Code;
            //        countrycode = globalsettings.Countries?.FirstOrDefault()?.DefaultCallingCode;
            //    }
            //}

            PhoneNumber phoneNumber = phoneUtil.Parse(mobileNumber, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
            if (defaultRegion == "AE")
            {
                if (contactWithCountryCode.Length == 12)
                {
                    return contactWithCountryCode;
                }
            }

            bool isValid;
            if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
            {
                return contactWithCountryCode;

            }
            else if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                return "+" + mobileNumber;
            }
            //else if (string.IsNullOrWhiteSpace(code) && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            //{
            //    return mobileNumber;
            //}
            //else if (mobileNumber.Length > 6 && mobileNumber.Length < 20)
            //{
            //    return code + mobileNumber;
            //}
            else
            {
                return string.Empty;
            }
        }

        public async Task Reset()
        {
            var isConfirmed = await _jsRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                Model = new();
            }
        }
    }
}
