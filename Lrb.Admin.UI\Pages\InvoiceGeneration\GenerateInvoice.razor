﻿@* @page "/generate-invoice"

<h3>Generate Invoice</h3>

<div>
    <EditForm class="form-floating" Model="InvoiceDto">
        <DataAnnotationsValidator />
        <ValidationSummary />
        <div class="form-group">
            <label for="invoice_no">Invoice No</label>
            <InputText id="invoice_no" @bind-Value="InvoiceDto.InvoiceNo" class="form-control"></InputText>
        </div>
        <div class="form-group">
            <label for="invoice_date">Invoice Date</label>
            <InputDate id="invoice_date" @bind-Value="InvoiceDto.InvoiceDate" class="form-control"></InputDate>
        </div>
        <div class="form-group">
            <label id="payment_date">Payment Date</label>
            <InputDate @bind-Value="InvoiceDto.PaymentDate" class="form-control"></InputDate>
        </div>
        <div class="form-group">
           
            <label for="invoice_no">Company GST No</label>
            <InputText id="invoice_no" @bind-Value="InvoiceDto.FromGSTIN" class="form-control"></InputText>
        </div>
        <div class="form-group">
            <label for="invoice_no">Tenant GST No</label>
            <InputText id="invoice_no" @bind-Value="InvoiceDto.ToGSTIN" class="form-control"></InputText>
        </div>
        <div class="form-group">
            <label for="tenantid-filter" style="font-weight:bold">Search By TenantId</label>
            <InputSelect id="tenantid-filter" @bind-Value="InvoiceDto.TenantId">
                <option value="select">Select</option>
                @foreach (var tenantId in TenantIds)
                {
                    <option value="@tenantId">@tenantId</option>
                }
            </InputSelect>
        </div>

        <div>
            <button type="button" @onclick="Reset" class="btn btn-primary">Reset</button>
            <button @onclick="Generate" class="btn btn-primary">Generate</button>
        </div>
    </EditForm>
</div> *@

