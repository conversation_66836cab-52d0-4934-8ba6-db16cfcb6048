﻿using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Admin.Data
{
    public static class Startup
    {
        public static IServiceCollection AddDataLayer(this IServiceCollection services, IConfiguration config)
        {
            services.AddSettings(config);
            services.AddRepos(config);
            services.AddServices(config);
            return services;
        }
    }
}
