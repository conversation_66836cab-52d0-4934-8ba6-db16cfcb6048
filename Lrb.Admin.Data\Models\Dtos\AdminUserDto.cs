﻿using Lrb.Admin.Data.Models.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class AdminUserDto
    {
        public Guid Id { get; set; }
        public string UserName { get; set; }
        public string PhoneNumber { get; set; }
        public Role Role { get; set; }
        public byte[] PasswordHash 
        { 
            get; set; 
        }
        public byte[] PasswordSalt { get; set; }
        public bool IsDeleted { get; set; }
    }
}
