﻿@using Faso.Blazor.SpinKit
@using Lrb.Admin.Data.Services;
@inject SpinnerService SpinnerService

<style>
    .spinner-container {
        display: flex;
        flex-direction: column;
        position: fixed;
        z-index: 999;
        top: 50%;
        left: 50%;
    }
</style>
@if (IsVisible)
{
    
    <div class="spinner-container">
        <SpinKitThreeBounce />
    </div>
}
@code
{
    protected bool IsVisible { get; set; }
    protected override void OnInitialized()
    {
        SpinnerService.OnShow += ShowSpinner;
        SpinnerService.OnHide += HideSpinner;
    }

    public void ShowSpinner()
    {
        IsVisible = true;
        StateHasChanged();
    }

    public void HideSpinner()
    {
        IsVisible = false;
        StateHasChanged();
    }
}