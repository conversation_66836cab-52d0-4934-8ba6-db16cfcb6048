﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Pages.Tenant;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.Subscription
{
    public partial class UpdatePayment
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Inject]
        public ISubscritionRepository SubscritionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        public string? Token { get; set; }
        [Parameter]
        public bool isAddon { get; set; }
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public Guid? SubscriptionId { get; set; }
        [Parameter]
        public LRBPayments PaymentModel { get; set; }
        //public LRBPayments payments { get; set; } = new();
        public double netinput { get=>PaymentModel.NetAmount; set=>PaymentModel.NetAmount=value; }
        public double gstinput { get=>PaymentModel.GSTAmount; set => PaymentModel.GSTAmount=value; }
        public double paidinput { get => netinput + gstinput; set => PaymentModel.TotalAmount = value; }
        public double pendinginput { get => PaymentModel.PendingAmount; set => PaymentModel.PendingAmount = value; }
        public async Task UpdateSubscription()
        {
            if (PaymentModel.NextDueDate == default)
            {
                PaymentModel.NextDueDate = DateTime.Now.AddMonths(1);
            }
            PaymentModel.NetAmount = netinput;
            PaymentModel.GSTAmount = gstinput;
            PaymentModel.TotalAmount = paidinput;
            PaymentModel.PendingAmount = pendinginput;
            PaymentModel.PaymentMode = PaymentModel.Mode.ToString();
            
            var added = await PaymentService.UpdatePaymentAsync(PaymentModel);
            if (added > 0)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"Payment of {TenantId} Successfully Updated");
                Token = await LocalStorage.GetItemAsync<string>("token");
            }
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            AdminUserActivity userActivity = new()
            {
                AdminUserId = userActivityHistory.AdminUserId,
                TenantId = TenantId,
                ActivityTime = DateTime.UtcNow,
                Activity = ActivityConst.UpdatePayment + " " + PaymentModel?.TenantId,
                Token = Token
            };
            await ActivityService.UserActivityAsync(userActivity);
            await jSRuntime.InvokeVoidAsync("location.reload");
        }
        public async Task Reset()
        {
            var isConfirmed = await jSRuntime.InvokeAsync<bool>("Confirm", $"Are you sure?");
            if (isConfirmed)
            {
                PaymentModel = new();
            }
        }
    }
}
