﻿@page "/analytics"

<link href="./css/analytics.css" rel="stylesheet"/>

<div class="analytics">
    <div class="analytics-filter">
        <div class="analytics-filter-left">
            <EditForm class="form-floating" Model="@Filter" OnInvalidSubmit="GetAnalyticsRecord">
                <div class="form-group">
                    <label for="tenantId" style="font-weight:bold">TenantId</label>
                    @*<InputText id="tenantId" @bind-Value="Filter.TenantId"></InputText>*@
                    <InputSelect id="tenantId" @bind-Value="Filter.TenantId" @onclick="GetTenantIdAsync">
                        <option value="Select">Select</option>
                        @foreach(var items in Records)
                        {
                            <option value="@items">@items</option>
                        }
                    </InputSelect>

                </div>
                <div class="form-group" style="margin-left:2px">
                    <label for="from-date" style="font-weight:bold">FromDate</label>
                    <InputDate id="from-date" style="margin-left:1px" @bind-Value="Filter.FromDate"></InputDate>
                </div>
                <div class="form-group" style="margin-left:2px">
                    <label for="To-date" style="font-weight:bold">ToDate</label>
                    <InputDate id="To-date" style="margin-left:1px" @bind-Value="Filter.ToDate"></InputDate>
                </div>
            </EditForm>
        </div>
        <div class="analytics-count">
            <span style="font-weight:bold">Record Count :</span>
            <span><u>@Collection.Count</u></span>
        </div>
        <div class="analytics-filters-right" style="float:right;">
            <input type="button" style="margin-left:3px" value="Filter" @onclick="@(async ()=> {PageNumber=1;PageSize=10;await GetAnalyticsRecord();})" /><br />
            <input type="button" style="margin-left:3px" value="Reset" @onclick="@(async ()=> {Filter = new(); await GetAnalyticsRecord();})" />
        </div>
    </div>
    <div class="analytics-button">
        <button class="btn btn-primary btn-sm" @onclick="@(async (e) => {await GetTodayRecord();})">Today</button>
        <button class="btn btn-primary btn-sm" @onclick="@(async (e) => {await GetYesterdayRecord();})">Yesterday</button>
        <button class="btn btn-primary btn-sm" @onclick="@(async (e) => {await GetLastWeekRecord();})">LastWeek</button>
        <button class="btn btn-primary btn-sm" @onclick="@(async (e) => {await GetLastMonthRecord();})">LastMonth</button>
    </div>
</div>

<div class="page-filter">
    <div class="page-size" style="float:right">
        <select @oninput="@(async (e) => { PageSize = int.Parse(e.Value.ToString());await GetAnalyticsRecord();})">
            @foreach (var item in new List<int>() { 10, 25, 50, 100 })
            {
                <option value="@item">@item</option>
            }
        </select>
    </div>
</div>

<h3>Analytics Table</h3>

<div class="analytics-properties">

    <div class="analytics-table">
        @if (Collection == null)
        {
            <div class="spinner-border"></div>
        }
        else
        {
            if (Collection.Any(i => i != null))
            {
                <table class="table table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>Id</th>
                            <th>UserId</th>
                            <th>Username</th>
                            <th>TenantId</th>
                            <th>CreatedOn</th>
                            <th>IsWebApp</th>
                            <th>IsMObileApp</th>
                            <th>Browser</th>
                            <th>Browser Version</th>
                            <th>Device</th>
                            <th>Device Type</th>
                            <th>Orientation</th>
                            <th>Os</th>
                            <th>Os Version</th>
                            <th>PlatformId</th>
                            <th>User Agent</th>
                            <th>Latitude</th>
                            <th>Longitude</th>
                            <th>Platform</th>
                            <th>IpAddress</th>
                        </tr>
                    </thead>
                    @foreach (var items in Collection.Select((value, i) => new { value, i }).Skip(PageNumber - 1).Take(PageSize))
                    {
                        <tr>
                            <td>@items.value?.Id</td>
                            <td>@items.value?.UserId</td>
                            <td>@items.value?.Username</td>
                            <td>@items.value?.TenantId</td>
                            <td>@items.value?.CreatedOn</td>
                            <td>@items.value?.IsWebApp</td>
                            <td>@items.value?.IsMobileApp</td>
                            <td>@items.value?.Browser</td>
                            <td>@items.value?.BrowserVersion</td>
                            <td>@items.value?.Device</td>
                            <td>@items.value?.DeviceType</td>
                            <td>@items.value?.Orientation</td>
                            <td>@items.value?.Os</td>
                            <td>@items.value?.OsVersion</td>
                            <td>@items.value?.PlatformId</td>
                            <td>@items.value?.UserAgent</td>
                            <td>@items.value?.Latitude</td>
                            <td>@items.value?.Longitude</td>
                            <td>@items.value?.Platform</td>
                            <td>@items.value?.IpAddress</td>
                        </tr>
                    }
                </table>
            }
            else
            {
                <div style="position:absolute;top:60%;left:50%;font-weight:bold">No Data Found</div>
            }
        }
    </div>
</div>
<div class="table-paging">

    @if(Collection.Count < 10)
    {
        <div style="display:none;border:none;"></div>
    }
    else
    {
        @for (int i = PageNumber - 2; i <= PageNumber + 2; i++)
        {
            int j = i;
            if (j > 0)
            {
                if (i == PageNumber)
                {
                    <span class="px-2" style="font-weight:bold;cursor:pointer" @onclick="() => Paginate(j)">@i</span>
                }
                else
                {
                    <span class="px-2" style="cursor:pointer" @onclick="()=> Paginate(j)">@i</span>
                }
            }
        }
    }
</div>

