﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.ActivityPage
{
    [Authorize(Roles = "Admin")]
    public partial class Activity
    {
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorageService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public SpinnerService SpinnerService { get; set; }
        [Inject]
        public IAuthService AuthService { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public double MaxPageNumber { get; set; }
        public int TotalCount { get; set; }
        public int ShowingCount { get; set; }
        private bool CustomFilter { get; set; } = false;

        public List<int> Count = new() { 10, 25, 50, 100 };
        public AdminUserActivity Model { get; set; } = new();

        public GetActivityDto ActivityModel { get; set; } = new();
        public ActivityFilter Filter { get; set; } = new();
        public ObservableCollection<GetActivityDto> Collection { get; set; } = new();
        public UserModelDto UserModel { get; set; } = new();
        public string? Token { get; set; }
        public List<int> PagesizeList { get; set; } = new List<int> { 10, 25, 50, 100 };
        protected override async Task OnInitializedAsync()
        {
            Token = await LocalStorageService.GetItemAsync<string>("token");
            Model = await ActivityService.GetUserByTokenAsync(Token);
            UserModel = await AuthService.GetUser(Model.AdminUserId);
            await GetUserActivity();
        }

        public async Task GetUserActivity()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetActivityDto, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await ActivityService.GetAdminUserActivityAsync(Filter);
                });
                if (!fetchData.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchData;
                    fetchData.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetActivityDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                }
                else
                {
                    await jSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }

        }

        protected void GetTrueFilter()
        {
            var activity = Filter.GetType().GetProperties();
            foreach (var property in activity)
            {
                if (property.GetValue(Filter) != null && property.GetValue(Filter).ToString() == "Select")
                {
                    property.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }

        private async Task GetTodayActivity()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetActivityDto, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await ActivityService.GetTodayActivityAsync(Filter);
                });
                if (!fetchData.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchData;
                    fetchData.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetActivityDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                }
                else
                {
                    await jSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        private async Task GetYesterdayActivity()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetActivityDto, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await ActivityService.GetYesterdayActivityAsync(Filter);
                });
                if (!fetchData.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchData;
                    fetchData.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetActivityDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                }
                else
                {
                    await jSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        private async Task GetLastWeekActivity()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetActivityDto, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await ActivityService.GetLastWeekActivityAsync(Filter);
                });
                if (!fetchData.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchData;
                    fetchData.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetActivityDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                }
                else
                {
                    await jSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        private async Task GetLastMonthActivity()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetActivityDto, string> response = null;
                var fetchData = InvokeAsync(async () =>
                {
                    response = await ActivityService.GetLastMontActivityAsync(Filter);
                });
                if (!fetchData.IsCompleted)
                {
                    SpinnerService.Show();
                    await fetchData;
                    fetchData.Dispose();
                    SpinnerService.Hide();
                }
                if (response != null && response.Items != null)
                {
                    Collection = new ObservableCollection<GetActivityDto>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = PageNumber * PageSize - (PageSize - Collection.Count);
                }
                else
                {
                    await jSRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        private async Task CustomFilterAsync()
        {
            CustomFilter = true;
        }

        public void Paginate(int page)
        {
            PageNumber = page;
        }
        private async Task IncreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber >= Math.Ceiling(MaxPageNumber))
            {
                PageNumber++;
            }
            await GetUserActivity();
        }

        private async Task DecreasePageNumber()
        {
            Collection.Clear();
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            await GetUserActivity();
        }
    }
}
