﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Invoice;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IPaymentService
    {
        Task<Response<bool>> CreatePaymentAsync(LRBPayments model);
        Task<LRBPayments> GetPaymentDetailsByIdAsync(Guid? paymentId);
        Task<LRBPayments> GetPaymentDetailsBySubscriptionIdAsync(Guid? SubscriptionId);
        Task<int> UpdatePaymentAsync(LRBPayments dto);
        Task<Response<bool>> CreateInvoiceAsync(CreateInvoice model);
        Task UpdateIsDormentedAsync();
        Task<PagedResponse<GetPendingPaymentModel, string>?> GetAllPendingPaymentAsync(PendingPaymentFilterParameter? filter);
        Task<List<PendingPaymentViewModelExport>> GetExportAllPendingPaymentAsync(PendingPaymentFilterParameter filter);
        //This method is being used to call an external API.
        Task<Response<IEnumerable<PaymentDetailsDto>>> GetPaymenDeatilstAsync(string baseUrl, string resource);
        Task <EmailEventDto> SendEmailViaUtility();
        Task<bool> SendPendingPaymentEmailAsync(GetPendingPaymentModel tenant);
        Task<bool> DeletePaymentAsync(Guid PaymentId);
        Task<IEnumerable<string>> GetAdminEmailAsync(string tenantId);
    }
}
