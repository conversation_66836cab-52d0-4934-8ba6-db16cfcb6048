﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>a1a4cd7b-7b3c-43b3-b3dd-5b9335c9e2d3</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <PropertyGroup>
	  <AssemblyVersion>1.0.0.0</AssemblyVersion>
	  <FileVersion>1.0.0.0</FileVersion>
	  <InformationalVersion>1.0.0</InformationalVersion>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\Lrb.Admin.Data\Lrb.Admin.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BlazorDateRangePicker" Version="5.2.0" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.3.0" />
    <PackageReference Include="Blazored.Modal" Version="7.1.0" />
    <PackageReference Include="EPPlus" Version="6.2.7" />
    <PackageReference Include="Faso.Blazor.SpinKit" Version="1.0.1" />
    <PackageReference Include="itext" Version="8.0.4" />
    <PackageReference Include="itext.bouncy-castle-adapter" Version="8.0.4" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="libphonenumbers-dotnet" Version="1.1.0" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.Core" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="7.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="7.0.9" />
    <PackageReference Include="NETCore.MailKit" Version="2.1.0" />
    <PackageReference Include="Radzen.Blazor" Version="4.21.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.32.1" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.prd.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.qa.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
