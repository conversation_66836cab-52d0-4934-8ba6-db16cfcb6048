﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Pages.Login
{
    [Authorize(Roles = "Admin")]
    public partial class RegisterUser
    {
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorageService { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        public UserModelDto UserModel { get; set; } = new();
        public string? Token { get; set; }

        public async Task RegisterUserAsync()
        {
            var isCreated = await AuthService.RegisterUser(UserModel);
            if (isCreated != null)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Successfully Registered");
                Token = await LocalStorageService.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity adminUser = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = userActivityHistory.TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.RegisterUser + " " + UserModel.FirstName + " " + UserModel.LastName,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(adminUser);
                Navigation.NavigateTo("/");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Something went wrong Try Again");
            }

        }
    }
}
