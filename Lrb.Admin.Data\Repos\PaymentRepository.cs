﻿using Dapper;
using Lrb.Admin.Data.Models.Invoice;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using Amazon.DynamoDBv2;
using Microsoft.EntityFrameworkCore;
using Lrb.Admin.Data.Models.Notification;

namespace Lrb.Admin.Data.Repos
{
    public class PaymentRepository : IPaymentRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string Schema = "tenantmaster";
        protected readonly string Schema1 = "LeadratBlack";
        protected readonly string Schema2 = "Identity";
        protected int TotalCount;
        protected Query Query;
        public PaymentRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }
        public async Task<Response<bool>> CreatePaymentAsync(LRBPayments model)
        {
            var connection = await _dbFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            model.Id = Guid.NewGuid();
            model.IsDeleted = false;
            var query = await db.Query($"LeadratBlack.Payments").InsertAsync(model);
            connection.Close();
            return new Response<bool>(query > 0);
        }

        public async Task<Response<bool>> CreateInvoiceAsync(CreateInvoice model)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                model.Id = Guid.NewGuid();
                model.IsDeleted = false;
                var query = await db.Query($"tenantmaster.Invoice").InsertAsync(model);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<LRBPayments> GetPaymentDetailsById(Guid? paymentId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();

                var innerquery = $"select * from \"LeadratBlack\".\"Payments\" where \"Id\"='{paymentId}'";
                var result = await connection.QueryAsync<LRBPayments>(innerquery);
                connection.Close();
                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<LRBPayments> GetPaymentDetailsBySubscriptionId(Guid? subscriptionId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();

                var innerquery = $"select sum(\"NetAmount\") as \"NetAmount\",sum(\"GSTAmount\") as \"GSTAmount\",sum(\"TotalAmount\") as \"TotalAmount\" " +
                    $"from \"LeadratBlack\".\"Payments\" where \"SubscriptionId\" = '{subscriptionId}' \r\nor \"SubscriptionAddOnId\" = '{subscriptionId}'";
                var result = await connection.QueryAsync<LRBPayments>(innerquery);
                connection.Close();
                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<int> UpdatePayment(LRBPayments dto)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    //if(dto.PartNumber == dto.PartPaid)
                    //{
                    //    dto.NextDueDate = DateTime.MinValue;
                    //    dto.NextDueAmount = 0;
                    //}
                    dto.IsDeleted = false;
                    var result = await db.Query("LeadratBlack.Payments")
                        .Where("Id", "=", dto.Id)
                        .UpdateAsync(dto);

                    return result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating payment: {ex.Message}");
            }
        }
        public async Task<IEnumerable<PendingPaymentDetailDto>> GetPendingPayments()
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            connection.Open();

            Query = db.Query().FromRaw($"\"tenantmaster\".getpendingpayments()").Select("TenantId",
                "TotalAmount", "NextDueDate", "PaidAmount", "PendingAmount",
                "LicenseValidity", "GSTNumber", "TenantName", "BillingType", "TDS", "LastInoviceNumber", "VendorNames", "OnBoardDate", "NoOfLicenses", "Address", "PaymentMode", "GSTAmount", "TransactionDate");

            IEnumerable<PendingPaymentDetailDto> PendingPaymentData = await Query.GetAsync<PendingPaymentDetailDto>();
            connection.Close();
            return PendingPaymentData;
        }

        public async Task<PagedResponse<GetPendingPaymentModel, string>?> GetAllAsync(int pageNumber, int pageSize, PendingPaymentFilterParameter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await TotalPendingPaymentCount(filter);
                //filter.Sort.Count;
                if (filter != default)
                {
                    Query = db.Query().FromRaw($"\"tenantmaster\".getpendingpayments()").Select("Id","TenantId",
                "TotalAmount", "NextDueDate", "PaidAmount", "PendingAmount","NoOfLicenses", "Address", "PaymentMode",
                "LicenseValidity", "GSTNumber", "TenantName", "BillingType", "TDS", "LastInvoiceNumber", "GSTAmount",
                "VendorNames", "OnBoardDate", "NetAmount", "Source", "VendorContactNumbers", "TransactionDate",
                "VendorEmail", "TenantInfoId", "MerchantEmail", "IsTestingAccount");
                }
                Query = Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                Query = await BuildQueryAsync(filter, Query);
                IEnumerable<GetPendingPaymentModel> paymentInfo = await Query.GetAsync<GetPendingPaymentModel>();
                connection.Close();
                return new PagedResponse<GetPendingPaymentModel, string>(paymentInfo, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> TotalPendingPaymentCount(PendingPaymentFilterParameter filter)
        {
            using var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query().FromRaw($"\"tenantmaster\".getpendingpayments()").Select("count(\"TenantId\")");
            query = await BuildQueryAsync(filter, query);
            connection.Close();
            return await query.CountAsync<int>();
        }

        public async Task<Query> BuildQueryAsync(PendingPaymentFilterParameter filter, Query query)
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());

            if (!string.IsNullOrEmpty(filter?.SearchTerm))
            {
                string searchTerm = filter.SearchTerm.ToLower().Replace(" ", "");

                string innerquery =
                    $" \"TenantId\" IN ( " +
                    $" SELECT \"TenantId\" FROM \"tenantmaster\".getpendingpayments() " +
                    $" WHERE LOWER(\"TenantId\") LIKE '%{searchTerm}%' " +
                    $" OR LOWER(\"TenantName\") LIKE '%{searchTerm}%')";

                query = query.WhereRaw(innerquery);
            }

            if ((filter.PendingAmountFrom != null && filter.PendingAmountFrom != default) || (filter.PendingAmountTo != null && filter.PendingAmountTo != default))
            {
                var subQuery = new Query()
                 .FromRaw("\"tenantmaster\".getpendingpayments()")
                 .Select("Id")
                 .Where(q =>
                     q.Where("PendingAmount", ">=", filter?.PendingAmountFrom)
                      .Where("PendingAmount", "<=", filter?.PendingAmountTo)
                 );
                query = query.WhereIn("Id", subQuery);
            }

            if (!string.IsNullOrEmpty(filter.VendorNames))
            {
                string innerQuery = $"\"TenantId\" IN (SELECT \"TenantId\" FROM \"tenantmaster\".getpendingpayments() WHERE \"VendorNames\" ILIKE '%{filter.VendorNames}%')";
                query = query.WhereRaw(innerQuery);
            }

            if (!string.IsNullOrEmpty(filter.LastInvoiceNumber))
            {
                string innerQuery = $"\"TenantId\" IN (SELECT \"TenantId\" FROM \"tenantmaster\".getpendingpayments() WHERE \"LastInvoiceNumber\" ILIKE '%{filter.LastInvoiceNumber}%')";
                query = query.WhereRaw(innerQuery);
            }

            if (!string.IsNullOrEmpty(filter.GSTNumber))
            {
                string innerQuery = $"\"TenantId\" IN (SELECT \"TenantId\" FROM \"tenantmaster\".getpendingpayments() WHERE \"GSTNumber\" ILIKE '%{filter.GSTNumber}%')";
                query = query.WhereRaw(innerQuery);
            }
            if(filter.IsTestingAccount != null)
            {
                string innerQuery = $"\"TenantId\" IN (SELECT \"TenantId\" FROM \"tenantmaster\".getpendingpayments() WHERE \"IsTestingAccount\" = '{filter.IsTestingAccount}')";
                query = query.WhereRaw(innerQuery);
            }

            if (filter.BillingType != default)
            {
                var subQuery = new Query().FromRaw("\"tenantmaster\".getpendingpayments()").Select("Id");    //TenantId

                switch (filter.BillingType)
                {
                    case BillingType.None:
                        subQuery = subQuery.Where("BillingType", 0);
                        break;
                    case BillingType.Quarterly:
                        subQuery = subQuery.Where("BillingType", 1);
                        break;
                    case BillingType.Halfyearly:
                        subQuery = subQuery.Where("BillingType", 2);
                        break;
                    case BillingType.Yearly:
                        subQuery = subQuery.Where("BillingType", 3);
                        break;
                }

                query = query.WhereIn("Id", subQuery);
            }

            if ((int)filter?.DateType == 1 && ((filter.FromDate != null && filter.FromDate != default) || (filter.ToDate != null && filter.ToDate != default)))
            {
                var subQuery = new Query()
                    .FromRaw("\"tenantmaster\".getpendingpayments()")
                    .Select("TenantId")
                    .Where(q =>
                        q.Where("OnBoardDate", ">=", filter?.FromDate)
                         .Where("OnBoardDate", "<=", filter?.ToDate)
                    );

                query = query.WhereIn("TenantId", subQuery);
            }

            if ((int)filter?.DateType == 2 && ((filter.FromDate != null && filter.FromDate != default) || (filter.ToDate != null && filter.ToDate != default)))
            {
                     var subQuery = new Query()
                    .FromRaw("\"tenantmaster\".getpendingpayments()")
                    .Select("TenantId")
                    .Where(q =>
                        q.Where("LicenseValidity", ">=", filter?.FromDate)
                         .Where("LicenseValidity", "<=", filter?.ToDate)
                    );

                query = query.WhereIn("TenantId", subQuery);
            }

            if ((int)filter?.DateType == 3 && ((filter.FromDate != null && filter.FromDate != default) || (filter.ToDate != null && filter.ToDate != default)))
            {
                var subQuery = new Query()
                    .FromRaw("\"tenantmaster\".getpendingpayments()")
                    .Select("TenantId")
                    .Where(q =>
                        q.Where("NextDueDate", ">=", filter?.FromDate)
                         .Where("NextDueDate", "<=", filter?.ToDate)
                    );

                query = query.WhereIn("TenantId", subQuery);
            }

            return query;
        }

        public async Task<List<PendingPaymentViewModelExport>> GetPendingPaymentList(PendingPaymentFilterParameter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());

                Query = db.Query().FromRaw($"\"tenantmaster\".getpendingpayments()").Select("TenantId",
                "TotalAmount", "NextDueDate", "PaidAmount", "PendingAmount", "NoOfLicenses", "Address", "PaymentMode",
                "LicenseValidity", "GSTNumber", "TenantName", "BillingType", "TDS", "LastInvoiceNumber", "GSTAmount",
                "VendorNames", "OnBoardDate", "NetAmount", "Source", "VendorContactNumbers", "TransactionDate",
                "VendorEmail", "TenantInfoId", "MerchantEmail", "UserName", "UserEmail");
                Query = await BuildQueryAsync(filter, Query);
                IEnumerable<PendingPaymentViewModelExport> tenantInfo = await Query.GetAsync<PendingPaymentViewModelExport>();
                List<PendingPaymentViewModelExport> tenantInfo1 = tenantInfo.ToList();
                connection.Close();
                return tenantInfo1;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<EmailEventDto> SendPaymentDueMails()
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            string query = "SELECT * FROM \"LeadratBlack\".\"MasterEmailTemplates\" where \"Event\" = '57'";
            connection.Open();
            var EmailBodyData = await connection.QueryAsync<EmailEventDto>(query);
            connection.Close();
            return EmailBodyData.FirstOrDefault();
        }

        public async Task<IEnumerable<string>> SendAdminMails(string tenantId) 
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            string query = $"SELECT u.\"Email\"\r\nFROM \"Identity\".\"Users\" u\r\nLEFT JOIN \"Identity\".\"UserRoles\" ur ON u.\"Id\" = ur.\"UserId\"\r\n" +
                $"LEFT JOIN \"Identity\".\"Roles\" r ON ur.\"RoleId\" = r.\"Id\"\r\nWHERE u.\"TenantId\" = '{tenantId}'\r\n  " +
                $"AND u.\"IsDeleted\" = FALSE\r\n  AND r.\"Name\" = 'Admin'\r\n  AND u.\"UserName\" NOT LIKE '%.admin';";
            connection.Open();
            var EmailBodyData = await connection.QueryAsync<string>(query);
            connection.Close();
            return EmailBodyData;
        }
        public async Task<bool> DeletePayment(Guid PaymentId)
        {
            try
            {
                using (var connection = await _dbFactory.CreateConnectionAsync())
                {
                    var db = new QueryFactory(connection, new PostgresCompiler());
                    var result = await db.Query($"LeadratBlack.Payments")
                        .Where("Id", "=", PaymentId)
                        .UpdateAsync(new
                        {
                            IsDeleted = true
                        });
                    return (result > 0);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error Deleting payment: {ex.Message}");
            }
        }
    }
}