﻿@* <h3>PendingPaymentFilter</h3> *@

<div class="form-container">
        <div class="form-group">
            <label for="DateType">Date Type</label>
            <select id="DateType" @bind="@Filter.DateType">
                @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Filters.PendingPaymentDateType)))
                {
                    <option value="@item">@item</option>
                }
            </select>
        </div>
        <div class="form-group">
            <label for="FromDate">From Date</label>
            <input type="date" @bind-value="@Filter.FromDate" Disabled="@IsDateDisabled()" />
        </div>
        <div class="form-group">
            <label for="ToDate">To Date</label>
            <input type="date" @bind-value="@Filter.ToDate" Disabled="@IsDateDisabled()" />
        </div>

        <div class="form-group">
            <label for="from_date">Pending Amount From</label>
            <input type="number" @bind-value="@Filter.PendingAmountFrom" Name="DatePickerDateOnlyType" />
        </div>
        <div class="form-group">
            <label for="to_date">Pending Amount To</label>
            <input type="number" @bind-value="@Filter.PendingAmountTo" Name="DatePickerDateOnlyType" />
        </div>

        <div class="form-group">
            <label for="VendorNames">Vendor Names</label>
            <input type="text" @bind-value="Filter.VendorNames"/>
        </div>
        <div class="form-group">
            <label for="LastInoviceNumber">Last Inovice Number</label>
            <input type="text" @bind-value="Filter.LastInvoiceNumber" />
        </div>
        <div class="form-group">
            <label for="GSTNumber">GST Number</label>
            <input type="text" @bind-value="Filter.GSTNumber"/>
        </div>

        <div class="form-group">
            <label for="billing_type">Billing Type</label>
            <select id="billing_type" @bind="Filter.BillingType">
                @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.BillingType)))
                {
                    <option value="@item">@item</option>
                }
            </select>
        </div>
    <div class="form-group">
        <label>IsTestingAccount</label>
        <select @bind="IsTestingAccount">
            <option value="">Select</option>
            <option value="True">True</option>
            <option value="False">False</option>
        </select>
    </div>
</div>
<div class="footer">
    <span onclick= "@CallGetPendingPaymentsAsync">
        <svg width="87" height="34" viewBox="0 0 87 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="87" height="34" rx="4" fill="#121212" />
            <path d="M25.792 22.12C25.304 22.12 24.856 22.06 24.448 21.94C24.04 21.812 23.672 21.624 23.344 21.376C23.016 21.128 22.724 20.828 22.468 20.476L23.488 19.324C23.88 19.868 24.264 20.244 24.64 20.452C25.016 20.66 25.44 20.764 25.912 20.764C26.184 20.764 26.432 20.724 26.656 20.644C26.88 20.556 27.056 20.44 27.184 20.296C27.312 20.144 27.376 19.972 27.376 19.78C27.376 19.644 27.348 19.52 27.292 19.408C27.244 19.288 27.168 19.184 27.064 19.096C26.96 19 26.832 18.912 26.68 18.832C26.528 18.752 26.356 18.684 26.164 18.628C25.972 18.572 25.76 18.52 25.528 18.472C25.088 18.384 24.704 18.268 24.376 18.124C24.048 17.972 23.772 17.788 23.548 17.572C23.324 17.348 23.16 17.1 23.056 16.828C22.952 16.548 22.9 16.232 22.9 15.88C22.9 15.528 22.976 15.204 23.128 14.908C23.288 14.612 23.504 14.356 23.776 14.14C24.048 13.924 24.364 13.756 24.724 13.636C25.084 13.516 25.472 13.456 25.888 13.456C26.36 13.456 26.78 13.512 27.148 13.624C27.524 13.736 27.852 13.904 28.132 14.128C28.42 14.344 28.656 14.608 28.84 14.92L27.808 15.94C27.648 15.692 27.468 15.488 27.268 15.328C27.068 15.16 26.852 15.036 26.62 14.956C26.388 14.868 26.144 14.824 25.888 14.824C25.6 14.824 25.348 14.864 25.132 14.944C24.924 15.024 24.76 15.14 24.64 15.292C24.52 15.436 24.46 15.612 24.46 15.82C24.46 15.98 24.496 16.124 24.568 16.252C24.64 16.372 24.74 16.48 24.868 16.576C25.004 16.672 25.176 16.756 25.384 16.828C25.592 16.9 25.828 16.964 26.092 17.02C26.532 17.108 26.928 17.228 27.28 17.38C27.632 17.524 27.932 17.7 28.18 17.908C28.428 18.108 28.616 18.34 28.744 18.604C28.872 18.86 28.936 19.148 28.936 19.468C28.936 20.02 28.804 20.496 28.54 20.896C28.284 21.288 27.92 21.592 27.448 21.808C26.976 22.016 26.424 22.12 25.792 22.12ZM32.5834 22.12C32.1274 22.12 31.7314 22.02 31.3954 21.82C31.0594 21.612 30.8034 21.32 30.6274 20.944C30.4514 20.56 30.3634 20.104 30.3634 19.576V15.676H31.8394V19.264C31.8394 19.6 31.8914 19.888 31.9954 20.128C32.0994 20.36 32.2514 20.54 32.4514 20.668C32.6514 20.796 32.8914 20.86 33.1714 20.86C33.3794 20.86 33.5674 20.828 33.7354 20.764C33.9114 20.692 34.0634 20.596 34.1914 20.476C34.3194 20.348 34.4194 20.2 34.4914 20.032C34.5634 19.856 34.5994 19.668 34.5994 19.468V15.676H36.0754V22H34.6354L34.6114 20.68L34.8754 20.536C34.7794 20.84 34.6154 21.112 34.3834 21.352C34.1594 21.584 33.8914 21.772 33.5794 21.916C33.2674 22.052 32.9354 22.12 32.5834 22.12ZM41.2974 22.12C41.0174 22.12 40.7414 22.08 40.4694 22C40.2054 21.912 39.9654 21.796 39.7494 21.652C39.5334 21.508 39.3534 21.348 39.2094 21.172C39.0654 20.988 38.9694 20.804 38.9214 20.62L39.2694 20.464L39.2334 21.976H37.8054V13.12H39.2814V17.14L39.0174 17.02C39.0574 16.828 39.1454 16.648 39.2814 16.48C39.4254 16.304 39.6014 16.148 39.8094 16.012C40.0174 15.868 40.2454 15.756 40.4934 15.676C40.7414 15.588 40.9934 15.544 41.2494 15.544C41.8174 15.544 42.3174 15.684 42.7494 15.964C43.1894 16.244 43.5334 16.632 43.7814 17.128C44.0374 17.624 44.1654 18.188 44.1654 18.82C44.1654 19.46 44.0414 20.028 43.7934 20.524C43.5454 21.02 43.2014 21.412 42.7614 21.7C42.3294 21.98 41.8414 22.12 41.2974 22.12ZM40.9854 20.824C41.3214 20.824 41.6214 20.74 41.8854 20.572C42.1494 20.396 42.3574 20.16 42.5094 19.864C42.6614 19.56 42.7374 19.212 42.7374 18.82C42.7374 18.436 42.6614 18.096 42.5094 17.8C42.3654 17.504 42.1614 17.272 41.8974 17.104C41.6334 16.936 41.3294 16.852 40.9854 16.852C40.6414 16.852 40.3374 16.936 40.0734 17.104C39.8094 17.272 39.6014 17.504 39.4494 17.8C39.2974 18.096 39.2214 18.436 39.2214 18.82C39.2214 19.212 39.2974 19.56 39.4494 19.864C39.6014 20.16 39.8094 20.396 40.0734 20.572C40.3374 20.74 40.6414 20.824 40.9854 20.824ZM45.4572 22V15.676H46.9092L46.9332 16.948L46.6932 17.044C46.7652 16.836 46.8732 16.644 47.0172 16.468C47.1612 16.284 47.3332 16.128 47.5332 16C47.7332 15.864 47.9452 15.76 48.1692 15.688C48.3932 15.608 48.6212 15.568 48.8532 15.568C49.1972 15.568 49.5012 15.624 49.7652 15.736C50.0372 15.84 50.2612 16.008 50.4372 16.24C50.6212 16.472 50.7572 16.768 50.8452 17.128L50.6172 17.08L50.7132 16.888C50.8012 16.696 50.9252 16.524 51.0852 16.372C51.2452 16.212 51.4252 16.072 51.6252 15.952C51.8252 15.824 52.0332 15.728 52.2492 15.664C52.4732 15.6 52.6932 15.568 52.9092 15.568C53.3892 15.568 53.7892 15.664 54.1092 15.856C54.4292 16.048 54.6692 16.34 54.8292 16.732C54.9892 17.124 55.0692 17.608 55.0692 18.184V22H53.5812V18.28C53.5812 17.96 53.5372 17.696 53.4492 17.488C53.3692 17.28 53.2412 17.128 53.0652 17.032C52.8972 16.928 52.6852 16.876 52.4292 16.876C52.2292 16.876 52.0372 16.912 51.8532 16.984C51.6772 17.048 51.5252 17.144 51.3972 17.272C51.2692 17.392 51.1692 17.532 51.0972 17.692C51.0252 17.852 50.9892 18.028 50.9892 18.22V22H49.5012V18.268C49.5012 17.964 49.4572 17.712 49.3692 17.512C49.2812 17.304 49.1532 17.148 48.9852 17.044C48.8172 16.932 48.6132 16.876 48.3732 16.876C48.1732 16.876 47.9852 16.912 47.8092 16.984C47.6332 17.048 47.4812 17.14 47.3532 17.26C47.2252 17.38 47.1252 17.52 47.0532 17.68C46.9812 17.84 46.9452 18.016 46.9452 18.208V22H45.4572ZM56.8758 22V15.676H58.3518V22H56.8758ZM57.5958 14.332C57.2998 14.332 57.0678 14.26 56.8998 14.116C56.7398 13.964 56.6598 13.752 56.6598 13.48C56.6598 13.224 56.7438 13.016 56.9118 12.856C57.0798 12.696 57.3078 12.616 57.5958 12.616C57.8998 12.616 58.1318 12.692 58.2918 12.844C58.4598 12.988 58.5438 13.2 58.5438 13.48C58.5438 13.728 58.4598 13.932 58.2918 14.092C58.1238 14.252 57.8918 14.332 57.5958 14.332ZM60.9074 22V14.068H62.3834V22H60.9074ZM59.6594 17.044V15.676H63.7634V17.044H59.6594Z" fill="white" />
        </svg>
    </span>
    <svg width="2" height="16" viewBox="0 0 2 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1V15" stroke="#DDDDDD" stroke-linecap="round" />
    </svg>
    <span onclick="@ResetFilter">
        <svg width="45" height="34" viewBox="0 0 45 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.128 22V13.6H10.776C11.28 13.6 11.74 13.72 12.156 13.96C12.572 14.192 12.9 14.512 13.14 14.92C13.388 15.32 13.512 15.772 13.512 16.276C13.512 16.756 13.388 17.2 13.14 17.608C12.9 18.008 12.572 18.328 12.156 18.568C11.748 18.8 11.288 18.916 10.776 18.916H8.652V22H7.128ZM12 22L9.864 18.208L11.472 17.908L13.848 22.012L12 22ZM8.652 17.56H10.788C11.02 17.56 11.22 17.508 11.388 17.404C11.564 17.292 11.7 17.14 11.796 16.948C11.892 16.756 11.94 16.544 11.94 16.312C11.94 16.048 11.88 15.82 11.76 15.628C11.64 15.436 11.472 15.284 11.256 15.172C11.04 15.06 10.792 15.004 10.512 15.004H8.652V17.56ZM17.9608 22.12C17.2968 22.12 16.7088 21.98 16.1968 21.7C15.6928 21.42 15.2968 21.04 15.0088 20.56C14.7288 20.072 14.5888 19.516 14.5888 18.892C14.5888 18.396 14.6688 17.944 14.8288 17.536C14.9888 17.128 15.2088 16.776 15.4888 16.48C15.7768 16.176 16.1168 15.944 16.5088 15.784C16.9088 15.616 17.3448 15.532 17.8168 15.532C18.2328 15.532 18.6208 15.612 18.9808 15.772C19.3408 15.932 19.6528 16.152 19.9168 16.432C20.1808 16.704 20.3808 17.032 20.5168 17.416C20.6608 17.792 20.7288 18.204 20.7208 18.652L20.7088 19.168H15.5848L15.3088 18.16H19.4728L19.2808 18.364V18.1C19.2568 17.852 19.1768 17.636 19.0408 17.452C18.9048 17.26 18.7288 17.112 18.5128 17.008C18.3048 16.896 18.0728 16.84 17.8168 16.84C17.4248 16.84 17.0928 16.916 16.8208 17.068C16.5568 17.22 16.3568 17.44 16.2208 17.728C16.0848 18.008 16.0168 18.36 16.0168 18.784C16.0168 19.192 16.1008 19.548 16.2688 19.852C16.4448 20.156 16.6888 20.392 17.0008 20.56C17.3208 20.72 17.6928 20.8 18.1168 20.8C18.4128 20.8 18.6848 20.752 18.9328 20.656C19.1808 20.56 19.4488 20.388 19.7368 20.14L20.4688 21.16C20.2528 21.36 20.0048 21.532 19.7248 21.676C19.4528 21.812 19.1648 21.92 18.8608 22C18.5568 22.08 18.2568 22.12 17.9608 22.12ZM24.1401 22.12C23.5801 22.12 23.0761 22.028 22.6281 21.844C22.1881 21.652 21.8281 21.38 21.5481 21.028L22.5081 20.2C22.7481 20.472 23.0161 20.668 23.3121 20.788C23.6081 20.908 23.9201 20.968 24.2481 20.968C24.3841 20.968 24.5041 20.952 24.6081 20.92C24.7201 20.888 24.8161 20.84 24.8961 20.776C24.9761 20.712 25.0361 20.64 25.0761 20.56C25.1241 20.472 25.1481 20.376 25.1481 20.272C25.1481 20.08 25.0761 19.928 24.9321 19.816C24.8521 19.76 24.7241 19.7 24.5481 19.636C24.3801 19.572 24.1601 19.508 23.8881 19.444C23.4561 19.332 23.0961 19.204 22.8081 19.06C22.5201 18.908 22.2961 18.74 22.1361 18.556C22.0001 18.404 21.8961 18.232 21.8241 18.04C21.7601 17.848 21.7281 17.64 21.7281 17.416C21.7281 17.136 21.7881 16.884 21.9081 16.66C22.0361 16.428 22.2081 16.228 22.4241 16.06C22.6401 15.892 22.8921 15.764 23.1801 15.676C23.4681 15.588 23.7681 15.544 24.0801 15.544C24.4001 15.544 24.7081 15.584 25.0041 15.664C25.3081 15.744 25.5881 15.86 25.8441 16.012C26.1081 16.156 26.3321 16.332 26.5161 16.54L25.7001 17.44C25.5481 17.296 25.3801 17.168 25.1961 17.056C25.0201 16.944 24.8401 16.856 24.6561 16.792C24.4721 16.72 24.3001 16.684 24.1401 16.684C23.9881 16.684 23.8521 16.7 23.7321 16.732C23.6121 16.756 23.5121 16.796 23.4321 16.852C23.3521 16.908 23.2881 16.98 23.2401 17.068C23.2001 17.148 23.1801 17.244 23.1801 17.356C23.1881 17.452 23.2121 17.544 23.2521 17.632C23.3001 17.712 23.3641 17.78 23.4441 17.836C23.5321 17.892 23.6641 17.956 23.8401 18.028C24.0161 18.1 24.2441 18.168 24.5241 18.232C24.9161 18.336 25.2441 18.452 25.5081 18.58C25.7721 18.708 25.9801 18.856 26.1321 19.024C26.2841 19.168 26.3921 19.336 26.4561 19.528C26.5201 19.72 26.5521 19.932 26.5521 20.164C26.5521 20.54 26.4441 20.876 26.2281 21.172C26.0201 21.468 25.7321 21.7 25.3641 21.868C25.0041 22.036 24.5961 22.12 24.1401 22.12ZM30.8163 22.12C30.1523 22.12 29.5643 21.98 29.0523 21.7C28.5483 21.42 28.1523 21.04 27.8643 20.56C27.5843 20.072 27.4443 19.516 27.4443 18.892C27.4443 18.396 27.5243 17.944 27.6843 17.536C27.8443 17.128 28.0643 16.776 28.3443 16.48C28.6323 16.176 28.9723 15.944 29.3643 15.784C29.7643 15.616 30.2003 15.532 30.6723 15.532C31.0883 15.532 31.4763 15.612 31.8363 15.772C32.1963 15.932 32.5083 16.152 32.7723 16.432C33.0363 16.704 33.2363 17.032 33.3723 17.416C33.5163 17.792 33.5843 18.204 33.5763 18.652L33.5643 19.168H28.4403L28.1643 18.16H32.3283L32.1363 18.364V18.1C32.1123 17.852 32.0323 17.636 31.8963 17.452C31.7603 17.26 31.5843 17.112 31.3683 17.008C31.1603 16.896 30.9283 16.84 30.6723 16.84C30.2803 16.84 29.9483 16.916 29.6763 17.068C29.4123 17.22 29.2123 17.44 29.0763 17.728C28.9403 18.008 28.8723 18.36 28.8723 18.784C28.8723 19.192 28.9563 19.548 29.1243 19.852C29.3003 20.156 29.5443 20.392 29.8563 20.56C30.1763 20.72 30.5483 20.8 30.9723 20.8C31.2683 20.8 31.5403 20.752 31.7883 20.656C32.0363 20.56 32.3043 20.388 32.5923 20.14L33.3243 21.16C33.1083 21.36 32.8603 21.532 32.5803 21.676C32.3083 21.812 32.0203 21.92 31.7163 22C31.4123 22.08 31.1123 22.12 30.8163 22.12ZM35.5675 22V14.068H37.0435V22H35.5675ZM34.3195 17.044V15.676H38.4235V17.044H34.3195Z" fill="#494F56" />
            <path d="M6 23.5H38.6875V24.1H6V23.5Z" fill="#494F56" />
        </svg>
    </span>
</div>