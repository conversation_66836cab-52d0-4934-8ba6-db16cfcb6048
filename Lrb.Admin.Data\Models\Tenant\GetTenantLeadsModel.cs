﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Tenant
{
    public class GetTenantLeadsModel
    {
        public DateTime? Date { get; set; }
        public string? Name { get; set; }
        public long PhoneNumber { get; set; }
        public string? LeadStatus { get; set; }
        public LeadSource Source { get; set; }  
        public EnquiryType EnquiredFor { get; set; }
        public int NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
    }

    public enum EnquiryType
    {
        [Description("None")]
        None = 0,
        [Description("Buy")]
        Buy,
        [Description("Sell")]
        Sell,
        [Description("Rent")]
        Rent
    }

    public enum BHKType
    {
        [Description("None")]
        None = 0,
        [Description("Simplex")]
        Simplex,
        [Description("Duplex")]
        Duplex,
        [Description("PentHouse")]
        PentHouse,
        [Description("Others")]
        Others
    }

    public enum LeadSource
    {
        [Description(EnumDescription.LeadSource.Direct)]
        Direct = 0,
        [Description(EnumDescription.LeadSource.IVR)]
        IVR,
        [Description(EnumDescription.LeadSource.Facebook)]
        Facebook,
        [Description(EnumDescription.LeadSource.LinkedIn)]
        LinkedIn,
        [Description(EnumDescription.LeadSource.GoogleAds)]
        GoogleAds,
        [Description(EnumDescription.LeadSource.MagicBricks)]
        MagicBricks,
        [Description(EnumDescription.LeadSource.NinetyNineAcres)]
        NinetyNineAcres,
        [Description(EnumDescription.LeadSource.Housing)]
        Housing,
        [Description(EnumDescription.LeadSource.GharOffice)]
        GharOffice,
        [Description(EnumDescription.LeadSource.Referral)]
        Referral,
        [Description(EnumDescription.LeadSource.WalkIn)]
        WalkIn,
        [Description(EnumDescription.LeadSource.Website)]
        Website,
        [Description(EnumDescription.LeadSource.Gmail)]
        Gmail,
        [Description(EnumDescription.LeadSource.PropertyMicrosite)]
        PropertyMicrosite,
        [Description(EnumDescription.LeadSource.PortfolioMicrosite)]
        PortfolioMicrosite,
        [Description(EnumDescription.LeadSource.Phonebook)]
        Phonebook,
        [Description(EnumDescription.LeadSource.CallLogs)]
        CallLogs,
        [Description(EnumDescription.LeadSource.LeadPool)]
        LeadPool,
        [Description(EnumDescription.LeadSource.SquareYards)]
        SquareYards,
        [Description(EnumDescription.LeadSource.QuikrHomes)]
        QuikrHomes,
        [Description(EnumDescription.LeadSource.JustLead)]
        JustLead,
        [Description(EnumDescription.LeadSource.WhatsApp)]
        WhatsApp,
        [Description(EnumDescription.LeadSource.YouTube)]
        YouTube,
        [Description(EnumDescription.LeadSource.QRCode)]
        QRCode,
        [Description(EnumDescription.LeadSource.Instagram)]
        Instagram,
        [Description(EnumDescription.LeadSource.OLX)]
        OLX,
        [Description(EnumDescription.LeadSource.EstateDekho)]
        EstateDekho,
        [Description(EnumDescription.LeadSource.GoogleSheet)]
        GoogleSheet,
        [Description(EnumDescription.LeadSource.ChannelPartner)]
        ChannelPartner,
        [Description(EnumDescription.LeadSource.RealEstateIndia)]
        RealEstateIndia,
        [Description(EnumDescription.LeadSource.CommonFloor)]
        CommonFloor,
        [Description(EnumDescription.LeadSource.Data)]
        Data,
        [Description(EnumDescription.LeadSource.RoofandFloor)]
        RoofandFloor,
        [Description(EnumDescription.LeadSource.MicrosoftAds)]
        MicrosoftAds,
        [Description(EnumDescription.LeadSource.PropertyWala)]
        PropertyWala,
        [Description(EnumDescription.LeadSource.ProjectMicrosite)]
        ProjectMicrosite,
        [Description(EnumDescription.LeadSource.MyGate)]
        MyGate,
        [Description(EnumDescription.LeadSource.Flipkart)]
        Flipkart,
        [Description(EnumDescription.LeadSource.PropertyFinder)]
        PropertyFinder,
        [Description(EnumDescription.LeadSource.Bayut)]
        Bayut,
        [Description(EnumDescription.LeadSource.Dubizzle)]
        Dubizzle,
        [Description(EnumDescription.LeadSource.Webhook)]
        Webhook,
        [Description(EnumDescription.LeadSource.TikTok)]
        TikTok,
        [Description(EnumDescription.LeadSource.Snapchat)]
        Snapchat
    }

    public static class EnumDescription
    {
        public static class LeadSource
        {
            public const string IVR = "IVR";
            public const string Facebook = "Facebook";
            public const string LinkedIn = "LinkedIn";
            public const string GoogleAds = "Google Ads";
            public const string MagicBricks = "Magic Bricks";
            public const string NinetyNineAcres = "99 Acres";
            public const string Housing = "Housing.com";
            public const string GharOffice = "GharOffice";
            public const string Referral = "Referral";
            public const string WalkIn = "Walk In";
            public const string Website = "Website";
            public const string Direct = "Direct";
            public const string Gmail = "Gmail";
            public const string PropertyMicrosite = "Microsite";
            public const string PortfolioMicrosite = "Portfolio";
            public const string Phonebook = "Phonebook";
            public const string CallLogs = "Call Logs";
            public const string LeadPool = "Lead Pool";
            public const string SquareYards = "Square Yards";
            public const string QuikrHomes = "Quikr Homes";
            public const string JustLead = "JustLead";
            public const string WhatsApp = "WhatsApp";
            public const string YouTube = "YouTube";
            public const string QRCode = "QR Code";
            public const string Instagram = "Instagram";
            public const string OLX = "OLX";
            public const string EstateDekho = "Estate Dekho";
            public const string GoogleSheet = "Google Sheets";
            public const string ChannelPartner = "Channel Partner";
            public const string RealEstateIndia = "Real Estate India";
            public const string CommonFloor = "Common Floor";
            public const string Data = "Data";
            public const string RoofandFloor = "Roof & Floor";
            public const string MicrosoftAds = "Microsoft Ads";
            public const string PropertyWala = "PropertyWala";
            public const string ProjectMicrosite = "Project Microsite";
            public const string MyGate = "MyGate";
            public const string Flipkart = "Flipkart";
            public const string PropertyFinder = "Property Finder";
            public const string Bayut = "Bayut";
            public const string Dubizzle = "Dubizzle";
            public const string Webhook = "Webhook";
            public const string TikTok = "TikTok";
            public const string Snapchat = "Snapchat";
        }
    }
}
