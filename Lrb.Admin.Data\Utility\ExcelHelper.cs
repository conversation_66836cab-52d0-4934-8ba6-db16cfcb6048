﻿using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using System.Web.Mvc;

namespace Lrb.Admin.Data.Utility
{
    public static class ExcelHelper
    {
        public static byte[] CreateExcelfromList<T>(List<T> items)
        {
            using MemoryStream stream = new();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets? sheets = spreadsheetDocument?.WorkbookPart?.Workbook.AppendChild(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument?.WorkbookPart?.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Tenant_Report"
            };
            sheets?.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            SheetData? sheetData = worksheet?.GetFirstChild<SheetData>();
            PropertyInfo[] properties = typeof(T).GetProperties();
            Row firstRow = new Row();
            foreach (PropertyInfo property in properties)
            {
                Cell cell1 = new()
                {
                    CellValue = new CellValue(property.Name),
                    DataType = CellValues.String,
                };
                firstRow.Append(cell1);
            }
            sheetData?.Append(firstRow);
            foreach (var item in items)
            {
                var row = new Row();
                foreach (PropertyInfo property in properties)
                {
                    Cell cell = new()
                    {
                        CellValue = new CellValue(property?.GetValue(item)?.ToString() ?? string.Empty),
                        DataType = property?.GetValue(item)?.GetType() == typeof(int) || property?.GetValue(item)?.GetType() == typeof(long) || property?.GetValue(item)?.GetType() == typeof(double) ? CellValues.Number : property?.GetValue(item)?.GetType() == typeof(DateTime) ? CellValues.Date : CellValues.String,
                    };
                    row.Append(cell);
                }
                sheetData?.Append(row);
            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument?.Close();
            return stream.ToArray();
        }

        public static byte[] CreateExcelFromList<T>(Dictionary<string, List<T>> items)
        {
            using MemoryStream stream = new MemoryStream();
            try
            {
                using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook))
                {
                    WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
                    workbookpart.Workbook = new Workbook();

                    // Add multiple tabs (worksheets)
                    Sheets sheets = new Sheets();
                    workbookpart.Workbook.AppendChild(sheets);
                    
                    int tabIndex = 0;
                    foreach (var kvp in items)
                    {
                        var tabName = kvp.Key;
                        var records = kvp.Value;

                        // Generate unique relationship ID for the worksheet part
                        string relationshipId = GeneratePartId("rId");

                        WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>(relationshipId);
                        worksheetPart.Worksheet = new Worksheet(new SheetData());

                        Sheet sheet = new Sheet()
                        {
                            Id = workbookpart.GetIdOfPart(worksheetPart),
                            SheetId = (uint)(tabIndex + 1),
                            Name = tabName // Name for each tab
                        };
                        sheets.Append(sheet);

                        // Add the header row
                        SheetData sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
                        PropertyInfo[] properties = typeof(T).GetProperties();
                        Row headerRow = new Row();
                        foreach (PropertyInfo property in properties)
                        {
                            Cell cell = new()
                            {
                                CellValue = new CellValue(property.Name),
                                DataType = CellValues.String,
                                StyleIndex = 1 // Apply custom header style
                            };
                            headerRow.Append(cell);
                        }
                        sheetData.Append(headerRow);

                        // Add data rows
                        foreach (var record in records)
                        {
                            var row = new Row();
                            foreach (PropertyInfo property in properties)
                            {
                                Cell cell = new()
                                {
                                    CellValue = new CellValue(property?.GetValue(record)?.ToString() ?? string.Empty),
                                    DataType = property?.GetValue(record)?.GetType() == typeof(int) || property?.GetValue(record)?.GetType() == typeof(long) || property?.GetValue(record)?.GetType() == typeof(double) ? CellValues.Number : property?.GetValue(record)?.GetType() == typeof(DateTime) ? CellValues.Date : CellValues.String,
                                };
                                row.Append(cell);
                            }
                            sheetData.Append(row);
                        }

                        tabIndex++;
                        // Save the worksheet
                        worksheetPart.Worksheet.Save();
                    }
                    workbookpart.Workbook.Save();
                }
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                // Handle exceptions
                Console.WriteLine($"An error occurred: {ex.Message}");
                return null;
            }
        }

        // Method to generate unique part IDs
        private static string GeneratePartId(string baseId)
        {
            return baseId + Guid.NewGuid().ToString("N");
        }

    }
}
