﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Lead;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IDulicateFeatureService
    {
        Task<PagedResponse<DuplicateFeatureViewModel , string >> GetAllDuplicateFeatureAsync(DuplicateFeatureFilter filter);
        Task<DuplicateFeatureViewModel> GetDuplicateFeatureById(string tenatId);
    }
}
