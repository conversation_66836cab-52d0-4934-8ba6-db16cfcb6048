﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface ICustomSubStatusService
    {
        public Task<IEnumerable<CustomSubStatusDto>> GetCustomMasterLeadSubStatuses(string? tenantId);
        public Task<string?> CreateMasterLeadSubStatus(string tenantId, CustomSubStatusDto custom);
        public Task<CustomSubStatusDto> GetCustomMasterLeadStatusById(Guid id);
        public Task<bool> DeleteMasterLeadSubStatusesById(Guid? id, Guid? transferingId);
        public Task<long> GetLeadCountRequest(Guid? id, string? tenantId);
        public Task<CustomSubStatusDto> UpdateMasterLeadSubStatus(Guid id, Guid baseId, string status, string displayName);
        public Task<IEnumerable<CustomMainStatusesDTO>> GetAllMainStatuses(string? tenantId);
        public Task<Guid> GetStatusId(string? tenantId,string? statusDisplayName);
        public Task<Guid> GetMasterStatusId(Guid? baseId);
        public Task<bool> GetUserAvailablity(string? tenantId, string? name);
    }
}
