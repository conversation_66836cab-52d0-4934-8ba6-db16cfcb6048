﻿* {
    font-family: '<PERSON><PERSON> Deca', sans-serif;
}

.Header {
}

.HeaderText {
    font-weight: 500;
    font-size: 25px;
}

.Body {
    margin-top: 1%;
}

.DomainName {
    margin-bottom: 1%;
}

.DomainNameHeadertext {
    text-decoration: underline;
    font-weight: 400;
    font-size: 14px;
}

.DomainName input {
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    height: 30px;
}

.DomainNameHeaderinput {
    font-weight: 400;
    font-size: 14px;
    padding-left: 1%;
}

.OrganizationInfoHeaderText {
    text-decoration: underline;
    font-weight: 400;
    font-size: 15px;
}

.form-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    background: #fff;
    padding: 1%;
    border-radius: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

    .form-group label {
        font-size: 14px;
        font-weight: 400;
    }

    .form-group input,
    .form-group select {
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 16px;
        height: 35px;
        width: 210px;
        ;
    }

        .form-group input::placeholder,
        .form-group select::placeholder {
            color: #aaa;
            font-size: 14px;
        }

.rupeessymbol {
    position: relative;
}

    .rupeessymbol span {
        position: absolute;
        left: 10px;
        top: 28px;
        font-size: 16px;
    }

    .rupeessymbol input {
        padding-left: 20px;
        width: 100%;
    }

.percentagesymbol {
    position: relative;
}

    .percentagesymbol span {
        position: absolute;
        left: 170px;
        top: 28px;
        font-size: 16px;
    }

.OrganizationInfo {
    margin-bottom: 1%;
}

.AdminInfo {
    margin-bottom: 1%;
}

.PlanFinance {
    margin-bottom: 1%;
}

.Miscellaneous {
    margin-bottom: 1%;
}

.footer {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 14px;
    justify-content: flex-end;
}

    .footer span {
        cursor: pointer;
        padding: 5px 10px;
        border: 1px solid transparent;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

        .footer span:nth-child(1) {
            color: #6c757d;
        }

        .footer span:nth-child(2) {
            color: #6c757d;
            border-left: 1px solid #e0e0e0;
            padding-left: 15px;
        }

        .footer span:nth-child(3) {
            background-color: #000;
            color: #fff;
        }

.vendor-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.vendor-button {
    border-radius: 12px;
    border-width: thin;
    font-size: medium;
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
}

    .vendor-button .remove-icon {
        cursor: pointer;
        color: black;
        margin-left: 10px;
    }
