﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IActivityService
    {
        Task<Response<bool>> UserActivityAsync(AdminUserActivity userActivity);
        Task<AdminUserActivity> GetUserByTokenAsync(string token);
        Task<PagedResponse<GetActivityDto, string>> GetAdminUserActivityAsync(ActivityFilter filter);
        Task<PagedResponse<GetActivityDto,string>> GetTodayActivityAsync(ActivityFilter filter);
        Task<PagedResponse<GetActivityDto , string>> GetYesterdayActivityAsync(ActivityFilter filter);
        Task<PagedResponse<GetActivityDto , string>> GetLastWeekActivityAsync(ActivityFilter filter);
        Task<PagedResponse<GetActivityDto , string>> GetLastMontActivityAsync(ActivityFilter filter);
    }
}
