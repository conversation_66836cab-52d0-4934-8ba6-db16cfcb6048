﻿using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface ITenantRepository
    {
        Task<GetTenantModel> GetTenantByIdAsync(string tenantId);
        Task<int> TotalTenantCount(InvoiceFilter filter);
        Task<int> TotalLeadsCount(GetAllTenantParameter filter);
        Task<int> TotalIntegrationCount(string tenantId, GetAllTenantParameter filter);
        Task<PagedResponse<GetTenantLeadsModel, string>> GetTenantLeadAsync(string tenantId, int pageNumber, int pagerSize, GetAllTenantParameter filter);
        Task<GetTenantLeadsModel> GetLeadByIdAsync(string tenantId);
        Task<GetTenantUserModel> GetUserByIdAsync(string tenantId);
        Task<PagedResponse<GetTenantIntegrationModel, string>> GetTenantIntegrationInfo(string tenantId, int pageNumber, int pagerSize, GetAllTenantParameter filter,string? readConnectionString);
        Task<Address> GetAddressAsync(string tenantId);
        Task<Response<bool>> UpdateTenantStatusAsync(string tenantId);
        Task<TenantProfile> GetTenantWebsite(string tenantId);
        Task<List<string>> GetTenantId();
        Task<int> CountAdminAsync(string tenantId,string readConnectionString);
        Task<IEnumerable<string>> GetAllUserId(string tenantId); 
        Task<Response<bool>> UpdateTenantValidityAsync(string tenantId, DateTime? validityDate);
        Task<List<TenantCompleteDetailsDto>> GetCompleteTenantData();
        Task<GetTenantUserModel> GetUserFromIdAsync(string id);
        Task<List<string>> GetAllTenantAsync();
        Task<bool> ToggleIsActive(string userId, bool value);
        Task UpdateIsDormented();
        Task<bool> UpdateTestingAccount(string tenantId, bool toggleValue);
        Task<bool> GetAccountStatus(string tenantId);
        Task<TotalSubscriptionDetails> GetTotalSubscriptionDetails(string tenantId);
        Task<Response<bool>> CreateTenantInfo(TenantsInfo tenantsInfo, bool isTestIngAccount);
        Task<PagedResponse<InvoiceDto, string>> GetInvoiceDetail(int pageNumber, int pageSize, InvoiceFilter filter);
        Task<bool> UpdateInvoiceNo(string OldInvoiceNo, string NewInvoiceNo, Guid PaymentId);
        Task<bool> UpdateTransactionDate(InvoiceDto InvoiceUpdate);
        Task<bool> UpdateTDSPercentage(InvoiceDto InvoiceTDS);
        Task<UpdateInvoiceDto> GetInvoiceById(Guid? id);
        Task<PagedResponse<GetTenantModel, string>> GetAllData(int pageNumber, int pageSize, GetAllTenantParameter filter);
        Task<bool> ToggleSubscriptionIsActive(Guid SubscriptionId, bool value);
        Task<bool> CompareTenantId(string TenantId);
        Task<IEnumerable<string>> GetInvoiceNo();
        Task<List<TenantViewModelExport>> GetTenantExport(GetAllTenantParameter filter);
        Task<ProfileDto> GetProfileDetails(string tenantId, string readConnectionString);
        Task<bool> UpdateTenantProfile(ProfileDto profileData,string connectionString);
        Task<bool> ToggleSubscriptionAddonIsActive(Guid SubscriptionAddonId, bool value);
        Task<PagedResponse<GetTenantUserModel, string>> GetUserByTenantId(UserFilter filter,string? readConnectionString);
        Task<bool> PostCustomListingSources(string tenantId, string connectionString);
        Task<Response<bool>> CreateDifferentDatabseTenant(string tenantId, string connectionString);
    }
}