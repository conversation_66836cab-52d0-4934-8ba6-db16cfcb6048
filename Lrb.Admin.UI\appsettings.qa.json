{"ConnectionStrings": {"DefaultConnection": "Host=qa-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"}, "LrbApiEndPoints": {"IdentityApi": "https://lrb-qa-identity.leadrat.info/", "WebApi": "https://qa-lrb-webapi.leadrat.info/", "MobileApi": "https://lrb-qa-mobile.leadrat.info/"}, "LrbDomains": {"DomainName": "leadrat.info", "DomainValue": "d390pujtbxatxx.cloudfront.net.", "Type": "CNAME", "Ttl": 3600}, "GoDaddySettings": {"ApiKey": "fYWDoVBV87hz_G7dYJXpr3CnA8Au1MUs1Mh", "Secret": "WiAjFjCnbJjGwbgXAXcdQT", "BaseUrl": "https://api.godaddy.com/v1/"}, "LrbS3Bucket": {"S3BucketUrl": "https://qleadrat-black.s3.ap-south-1.amazonaws.com/"}}