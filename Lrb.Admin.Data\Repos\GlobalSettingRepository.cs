﻿using Dapper;
using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata.Compilers;
using SqlKata.Execution;
using System.Text.Json;

namespace Lrb.Admin.Data.Repos
{
    public class GlobalSettingRepository : IGlobalSettingRepository
    {
        private readonly IDBConnectionFactory _dBFactory;
        protected readonly string Schema = "LeadratBlack";
        public GlobalSettingRepository(IDBConnectionFactory dBFactory)
        {
            _dBFactory = dBFactory;
        }
        public async Task<GlobalSettingViewModel> GetSettingByTenant(string? tenantId, string? readConnectionString)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync(readConnectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Where("IsDeleted", "=", false)
                .Select("HasInternationalSupport", "HasDailyStatusFeatureEnabled", "IsLeadStatusToPendingUpdatesEnabled", "IsLeadsExportEnabled",
                "IsLeadSourceEditable", "IsMicrositeFeatureEnabled", "IsZoneLocationEnabled", "OTPSettings", "ShouldEnablePropertyListing", "IsCustomLeadFormEnabled");

            connection.Close();
            return await query.FirstOrDefaultAsync<GlobalSettingViewModel>();
        }

        public async Task<bool?> GetShouldHideSubscription(string? tenantId, string? readConnectionString)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync(readConnectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.Profiles").Where("TenantId", "=", tenantId).Select("ShouldHideSubscription");
            connection.Close();
            return await query.FirstOrDefaultAsync<bool?>();
        }
        public async Task<Response<bool>> UpdateInternationalSupportAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("HasInternationalSupport");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { HasInternationalSupport = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateDailyStatusEnabledAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("HasDailyStatusFeatureEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { HasDailyStatusFeatureEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateLeadStatusPendingToUpdateAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsLeadStatusToPendingUpdatesEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsLeadStatusToPendingUpdatesEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateLeadExportEnabledAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsLeadsExportEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsLeadsExportEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateLeadSourceEditableAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsLeadSourceEditable");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsLeadSourceEditable = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateDuplicateLeadFeatureAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.DuplicateFeatureInfo").Where("TenantId", "=", tenantId).Select("IsFeatureAdded");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsFeatureAdded = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<DuplicateLeadViewModel> GetDuplicateLeadAsync(string? tenantId, string? readConnectionString)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync(readConnectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());

            var query = db.Query($"{Schema}.DuplicateFeatureInfo").Where("TenantId", "=", tenantId).Select("IsFeatureAdded");
            connection.Close();
            return await query.FirstOrDefaultAsync<DuplicateLeadViewModel>();
        }

        public async Task<Response<bool>> UpdateMicrositeFeatureToggleAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsMicrositeFeatureEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsMicrositeFeatureEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateZoneLocationFeatureToggleAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsZoneLocationEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsZoneLocationEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateToggleAsync(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsZoneLocationEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsZoneLocationEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateShouldEnablePropertyListing(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("ShouldEnablePropertyListing");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { ShouldEnablePropertyListing = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> UpdateEnableCustomLeadForm(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.GlobalSettings").Where("TenantId", "=", tenantId).Select("IsCustomLeadFormEnabled");
            bool status = await query.FirstOrDefaultAsync<bool>();
            await query.UpdateAsync(new { IsCustomLeadFormEnabled = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool?>> DNSEnableDisable(string? tenantId)
        {
            var connection = await _dBFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"tenantmaster.TenantsInfo").Where("Id", "=", tenantId).Select("IsDomainEnabled");
            bool? status = await query.FirstOrDefaultAsync<bool>();
            if (status != null)
            {
                await query.UpdateAsync(new { IsDomainEnabled = !status });
            }
            connection.Close();
            return new Response<bool?>(!status ?? null);
        }

        public async Task<bool?> GetIsDomainEnabledAsync(string? tenantId)
        {
            var connection = await _dBFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"tenantmaster.TenantsInfo").Where("Id", "=", tenantId).Select("IsDomainEnabled");
            connection.Close();
            return await query.FirstOrDefaultAsync<bool?>();
        }
        public async Task<Response<bool>> UpdateShouldHideSubscription(string? tenantId, string? connectionString)
        {
            var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.Profiles").Where("TenantId", "=", tenantId).Select("ShouldHideSubscription");
            bool status = await query.FirstOrDefaultAsync<bool?>() ?? false;
            await query.UpdateAsync(new { ShouldHideSubscription = !status });
            connection.Close();
            return new Response<bool>(!status);
        }

        public async Task<Response<bool>> ToggleCallRecording(string? tenantId, string? connectionString)
        {
            try
            {
                using var connection = await _dBFactory.CreateConnectionAsync(connectionString ?? string.Empty);
                var db = new QueryFactory(connection, new PostgresCompiler());
                var json = await db.Query($"{Schema}.GlobalSettings").Where("TenantId", tenantId).Select("CallSettings").FirstOrDefaultAsync<string>();

                if (string.IsNullOrWhiteSpace(json)) return new Response<bool>(false, "CallSettings not found.");

                var settings = JsonSerializer.Deserialize<CallSettings>(json);
                if (settings == null) return new Response<bool>(false, "Failed to parse CallSettings.");

                settings.IsCallRecordingEnabled = !settings.IsCallRecordingEnabled;
                var jsonStr = JsonSerializer.Serialize(settings);
                var query = $"update \"LeadratBlack\".\"GlobalSettings\" set \"CallSettings\" ='{jsonStr}' where \"TenantId\"='{tenantId}'";
                var result = (connection.QueryAsync(query));

                return new Response<bool>(settings.IsCallRecordingEnabled);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }

        public async Task<Response<bool?>> GetIsCallRecordingEnabled(string? tenantId, string? readConnectionString)
        {
            try
            {
                using var connection = await _dBFactory.CreateReadConnectionAsync(readConnectionString ?? string.Empty);
                var db = new QueryFactory(connection, new PostgresCompiler());
                var newQuery = $"select \"CallSettings\" from \"LeadratBlack\".\"GlobalSettings\" where \"TenantId\"='{tenantId}'";

                var result = (connection.Query<string>(newQuery)).FirstOrDefault();
                var settings = JsonSerializer.Deserialize<CallSettings>(result ?? "");

                return new(settings?.IsCallRecordingEnabled ?? false);
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
    }
}
