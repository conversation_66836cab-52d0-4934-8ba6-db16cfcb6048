<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.200.17" />
    <PackageReference Include="Dapper" Version="2.0.143" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.20.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="Mapster" Version="7.3.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.0" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
    <PackageReference Include="Microsoft.AspNet.WebPages" Version="3.2.9" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Forms" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.10" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="7.0.0" />
    <PackageReference Include="NETCore.MailKit" Version="2.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql" Version="7.0.4" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="SqlKata.Execution" Version="2.4.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.32.1" />
  </ItemGroup>

</Project>
