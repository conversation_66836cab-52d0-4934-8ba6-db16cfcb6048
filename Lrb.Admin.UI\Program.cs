using Blazored.LocalStorage;
using Blazored.Modal;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Lrb.Admin.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Configuration;
using Lrb.Admin.Data.Settings;
using Lrb.Admin.Data.Services;
using Lrb.Admin.UI.Authentication;
using Lrb.Admin.UI.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Radzen;
using static Lrb.Admin.UI.Pages.PendingPayment.PendingPayment;
using Lrb.Admin.UI.Pages.PendingPayment;

var builder = WebApplication.CreateBuilder(args);
builder.Host.ConfigureAppConfiguration((context, config) =>
{
    const string configurationsDirectory = "Configurations";
    var env = context.HostingEnvironment;
    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true);
});

builder.Services.AddScoped<NotificationService>();
builder.Services.AddHttpClient<EmailService>();
builder.Services.AddHttpClient();
builder.Services.AddScoped<DialogService>();

// Register EmailHelper
builder.Services.AddServerSideBlazor();

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor(options => options.DetailedErrors = true);
builder.Services.AddSingleton<WeatherForecastService>();
builder.Services.AddScoped<CustomAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddDataLayer(builder.Configuration);
builder.Services.AddHealthChecks();

builder.Services.AddAuthenticationCore();
builder.Services.AddBlazoredModal();
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddBlazoredModal();
builder.Services.AddScoped<TooltipService>();
builder.Services.AddScoped<DialogService>();
builder.Services.AddScoped<NotificationService>(); 
builder.Services.AddTransient<EmailService>();
builder.Services.AddTransient<DueEmailHelper>();


var app = builder.Build(); 

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.MapHealthChecks("/healthz");

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();

app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

app.Run();
