﻿using Lrb.Admin.Data.Services.HTTP;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Admin.Data.Services
{
    public static class Startup
    {
        public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration config)
        {
            services.AddTransient<IHttpService, HttpService>();
            services.AddTransient<ITenantService, TenantService>();
            services.AddTransient<IUserService, UserService>();
            services.AddTransient<IRoleService, RoleService>();
            services.AddTransient<IAnalyticsService, AnalyticsService>();
            services.AddTransient<IAuthService, AuthService>();
            services.AddTransient<IActivityService, ActivityService>();
            services.AddTransient<IVendorService, VendorService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddTransient<IGlobalService, GlobalService>();
            services.AddTransient<ICustomSubStatusService, CustomSubStatusService>();
            services.AddTransient<IDulicateFeatureService, DulicateFeatureService>();
            services.AddTransient<ISubscriptionService, SubscriptionService>();
            services.AddTransient<IPaymentService, PaymentService>();
            services.AddTransient<IEmailService, EmailService>();
            services.AddScoped<SpinnerService>();
            //services.AddScoped<BlazorDisplaySpinnerAutomaticallyHttpMessageHandler>();
            //services.AddScoped(s =>
            //{
            //    var accessTokenHandler = s.GetRequiredService<BlazorDisplaySpinnerAutomaticallyHttpMessageHandler>();
            //    accessTokenHandler.InnerHandler = new HttpClientHandler();
            //    var uriHelper = s.GetRequiredService<NavigationManager>();
            //    return new HttpClient(accessTokenHandler)
            //    {
            //        BaseAddress = new Uri(uriHelper.BaseUri)
            //    };
            //});
            return services;
        }
    }
}
