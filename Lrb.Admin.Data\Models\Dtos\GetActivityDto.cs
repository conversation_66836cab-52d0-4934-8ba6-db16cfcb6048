﻿using Lrb.Admin.Data.Models.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class GetActivityDto
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public Role Role { get; set; }
        public string? TenantId { get; set; }
        public DateTime? ActivityTime { get; set; }
        public string? Activity { get; set; }
        public string? Token { get; set; }
        public Guid AdminUserId { get; set; }
    }
}
