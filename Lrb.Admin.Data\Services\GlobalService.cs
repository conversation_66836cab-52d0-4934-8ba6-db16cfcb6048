﻿using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Options;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class GlobalService : IGlobalService
    {
        private readonly LrbDomains _domains;
        private readonly GoDaddySettings _goDaddySetings;
        private readonly IGlobalSettingRepository _repository;
        public GlobalService(IGlobalSettingRepository repository, IOptions<LrbDomains> domainOptions, IOptions<GoDaddySettings> goDaddySetings)
        {
            _repository = repository;
            _domains = domainOptions.Value;
            _goDaddySetings = goDaddySetings.Value;
        }

        public async Task<DuplicateLeadViewModel> GetDuplicateLead(string tenantId, string? readConnectionString)
        {
            return await _repository.GetDuplicateLeadAsync(tenantId, readConnectionString);
        }

        public async Task<GlobalSettingViewModel> GetSettingByTenantAsync(string tenantId, string? readConnectionString)
        {
            return await _repository.GetSettingByTenant(tenantId, readConnectionString);
        }
        public async Task<Response<bool>> UpdateDailyStatusAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateDailyStatusEnabledAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateDuplicateLeadAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateDuplicateLeadFeatureAsync(tenantId,connectionString);
        }

        public async Task<Response<bool>> UpdateInternationalNumberAsync(string tenantId, string connectionString)
        {
            return await _repository.UpdateInternationalSupportAsync(tenantId,connectionString);
        }

        public async Task<Response<bool>> UpdateLeadExportAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateLeadExportEnabledAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateLeadSourceAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateLeadSourceEditableAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateLeadStatusPendingAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateLeadStatusPendingToUpdateAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateMicrositeFeatureAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateMicrositeFeatureToggleAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateZoneLocationFeatureAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateZoneLocationFeatureToggleAsync(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateShouldEnablePropertyListingAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateShouldEnablePropertyListing(tenantId, connectionString);
        }

        public async Task<Response<bool>> UpdateEnableCustomLeadFormAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateEnableCustomLeadForm(tenantId, connectionString);
        }

        public async Task<Response<bool?>> DNSEnableDisableAsync(string tenantId, bool? status)
        {
            bool DomaincreatedOrDeleated = false;
            Response<bool?> response = new Response<bool?>(false);
            try
            {
                if (status != null)
                {
                    if (status == true)
                    {
                        DomaincreatedOrDeleated = await DeleteDNSRecords(tenantId);
                    }
                    else if (status == false)
                    {
                        DomaincreatedOrDeleated = await AddDNSRecord(tenantId);
                    }
                }
            }
            catch (Exception ex) { }
            //return false;
            if (DomaincreatedOrDeleated)
            {
                response = await _repository.DNSEnableDisable(tenantId);
            }
             
            return new Response<bool?>(DomaincreatedOrDeleated);
        }
        public async Task<bool?> GetIsDomainEnabledAsync(string tenantId)
        {
            return await _repository.GetIsDomainEnabledAsync(tenantId);
        }
        public async Task<bool?> GetShouldHideSubscriptionAsync(string tenantId, string? readConnectionString)
        {
            return await _repository.GetShouldHideSubscription(tenantId, readConnectionString);
        }

        public async Task<bool> DeleteDNSRecords(string tenantId)
        {
            try
            {
                Console.WriteLine(tenantId);
                var resource = $"domains/{_domains.DomainName}/records/CNAME/{tenantId}";
                RestClient restClient = new RestClient(_goDaddySetings.BaseUrl);
                var request = new RestRequest(resource);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Authorization", $"sso-key {_goDaddySetings.ApiKey}:{_goDaddySetings.Secret}");
                var response = await restClient.DeleteAsync(request);
                return response.StatusCode == System.Net.HttpStatusCode.NoContent;
            }
            catch (Exception ex) { return false; }
        }
        private async Task<bool> AddDNSRecord(string tenantName)
        {
            try
            {
                var resource = $"domains/{_domains.DomainName}/records";
                var payload = new List<AddDNSRecordRequest>()
                {
                    new AddDNSRecordRequest
                    {
                        name = tenantName,
                        data = _domains.DomainValue,
                        ttl = _domains.Ttl,
                        type = _domains.Type,
                    }
                };
                RestClient restClient = new RestClient(_goDaddySetings.BaseUrl);
                var request = new RestRequest(resource);
                request.AddJsonBody(payload);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Authorization", $"sso-key {_goDaddySetings.ApiKey}:{_goDaddySetings.Secret}");
                var response = await restClient.PatchAsync(request);
                return response.StatusCode == System.Net.HttpStatusCode.OK;
            }
            catch (Exception ex)
            {
                return false;
            }

        }
        public async Task<Response<bool>> UpdateShouldHideSubscriptionAsync(string tenantId, string? connectionString)
        {
            return await _repository.UpdateShouldHideSubscription(tenantId, connectionString);
        }

        public async Task<Response<bool>> ToggleCallRecordingAsync(string tenantId, string? connectionString)
        {
            return await _repository.ToggleCallRecording(tenantId, connectionString);
        }

        public async Task<Response<bool?>> GetIsCallRecordingEnabledAsync(string tenantId, string? connectionString)
        {
            return await _repository.GetIsCallRecordingEnabled(tenantId, connectionString);
        }
    }
}
