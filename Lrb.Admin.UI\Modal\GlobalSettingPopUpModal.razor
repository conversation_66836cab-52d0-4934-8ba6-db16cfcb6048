﻿
<style>
    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

        /* Hide default HTML checkbox */
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:after {
            content: "";
            position: absolute;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            top: 1px;
            left: 1px;
            transition: all 0.5s;
        }

    input:checked + .slider {
        background-color: #439B89;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #439B89;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    input:checked + .slider:after {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 34px;
    }

        .slider.round:before {
            border-radius: 50%;
        }
</style>

<EditForm Model="GlobalSettingView">
    <div class="setting-pop-up">
        <div class="golbal-setting-international-Support" style="display:flex;justify-content:space-between">
            <span style=" font-weight:bold">International Support :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.HasInternationalSupport">
                <span class="slider round" @onclick="( async () => { await UpdateInternationalNumber(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-export" style="display:flex;justify-content:space-between; padding-top:1%;">
            <span style=" font-weight:bold">Lead Export :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsLeadsExportEnabled">
                <span class="slider round" @onclick="( async () => { await UpdateLeadExportEnabled(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-source" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Lead Source :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsLeadSourceEditable">
                <span class="slider round" @onclick="( async () => { await UpdateLeadSourceEditable(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Lead Status To Pending Updates :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsLeadStatusToPendingUpdatesEnabled">
                <span class="slider round" @onclick="( async () => { await UpdateLeadStatusPendingToUpdate(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Microsite Feature :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsMicrositeFeatureEnabled">
                <span class="slider round" @onclick="( async () => { await UpdateMicrositeEditable(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Zone & Location :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsZoneLocationEnabled">
                <span class="slider round" @onclick="( async () => { await UpdateZoneLocationEditable(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex; justify-content:space-between; padding-top:1%;">
            <span style="font-weight: bold">OTP :</span>
            <label class="switch">
                <input type="checkbox" @bind="OTPSettings" disabled="true">
                <span class="slider round" style="pointer-events: none; cursor: default;"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Listing Management :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.ShouldEnablePropertyListing">
                <span class="slider round" @onclick="( async () => { await UpdateShouldEnablePropertyListing(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Dubai Lead Form :</span>
            <label class="switch">
                <input type="checkbox" checked="@GlobalSettingView?.IsCustomLeadFormEnabled">
                <span class="slider round" @onclick="( async () => { await EnableCustomLeadFormAsync(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Hide Subscription :</span>
            <label class="switch">
                <input type="checkbox" checked="@ShouldHideSubscription">
                <span class="slider round" @onclick="( async () => { await UpdateShouldHideSubscription(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Call Recording :</span>
            <label class="switch">
                <input type="checkbox" checked="@IsCallRecordingEnabled">
                <span class="slider round" @onclick="( async () => { await ToggleCallRecording(); await OnInitializedAsync();})"></span>
            </label>
        </div>
        @if (IsDomainEnabled != null)
        {
            <div class="golbal-setting-lead-status" style="display:flex;justify-content:space-between;padding-top:1%;">
                <span style=" font-weight:bold">DNS :</span>
                <label class="switch">
                    <input type="checkbox" checked="@IsDomainEnabled">
                    <span class="slider round" @onclick="( async () => { await DNSEnableDisable(); await OnInitializedAsync();})"></span>
                </label>
            </div>
        }
        
    </div>
</EditForm>
<EditForm Model="DuplicateLeadModel">
    <div class="setting-pop-up">
        <div class="golbal-setting-duplicate-lead" style="display:flex;justify-content:space-between;padding-top:1%;">
            <span style=" font-weight:bold">Duplicate Lead :</span>
            <label class="switch">
                <input type="checkbox" checked="@DuplicateLeadModel?.IsFeatureAdded">
                <span class="slider round" @onclick="( async () => { await UpdateDuplicateLeadFeature(); await OnInitializedAsync();})"></span>
            </label>
        </div>
    </div>
</EditForm>





