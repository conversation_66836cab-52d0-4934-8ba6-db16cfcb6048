﻿@page "/loginpage"



<div>
    <section class="bg-light py-3 py-md-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5 col-xxl-4">
                    <div class="card border border-light-subtle rounded-3 shadow-sm">
                        <div class="text-center">
                            <h1 class="fs-4 fw-normal mt-3">Welcome to Leadrat Admin</h1>
                        </div>
                        <div class="card-body p-2 p-md-2 p-xl-4">
                            <h2 class="fs-6 fw-normal text-center text-secondary mb-4">Sign in to your account</h2>
                            <EditForm Model="UserLogin">
                                <div class="row gy-2 overflow-hidden">
                                    <div class="col-12">
                                        <div class="form-floating mb-3">
                                            <input @bind-value="UserLogin.UserName" type="username" class="form-control" name="username" id="username" placeholder="admin" required>
                                            <label for="username" class="form-label">UserName</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating mb-3">
                                            <input @bind-value="UserLogin.Password" type="password" class="form-control" name="password" id="password" placeholder="***" required>
                                            <label for="password" class="form-label">Password</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                    </div>
                                    <div class="col-12">
                                        <div class="d-grid my-3">
                                            <button @onclick="HandleLogin" class="btn btn-primary btn-lg" type="submit">Log in</button>
                                        </div>
                                    </div>
                                </div>
                            </EditForm>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
