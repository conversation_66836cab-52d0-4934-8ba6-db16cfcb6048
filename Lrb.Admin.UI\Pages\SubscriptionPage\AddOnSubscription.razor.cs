﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Microsoft.AspNetCore.Authorization;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Invoice;
using Microsoft.AspNetCore.Components.Forms;
using Lrb.Admin.Data.Utility;

namespace Lrb.Admin.UI.Pages.SubscriptionPage
{
    [Authorize(Roles = "Admin")]
    public partial class AddOnSubscription
    {
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IJSRuntime jSRuntime { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public ITenantService TenantService { get; set; }
        [Parameter]
        public string? TenantId {  get; set; }
        [Parameter]
        public LRBSubscription SubscriptionModel {  get; set; }
        public AddOnSubscriptionsDto Model { get; set; } = new();
        public string? Token { get; set; }
        public List<int> Parts { get; set; } = new() { 1, 2, 3, 4, 5, 6 };
        public LRBSubscriptionAddOns SubscriptionAddon { get; set; } = new();
        private string IsTDSApplicable { get; set; }
        private float? TDSNumber { get; set; }
        List<string> fileData = new List<string>();
        protected override async Task OnInitializedAsync()
        {
            Model.DaysLeft = Daysleft();
            Model.PerUserCost = CalculatingDailyCost();
        }

        public async Task<bool> AddSubscriptionAsync()
        {
            if(SubscriptionModel == null)
            {
                await jSRuntime.InvokeVoidAsync("alert", $"Subscription not available");
                return false;
            }
            try
            {
                SubscriptionAddon.Id = Guid.NewGuid();
                SubscriptionAddon.SubscriptionId = SubscriptionModel.Id;
                SubscriptionAddon.NetAmount = Model.NetAmount;
                SubscriptionAddon.SoldLicenses = Model.SoldLicenses;
                SubscriptionAddon.CreatedOn = DateTime.UtcNow;
                if (Model.Type == PaymentType.Full)
                {
                    SubscriptionAddon.PaidAmount = Model.TotalAmount;
                    SubscriptionAddon.DueAmount = 0;
                    SubscriptionAddon.DueDate = null;
                }
                else
                {
                    SubscriptionAddon.PaidAmount = Model.PaidTotalAmount;
                    SubscriptionAddon.DueAmount = Model.DueAmount;
                    SubscriptionAddon.DueDate = Model.DueDate;
                }
                SubscriptionAddon.TotalAmount = Model.TotalAmount;
                SubscriptionAddon.PaymentDate = Model.PaymentDate;
                SubscriptionAddon.GSTAmount = Model.GstAmount;
                SubscriptionAddon.TenantId = TenantId;
                SubscriptionAddon.IsActive = true;
                SubscriptionAddon.IsExpired = false;
                SubscriptionAddon.IsDeleted = false;
                var response = await SubscriptionService.CreateAddOnSubscription(SubscriptionAddon);
                if (response != null)
                {
                    LRBPayments payment = new LRBPayments();
                    if (Model.Type == PaymentType.Full)
                    {
                        payment = new()
                        {
                            //Id = Guid.NewGuid(),
                            TenantId = TenantId,
                            Type = Model.Type,
                            //PartPaid = 1,
                            //PartNumber = Model.NoOfParts,
                            NetAmount = Model.NetAmount,
                            GSTAmount = Model.GstAmount,
                            TotalAmount = Model.TotalAmount,
                            NextDueDate = null,
                            //PaidAmount = Model.TotalAmount,
                            PendingAmount = 0,
                            CreatedOn = Model.PaymentDate,
                            SubscriptionAddOnId = SubscriptionAddon.Id,
                            Mode = Model.PaymentMode,
                            Description = Model.Description,
                            IsDeleted = false,
                            InvoiceNumber = await GenerateInvoiceNumber()
                        };
                    }
                    else
                    {
                        payment = new()
                        {
                            //Id = Guid.NewGuid(),
                            TenantId = TenantId,
                            Type = Model.Type,
                            //PartPaid = 1,
                            //PartNumber = Model.NoOfParts,
                            NetAmount = Model.PaidNetAmount,
                            GSTAmount = Model.PaidGSTAmount,
                            TotalAmount = Model.PaidTotalAmount,
                            NextDueDate = Model.DueDate,
                            PendingAmount = Model.DueAmount,
                            //PaidAmount = Model.PaidTotalAmount,
                            CreatedOn = Model.PaymentDate,
                            SubscriptionAddOnId = SubscriptionAddon.Id,
                            Mode = Model.PaymentMode,
                            Description = Model.Description,
                            IsDeleted = false,
                            InvoiceNumber = await GenerateInvoiceNumber()
                        };
                    }
                    var added = await PaymentService.CreatePaymentAsync(payment);
                    if (added != null)
                    {
                        string? TDSUrl = null;
                        if (IsTDSApplicable == "Applicable" && fileData.Count > 0)
                        {
                            string fileName = $"{TenantId}_{Model.PaymentDate.ToString("ddMMyyHHmm")}";
                            var tdsresponse = await TenantService.UploadTDSCertificate(fileData, fileName);
                            TDSUrl = tdsresponse;
                        }
                        CreateInvoice createInvoice = new()
                        {
                            Id = new Guid(),
                            TenantId = payment.TenantId,
                            CreatedOn = Model.PaymentDate,
                            PaymentId = payment.Id,
                            InvoiceNo = payment.InvoiceNumber,
                            TDSPercent = TDSNumber ?? null,
                        };
                        await PaymentService.CreateInvoiceAsync(createInvoice);
                        await jSRuntime.InvokeVoidAsync("alert", $"Addon Subscription for {SubscriptionModel.TenantId} Successfully Created");
                        Token = await LocalStorage.GetItemAsync<string>("token");
                        var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                        AdminUserActivity userActivity = new()
                        {
                            AdminUserId = userActivityHistory.AdminUserId,
                            TenantId = TenantId,
                            ActivityTime = DateTime.UtcNow,
                            Activity = ActivityConst.AddedAddonSubscription + " " + SubscriptionModel.Name,
                            Token = Token
                        };
                        await ActivityService.UserActivityAsync(userActivity);
                    }
                    await jSRuntime.InvokeVoidAsync("location.reload");
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }

        }

        public async Task<string> GenerateInvoiceNumber()
        {
            if (DateTime.UtcNow.Month >= 4)
            {
                return "DSL/" + DateTime.UtcNow.Year % 100 + "-" + ((DateTime.UtcNow.Year % 100) + 1) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
            else
            {
                return "DSL/" + ((DateTime.UtcNow.Year % 100) - 1) + "-" + (DateTime.UtcNow.Year % 100) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
        }
        public async Task Reset()
        {
            var isConfirmed = await jSRuntime.InvokeAsync<bool>("confirm", $"are you sure");
            if (isConfirmed)
            {
                Model = new();
            }
        }

        public int Daysleft()
        {
            int DaysLeft = 0;
            var today = DateTime.UtcNow;
            switch (SubscriptionModel.BillingType)
            {
                case BillingType.Yearly:
                    var daysLeftInYear = (SubscriptionModel.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInYear;
                    break;
                case BillingType.Halfyearly:
                    var daysLeftInHalfYear = (SubscriptionModel.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInHalfYear;
                    break;
                case BillingType.Quarterly:
                    var daysLeftInQuarterly = (SubscriptionModel.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInQuarterly;
                    break; 
            }
            return DaysLeft;
        }

        public double CalculatingDailyCost()
        {
            if (SubscriptionModel != null)
            {
                double perUserPrice;
                var NetAmount = SubscriptionModel.NetAmount;
                var SubscriptionLicense = SubscriptionModel.SoldLicenses;
                var BillingType = SubscriptionModel.BillingType;
                var PerLicenseAmountWithOutGst = NetAmount / SubscriptionLicense;

                if ((int)BillingType == 1)
                {
                    perUserPrice = Math.Round((PerLicenseAmountWithOutGst / 3),2);
                    return (perUserPrice / 30);
                }
                else if ((int)BillingType == 2)
                {
                    perUserPrice = Math.Round((PerLicenseAmountWithOutGst / 6),2);
                    return (perUserPrice / 30);
                }
                else if ((int)BillingType == 3)
                {
                    perUserPrice = Math.Round((PerLicenseAmountWithOutGst / 12),2);
                    return (perUserPrice / 30);
                }
                return 0;
            }
            return 0;

        }
        private async Task TDSFileUpload(InputFileChangeEventArgs files)
        {
            foreach (var file in files.GetMultipleFiles())
            {
                var data = await PdfUploadHelper.PdfToByteArray(file);
                if (data != null)
                {
                    var base64Data = Convert.ToBase64String(data);
                    fileData.Add(base64Data);
                }
            }
        }

    }
}
