﻿using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Models.Roles;
using Lrb.Admin.Data.Repos;

namespace Lrb.Admin.Data.Services
{
    public class RoleService : IRoleService
    {
        private readonly IDapperRepositoryAsync _dapperRepository;

        public RoleService(IDapperRepositoryAsync dapperRepository)
        {
            _dapperRepository = dapperRepository;
        }
        public async Task<ApplicationRole> GetAdminRole(string tenantId,string? connectionString)
        {
            var sql = $"select * from \"Identity\".\"Roles\" where \"TenantId\" = '{tenantId}' and \"Name\" like '%Admin%'";
            var role = await _dapperRepository.QueryFirstOrDefaultAsync<ApplicationRole>(sql,connectionString:connectionString);
            return role;
        }

        public async Task<ApplicationRole> GetBasicRoleAsync(string tenantId, string? connectionString)
        {
            var sql = $"select * from \"Identity\".\"Roles\" where \"TenantId\" = '{tenantId}' and \"Name\" like '%Basic%'";
            var role = await _dapperRepository.QueryFirstOrDefaultAsync<ApplicationRole>(sql, connectionString: connectionString);
            return role;
        }

        public async Task<ApplicationRole> GetHRRoleAsync(string tenantId, string? connectionString)
        {
            var sql = $"select * from \"Identity\".\"Roles\" where \"TenantId\" = '{tenantId}' and \"Name\" like '%HR%'";
            var role = await _dapperRepository.QueryFirstOrDefaultAsync<ApplicationRole>(sql, connectionString: connectionString);
            return role;
        }

        public async Task<ApplicationRole> GetManagerRoleAsync(string tenantId, string? connectionString)
        {
            var sql = $"select * from \"Identity\".\"Roles\" where \"TenantId\" = '{tenantId}' and \"Name\" like '%Manager%'";
            var role = await _dapperRepository.QueryFirstOrDefaultAsync<ApplicationRole>(sql, connectionString: connectionString);
            return role;
        }

        public async Task<ApplicationRole> GetSalesExecutiveRoleAsync(string tenantId, string? connectionString)
        {
            var sql = $"select * from \"Identity\".\"Roles\" where \"TenantId\" = '{tenantId}' and \"Name\" like '%SalesExecutive%'";
            var role = await _dapperRepository.QueryFirstOrDefaultAsync<ApplicationRole>(sql, connectionString: connectionString);
            return role;
        }
    }
}
