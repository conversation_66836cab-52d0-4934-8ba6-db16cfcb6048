﻿/*@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
@import url('c:\users\<USER>\source\repos\leadrat-black-admin\lrb.admin.ui\shared\mainlayout.razor.css');*/



/*UI for Mobile devices */


/*@media (min-width:320px) and (max-width: 476px) {
    * {
        font-family: Helvetica;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .filter {
        border: 1px solid black;
        margin: auto;
        overflow: scroll;
    }

    img.header-setting-img {
        width: 5%;
        float: right;
    }

    .logo img {
        max-width: 95%;
    }

    img.header-setting-img {
        width: 5%;
    }

    .logo-down {
        text-align: center;
        margin: auto;
        word-break: break-word;
    }

    .filter-left {
        display: flex;
        border-bottom: 2px solid black;
        padding: 1% 2%;
    }

    .filter-right {
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-1 {
        align-items: baseline;
        border:1px solid black;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-1-1 {
        display: flex;
        flex-wrap: nowrap;
        width: 33%;
    }

    .filter-right-1-2 {
        display: flex;
        flex-wrap: nowrap;
        width: 33%;
    }

    .filter-right-1-3 {
        display: flex;
        flex-wrap: nowrap;
        width: 33%;
    }

    .filter-right-2 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-2-noofuser {
        width: 100%;
    }

    .filter-right-2-account {
        width: 100%;
    }

    .filter-right-2-user {
        width: 100%;
    }

    .filter-right-3 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-3-admin {
        width: 100%;
    }

    .filter-right-3-unassigned {
        width: 100%;
    }

    .filter-right-3-assinged {
        width: 100%;
    }

    .filter-right-4 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-4-property {
        width: 100%;
    }

    .filter-right-4-tenant {
        width: 100%;
    }

    .filter-right-4-activeusers {
        width: 100%;
        padding-left: 3%
    }

    .filter-right-5 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 66%;
        padding-left: 1%;
    }

    .filter-right-5-inactiveusers {
        width: 100%;
    }

    .filter-right-5-ispaid {
        width: 100%;
    }

    .tenant-detail {
        display: flex;
        padding-top: 2%;
        padding-bottom: 2%;
        flex-wrap: wrap;
        gap: 1%
    }

    .tenant-detail button {
        max-width: 100%
     }

    .table-header {
        display: flex;
        margin-top: 1%;
        border: 2px solid black;
    }

    .table-header-button {
        padding: 2% 1%;
        display: flex;
        width: 100%;
        flex-wrap: wrap;
    }
    

    .table-header-button-refresh {
        width: 24%
    }

    .table-header-button-add-user {
        width: 38%;
    }

    .table-header-button-add-bulk {
        width: 38%;
    }

    .table-header-button-notification {
        margin-left: 2px;
        padding-top: 2%;
        width: 20%;
    }

    .table-header-search {
        padding-top: 2%;
        width: 84%;
    }

    .table-header-page {
        padding-top: 2%;
        float: right;
    }

    .table-header-button {
        padding: 2% 1%;
        display: -webkit-box;
        width: 100%;
    }

    .table-detail {
        border: 2px solid black;
        padding-top: 1%;
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
        display: flex;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-11 {
        display: -webkit-inline-box;
        margin-top: 1%
    }

    .user-pagination-filter {
        border: 2px solid black;
        margin-left: 52%
    }

    .integration-pagination-filter {
        border: 2px solid black;
        margin-left: 52%
    }

    .w-220 {
        width: 220px;
    }
}*/

/*----------------------------------------------------------------------------------------*/
/* Ui for ipad and tablet*/


/*@media (min-width:477px) and (max-width:768px) {
    * {
        font-family: Helvetica;
    }

    .logo-down {
        word-break: break-word;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    img.header-setting-img {
        width: 5%;
        float: right;
    }

    .filter {
        border: 1px solid black;
        margin: auto;
        display: flex;
        overflow: scroll;
    }

    .filter-left {
        border-right: 2px solid black;
        padding: 1% 2%;
    }

    .filter-right {
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-1 {*/
        /*display: flex;*/
        /*align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-1-onboard {
        width: 100%;
    }

    .filter-right-1-sub {
        width: 100%;*/
        /*padding-left: 3%*/
    /*}

    .filter-right-1-exp {
        width: 100%;*/
        /*padding-left: 3%*/
    /*}

    .filter-right-2 {*/
        /*display: flex;*/
        /*align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-2-account {
        width: 100%;
    }

    .filter-right-2-noofuser {
        width: 100%;
    }

    .filter-right-2-user {
        width: 100%;*/
        /* padding-left: 3%*/
    /*}


    .filter-right-3 {*/
        /*display: flex;*/
        /*align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-3-admin {
        width: 100%;*/
        /*padding-left: 3%*/
    /*}

    .filter-right-3-unassigned {
        width: 100%;
    }

    .filter-right-3-assinged {
        width: 100%;*/
        /*padding-left: 3%*/
    /*}

    .filter-right-4 {
        align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }*/

    /*.filter-right-4-property {
        width: 100%;*/
    /*padding-left: 3%*/
    /*}

    .filter-right-4-tenent{
        width: 100%;
    }

    .filter-right-4-activeusers{
        width: 100%;
    }*/

    /*.filter-right-5 {
        align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }*/

    /*.filter-right-5-inactiveusers{
        width: 100%;
    }

    .filter-right-5-ispaid{
        width: 100%;
    }*/

    /*.tenant-detail {
        display: flex;
        padding-top: 1%;
        padding-bottom: 1%;
        flex-wrap: wrap;
        gap: 1%;
    }

    .table-header {
        display: flex;
        margin-top: 1%;
        border: 2px solid black;
    }

    .table-header-button {
        padding: 2% 1%;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
    }

    .table-header-button-refresh {
        width: 15%;
    }

    .table-header-button-add-user {
        width: 26%;
    }

    .table-header-button-add-bulk {
        width: 26%;
    }

    .table-header-search {
        padding-top: 2%;
        width: 84%;
    }

    .table-header-page {
        padding-top: 2%;
        float: right;
    }

    .table-header-search {
        padding-left: 1%;
    }

    .table-detail {
        border: 2px solid black;
        padding-top: 1%;
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-11 {
        display: -webkit-inline-box;
        margin-top: 2%;
        width: 100%
    }
    

    .user-pagination-filter {
        border: 2px solid black;
        margin-left: 50%
    }

    .integration-pagination-filter {
        border: 2px solid black;
        margin-left: 50%
    }
}*/


/*-------------------------------------------------------------------------------------------
*/
/* UI for laptop*/



/*@media (min-width:769px) {
    * {
        font-family: Helvetica;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .logo-down {
        word-break: break-word;
        gap: 100%
    }

    img.header-setting-img {
        width: 3%;
        float: right;
    }

    .filter {
        border: 1px solid black;
        margin: auto;
        display: flex;
        overflow: scroll;
    }

    .filter-left {
        border-right: 2px solid black;
        padding: 1% 2%;
        width:330px;
    }

    .filter-right {
        width: 1500px;
        padding-left: 1%;

    }

    .filter-right-1 {
        display: flex;
        flex-wrap: nowrap;
        align-items: baseline;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-1-1 {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
    }

    .filter-right-1-2 {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
    }

    .filter-right-1-3 {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        padding-left: 3%
    }

    .filter-right-2 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-2-noofuser {
        width: 100%;
        padding-right:1%;
    }

    .filter-right-2-account {
        width: 100%;
    }

    .filter-right-2-user {
        width: 100%;
        padding-left:2%;
    }

    .filter-right-3 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-3-admin {
        width: 100%;
    }

    .filter-right-3-unassigned {
        width: 100%;
        padding-right:1%;
    }

    .filter-right-3-assinged {
        width: 100%;
        padding-left:2%;
    }

    .filter-right-4 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 100%;
        padding-left: 1%;
    }

    .filter-right-4-property {
        width: 100%;
    }

    .filter-right-4-tenant {
        width: 100%;
    }

    .filter-right-4-activeusers {
        width: 100%;
        padding-left: 3%
    }

    .filter-right-5 {
        align-items: baseline;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 1% 0;
        width: 66%;
        padding-left: 1%;
    }

    .filter-right-5-inactiveusers {
        width: 100%;
    }

    .filter-right-5-ispaid {
        width: 100%;
    }


    .tenant-detail {
        display: flex;
        padding-top: 1%;
        padding-bottom: 1%;
        gap: 1%
    }
    .tenant-detail-1 {
        display: flex;
        gap: 1%
    }

    .table-header {
        display: flex;
        margin-top: 1%;
        border: 2px solid black;
        margin: auto
    }

    .table-header-button-notification {
        margin-left: 5px;
    }

    .table-header-button {
        padding: 0 1%;
        display: -webkit-box;
        width: 100%;
        margin: auto;
    }
    .table-header-button-1 {
        display: -webkit-box;
        width: 100%;
        margin: auto;
        gap: 2px;
    }
    .table-header-search {
        margin: auto;
    }

    .table-detail {
        border: 2px solid black;
        padding-top: 1%;
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .table-paging-11 {
        display: -webkit-inline-box;
        margin-top: 2%;
        width: 100%
    }
    .table-paging-12 {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }
    .user-pagination-filter {
        border: 2px solid black;
        border-radius: 10px;
        margin-left: 73%;
    }

    .user-pagination-count {
        border: 2px solid black;
        font-weight: bold;
        border-radius: 10px;
        padding: 10px;
    }

    .status-data{

        display:flex;
        width:fit-content;
    }

    .status-header {
        display: flex;
        margin-top: 1%;
        border: 2px solid black;
        margin: auto;
        justify-content:space-between;
        width: stretch;
    }

    .btn-status-delete {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        font-size: 10px !important;
        border-radius: 5px;
        color: white;
        border-color:dimgrey;
        background-color: dimgrey !important;
        width: 20px !important;
        height: 20px !important;
        text-align: center !important;
    }

    .btn-status-add {
        display: flex !important;
        justify-content: center !important;
        align-content:center;
        align-items: center !important;
        font-size: 15px !important;
        border-radius: 10px;
        color: white;
        border-color: dimgrey;
        background-color: dimgrey !important;
        width:fit-content !important;
        height: 55% !important;
        text-align: center !important;
        border-radius:
    }

    .integration-pagination-filter {
        border: 2px solid black;
        border-radius: 10px;
        margin-left: 73%;
    }
}

select {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
}*/