﻿namespace Lrb.Admin.Data.Models.Roles
{
    public class CreateRoleModel
    {
        public string? Id { get; set; }
        public string Name { get; set; } = default!;
        public string? Description { get; set; }
        public List<string> Permissions { get; set; } = default!;
    }
    public class UserRoleModel
    {
        public string? RoleId { get; set; }
        public string? RoleName { get; set; }
        public string? Description { get; set; }
        public bool Enabled { get; set; }
    }
}
