﻿<div class="@(sidebarExpanded ? "sidebar" : "sidebar-collaped")">
    <div class="sidebar-options">
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo("/"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14.6507 17.7782H11.8547C11.5873 17.6776 11.49 17.4822 11.4919 17.2008C11.4995 16.042 11.4958 14.8835 11.4946 13.7247C11.4946 13.208 11.1797 12.8965 10.6616 12.895C10.2316 12.895 9.80092 12.9072 9.37149 12.8917C8.84421 12.8725 8.4929 13.2509 8.5011 13.7539C8.51903 14.902 8.5011 16.0508 8.50961 17.1993C8.51144 17.4801 8.41661 17.6773 8.14826 17.7782H5.29152C5.28575 17.7548 5.26751 17.7509 5.24655 17.7478C4.50744 17.6111 3.87075 16.9324 3.88503 15.9897C3.90752 14.5331 3.89111 13.0758 3.89081 11.6186C3.89081 11.5833 3.88868 11.5478 3.89081 11.5125C3.89506 11.456 3.87622 11.4271 3.81483 11.4317C3.76955 11.435 3.72366 11.4317 3.67837 11.4317C2.99853 11.4277 2.46791 11.0278 2.27341 10.3759C2.26307 10.3416 2.27341 10.2914 2.22266 10.275V9.72768C2.24636 9.72373 2.25304 9.70579 2.25699 9.68513C2.33145 9.35782 2.5217 9.10497 2.75571 8.87157C4.7532 6.87874 6.74887 4.88307 8.74271 2.88457C9.02382 2.60254 9.30524 2.33146 9.71309 2.24636C9.71947 2.24636 9.72281 2.23086 9.72737 2.22266H10.2136C10.2185 2.23056 10.2224 2.24515 10.2282 2.24575C10.5649 2.2886 10.8315 2.45849 11.0664 2.69463C13.1492 4.77883 15.2328 6.86222 17.3172 8.94481C17.5254 9.14633 17.6729 9.40233 17.7426 9.68361C17.749 9.70974 17.7354 9.74773 17.7782 9.75655V10.2732C17.7308 10.2832 17.7387 10.3282 17.7296 10.358C17.5168 11.0509 17.0078 11.4323 16.2769 11.4262C16.1359 11.4262 16.1061 11.4645 16.1064 11.6001C16.111 13.1074 16.1137 14.6151 16.1064 16.1225C16.1031 16.8902 15.5861 17.5296 14.8437 17.7259C14.7811 17.7445 14.7036 17.7183 14.6507 17.7782Z" fill="#97959E" />
                    <title>Tenant Master</title>
                </svg>
            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Tenant Master</span>
            }
        </div>
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo($"add-tenant"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.69922 6.75358C4.69853 5.85774 4.9586 4.98184 5.44651 4.23672C5.93441 3.49161 6.62823 2.91077 7.44014 2.56774C8.25204 2.2247 9.14554 2.13488 10.0075 2.30965C10.8696 2.48442 11.6613 2.91592 12.2826 3.54953C12.904 4.18315 13.3269 4.9904 13.4979 5.86911C13.6689 6.74782 13.5803 7.65849 13.2433 8.48586C12.9064 9.31323 12.3362 10.0201 11.6049 10.517C10.8736 11.014 10.0141 11.2786 9.1352 11.2774C8.55246 11.2774 7.97546 11.1604 7.43711 10.933C6.89876 10.7056 6.40964 10.3724 5.99769 9.95226C5.58574 9.53216 5.25903 9.03343 5.03624 8.48459C4.81345 7.93575 4.69892 7.34754 4.69922 6.75358Z" fill="#97959E" />
                    <path d="M12.6519 11.9129C12.1136 12.4356 11.7432 13.1123 11.5896 13.8539C11.4639 14.4377 11.4878 15.0447 11.6592 15.6163C11.7137 15.7937 11.6592 15.8078 11.5131 15.8078C8.4645 15.8078 5.41596 15.8078 2.36741 15.8078C2.18185 15.8078 2.17489 15.7334 2.18764 15.5868C2.28738 14.5229 2.83362 13.7215 3.6118 13.0607C4.46652 12.3337 5.46737 11.8951 6.53316 11.6067C6.6173 11.5914 6.70399 11.6105 6.77439 11.6599C8.35859 12.4141 9.94162 12.4164 11.5212 11.6469C11.5649 11.6236 11.6128 11.6094 11.662 11.6054C11.7113 11.6013 11.7608 11.6074 11.8076 11.6233C12.0949 11.7013 12.3769 11.798 12.6519 11.9129Z" fill="#97959E" />
                    <path d="M15.1297 17.3285C14.5983 17.328 14.079 17.1668 13.6376 16.8653C13.1961 16.5638 12.8524 16.1356 12.6499 15.6348C12.4474 15.1341 12.3952 14.5833 12.5 14.0523C12.6048 13.5213 12.8618 13.034 13.2386 12.652C13.6153 12.27 14.0948 12.0105 14.6163 11.9064C15.1377 11.8024 15.6778 11.8584 16.168 12.0674C16.6583 12.2764 17.0767 12.629 17.3702 13.0805C17.6637 13.532 17.8191 14.0622 17.8168 14.6038C17.8141 15.328 17.5296 16.0216 17.0259 16.5324C16.5222 17.0431 15.8403 17.3295 15.1297 17.3285ZM15.8546 14.9383C15.9404 14.9383 16.0274 14.9383 16.1132 14.9383C16.1579 14.9402 16.2025 14.933 16.2444 14.9169C16.2864 14.9009 16.3247 14.8764 16.3572 14.845C16.3896 14.8136 16.4155 14.7758 16.4334 14.734C16.4512 14.6921 16.4607 14.6471 16.4611 14.6014C16.4616 14.5554 16.4529 14.5097 16.4354 14.4672C16.4179 14.4247 16.3921 14.3863 16.3595 14.3544C16.3269 14.3225 16.2882 14.2977 16.2458 14.2817C16.2035 14.2656 16.1583 14.2586 16.1132 14.261C15.9404 14.2527 15.7653 14.2539 15.5948 14.261C15.4927 14.261 15.4533 14.2279 15.4567 14.1215C15.4625 13.9454 15.4637 13.7669 15.4567 13.5931C15.4567 13.5487 15.4482 13.5048 15.4315 13.4638C15.4149 13.4227 15.3904 13.3855 15.3596 13.3541C15.3288 13.3227 15.2923 13.2978 15.252 13.2808C15.2118 13.2638 15.1686 13.2551 15.1251 13.2551C15.0815 13.2551 15.0384 13.2638 14.9981 13.2808C14.9579 13.2978 14.9213 13.3227 14.8905 13.3541C14.8597 13.3855 14.8353 13.4227 14.8186 13.4638C14.802 13.5048 14.7934 13.5487 14.7934 13.5931C14.7934 13.761 14.7841 13.9312 14.7934 14.0991C14.8015 14.2279 14.7528 14.2634 14.6345 14.2575C14.4756 14.2492 14.3167 14.2515 14.159 14.2575C13.9271 14.2669 13.7925 14.3922 13.7937 14.6003C13.7949 14.8083 13.9329 14.9336 14.1625 14.9372C14.3283 14.9372 14.4942 14.9431 14.6589 14.9372C14.7632 14.9372 14.798 14.9667 14.7934 15.0731C14.7864 15.2563 14.7864 15.4407 14.7934 15.6239C14.7984 15.71 14.8354 15.7909 14.897 15.85C14.9585 15.9091 15.0399 15.942 15.1245 15.942C15.2091 15.942 15.2904 15.9091 15.352 15.85C15.4135 15.7909 15.4506 15.71 15.4556 15.6239C15.4625 15.5294 15.4556 15.4336 15.4556 15.3379C15.4649 14.9383 15.4649 14.9383 15.8546 14.9383Z" fill="#97959E" />
                    <title>Onboard New Tenant</title>
                </svg>

            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Onboard New Tenant</span>
            }
        </div>
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo($"InvoiceManagement"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.00139 10.2372C4.00139 8.33763 4.00139 6.43771 4.00139 4.53747C4.00139 3.785 4.46878 3.1854 5.16285 3.02781C5.28512 3.00529 5.40934 2.99641 5.53341 3.00131C8.84448 3.00131 12.1557 3.00131 15.4671 3.00131C16.3937 3.00131 17 3.6383 17 4.61034C17 8.39253 17 12.1749 17 15.9574C17 16.8518 16.3317 17.5517 15.5069 17.5281C15.3323 17.519 15.1604 17.4793 14.9983 17.4107L13.8477 16.9578C13.507 16.8244 13.1831 16.8561 12.8523 17.0241C12.2714 17.3198 11.6841 17.6043 11.0905 17.8717C10.6778 18.0572 10.2543 18.0368 9.84305 17.8414C9.32122 17.5937 8.79999 17.3441 8.27936 17.0927C7.86039 16.8901 7.43146 16.8778 6.99394 17.0099C6.54148 17.1471 6.1035 17.3468 5.63657 17.4296C4.80133 17.5782 4.00365 16.8617 4.00229 15.973C3.99958 14.0611 3.99927 12.1492 4.00139 10.2372ZM10.4765 9.1961H7.00797C6.92897 9.19467 6.84996 9.19799 6.77133 9.20604C6.6666 9.21509 6.56748 9.25935 6.48878 9.33219C6.41008 9.40504 6.35603 9.50256 6.3347 9.61019C6.22974 10.064 6.4976 10.4048 6.96453 10.4052C9.32213 10.4052 11.6797 10.4052 14.0373 10.4052C14.0992 10.4062 14.161 10.401 14.2219 10.3896C14.321 10.3723 14.4137 10.3272 14.4903 10.2592C14.5669 10.1912 14.6244 10.1028 14.6569 10.0033C14.6893 9.90391 14.6955 9.79715 14.6747 9.69437C14.6539 9.59158 14.6069 9.4966 14.5386 9.41947C14.4029 9.25715 14.2346 9.19374 14.0287 9.19421C12.8456 9.19705 11.661 9.19516 10.4765 9.19468V9.1961ZM10.5086 7.37884H13.9776C14.051 7.38098 14.1244 7.37813 14.1975 7.37032C14.2746 7.36037 14.349 7.3338 14.4158 7.2923C14.4826 7.25079 14.5405 7.19524 14.5858 7.12916C14.6311 7.06309 14.6628 6.98791 14.6789 6.90837C14.695 6.82884 14.6951 6.74668 14.6794 6.66707C14.6187 6.34101 14.3943 6.17016 14.0201 6.16969C11.6737 6.16969 9.32725 6.16969 6.98081 6.16969C6.94128 6.1685 6.90172 6.16945 6.86228 6.17253C6.7331 6.19009 6.61215 6.24856 6.51567 6.34009C6.41918 6.43162 6.35177 6.55183 6.32249 6.68458C6.27724 6.90086 6.40167 7.15168 6.61568 7.29318C6.73196 7.36984 6.85774 7.37978 6.98895 7.37978C8.16232 7.37757 9.33555 7.37678 10.5086 7.37742V7.37884ZM8.71869 12.2173C8.12643 12.2173 7.53417 12.2173 6.94191 12.2173C6.56456 12.2173 6.31299 12.461 6.30847 12.8221C6.30576 13.2036 6.52881 13.4222 6.92788 13.4227C8.10125 13.4227 9.27462 13.4227 10.448 13.4227C10.8276 13.4227 11.0742 13.1827 11.0755 12.8179C11.0755 12.4293 10.8615 12.2178 10.4598 12.2168C9.88001 12.2153 9.29965 12.2149 8.71869 12.2159V12.2173Z" fill="#97959E" />
                    <title>Invoice</title>
                </svg>

            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Invoice</span>
            }
        </div>
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo($"vendor"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1.11133 7.8476C1.28501 7.54999 1.55837 7.49978 1.88802 7.50416C3.00443 7.51831 4.12084 7.51225 5.23725 7.50618C5.37619 7.50618 5.44045 7.54898 5.49777 7.66729C5.69889 8.08219 5.91252 8.4917 6.12301 8.90289C6.34984 9.34914 6.717 9.56889 7.23249 9.56822C9.4594 9.56586 11.6867 9.56822 13.9136 9.5635C14.0873 9.5635 14.139 9.61068 14.1387 9.78157C14.1331 12.0211 14.1318 14.2607 14.1349 16.5002C14.1349 16.9222 13.9379 17.1099 13.4957 17.1099C9.60274 17.1099 5.70966 17.1115 1.81646 17.1146C1.49411 17.1146 1.25686 17.028 1.11271 16.7439L1.11133 7.8476ZM12.6937 15.3088H12.6857C12.6857 15.236 12.6919 15.1628 12.6857 15.0907C12.6768 14.9678 12.6217 14.8524 12.5308 14.7664C12.44 14.6804 12.3198 14.6298 12.193 14.6242C12.0662 14.6186 11.9417 14.6583 11.8432 14.736C11.7446 14.8136 11.6789 14.9236 11.6585 15.0452C11.6271 15.2164 11.6278 15.3916 11.6606 15.5626C11.6842 15.6801 11.7502 15.7857 11.8469 15.8602C11.9436 15.9347 12.0645 15.9732 12.1878 15.9689C12.3112 15.9645 12.4289 15.9175 12.5197 15.8364C12.6106 15.7553 12.6685 15.6454 12.6832 15.5265C12.6912 15.4554 12.6902 15.3822 12.6937 15.3098V15.3088ZM10.9676 15.2916H10.9746C10.9746 15.2467 10.9746 15.2019 10.9746 15.1567C10.9694 14.8507 10.7446 14.6222 10.4511 14.6235C10.1576 14.6249 9.93493 14.8561 9.93007 15.1628C9.93007 15.269 9.92625 15.3755 9.93007 15.4817C9.93352 15.6065 9.98445 15.7256 10.073 15.8162C10.1616 15.9067 10.2816 15.9623 10.4099 15.9721C10.5382 15.9819 10.6656 15.9454 10.7678 15.8694C10.8699 15.7935 10.9395 15.6836 10.9631 15.5609C10.9808 15.4736 10.9676 15.3819 10.9676 15.2926V15.2916Z" fill="#97959E" />
                    <path d="M18.8898 12.129C18.7467 12.4152 18.5112 12.5287 18.1875 12.4998C18.0256 12.4881 17.863 12.4881 17.7012 12.4998C17.5876 12.5065 17.557 12.4728 17.5577 12.3619C17.5626 11.5591 17.5605 10.7562 17.5605 9.95339C17.5605 9.12201 17.5605 8.29063 17.5605 7.45926C17.5605 6.69754 17.0957 6.2459 16.31 6.2459C14.1057 6.24455 11.9013 6.24556 9.69698 6.24894C9.5424 6.24894 9.46147 6.20748 9.39512 6.06761C9.19991 5.65641 8.98594 5.25331 8.7796 4.84649C8.55591 4.40497 8.18909 4.1869 7.68369 4.18454C7.12791 4.18184 6.57214 4.18016 6.01636 4.1869C5.89583 4.1869 5.85796 4.16061 5.86387 4.04096C5.87464 3.82795 5.86387 3.61426 5.86804 3.40057C5.87394 3.1151 6.09104 2.89703 6.38456 2.89501C7.60552 2.8913 8.82615 2.89501 10.0468 2.89062C10.1572 2.89062 10.1975 2.94489 10.2375 3.02544C10.4459 3.4427 10.6609 3.85794 10.8697 4.27588C11.0968 4.73021 11.4657 4.95367 11.9885 4.953C14.0727 4.94996 16.154 4.95536 18.2368 4.94828C18.5598 4.94828 18.7762 5.0595 18.8888 5.35273L18.8898 12.129Z" fill="#97959E" />
                    <path d="M16.5128 10.8592C16.5128 11.9652 16.5128 13.0711 16.5128 14.1771C16.5128 14.6173 16.3249 14.7986 15.8702 14.7999C15.7083 14.7999 15.5454 14.7891 15.3839 14.803C15.2224 14.8168 15.1706 14.7773 15.1723 14.6075C15.1817 13.5744 15.1769 12.5417 15.1769 11.5087C15.1769 10.9081 15.1769 10.3071 15.1769 9.70652C15.1744 9.01827 14.6992 8.55551 13.991 8.55517C11.7633 8.55382 9.53576 8.55483 7.30849 8.5582C7.15878 8.5582 7.08271 8.51304 7.01984 8.38362C6.81871 7.96838 6.603 7.55921 6.39459 7.14767C6.17367 6.71244 5.81335 6.49527 5.31361 6.49617C4.75228 6.49617 4.19095 6.49313 3.62997 6.4992C3.51429 6.4992 3.48025 6.46819 3.48476 6.35865C3.49275 6.15643 3.48477 5.9542 3.48789 5.75197C3.4938 5.41088 3.69874 5.2036 4.04957 5.20226C5.25282 5.19821 6.45643 5.20225 7.65968 5.19922C7.76388 5.19922 7.81251 5.23865 7.85558 5.32629C8.04976 5.71995 8.26304 6.1052 8.44609 6.50291C8.69341 7.03611 9.10608 7.27002 9.70562 7.26733C11.894 7.25789 14.0796 7.26362 16.2665 7.26362C16.5097 7.26362 16.5097 7.26362 16.5097 7.50764L16.5128 10.8592Z" fill="#97959E" />
                    <title>Vendor Data</title>
                </svg>

            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Vendor Data</span>
            }
        </div>
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo($"pending-payment"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.888 14.9859V15.0561C18.8272 15.079 18.8532 15.1356 18.8448 15.1763C18.6125 16.3528 17.7133 17.1362 16.547 17.139C13.3266 17.1465 10.1061 17.1465 6.88567 17.139C5.6362 17.1353 4.60272 16.1605 4.57532 14.9461C4.54698 13.6826 4.56882 12.4139 4.5651 11.1475C4.5651 11.0511 4.59528 11.0347 4.68446 11.0436C5.04846 11.0764 5.41529 11.053 5.7723 10.9744C7.93356 10.5223 9.36417 8.79874 9.42409 6.57538C9.42734 6.4537 9.458 6.42141 9.57876 6.42188C11.8952 6.42516 14.212 6.42188 16.5289 6.42609C17.6316 6.42609 18.5401 7.14631 18.8049 8.20113C18.8337 8.31579 18.8234 8.43933 18.8875 8.5451V8.5802C18.8587 8.60032 18.8653 8.63027 18.8653 8.65882C18.8671 10.6668 18.8693 12.6749 18.8717 14.6831C18.8727 14.7818 18.8388 14.8867 18.888 14.9859Z" fill="#97959E" />
                    <path d="M4.94854 2.67586C7.08517 2.68943 8.79076 4.41767 8.77683 6.55212C8.77635 7.06022 8.67613 7.56321 8.48193 8.03213C8.28773 8.50105 8.0034 8.92664 7.64529 9.28439C7.28718 9.64215 6.86238 9.92501 6.39533 10.1167C5.92828 10.3084 5.42821 10.4051 4.92392 10.4012C2.80307 10.3867 1.09794 8.65381 1.11141 6.52732C1.12534 4.38819 2.84395 2.66275 4.94854 2.67586ZM4.62339 5.38311C4.62339 5.75048 4.62339 6.11737 4.62339 6.48473C4.62339 6.63589 4.66752 6.75616 4.80686 6.83291C5.29241 7.10153 5.77702 7.37139 6.26071 7.64251C6.29706 7.66655 6.33779 7.68307 6.38053 7.6911C6.42327 7.69913 6.46716 7.69852 6.50967 7.68931C6.57468 7.67413 6.63348 7.63916 6.67809 7.58915C6.7227 7.53914 6.75095 7.47652 6.75901 7.40978C6.76708 7.34303 6.75456 7.2754 6.72317 7.21607C6.69177 7.15674 6.64302 7.10858 6.58353 7.07813C6.19057 6.85771 5.79855 6.63495 5.40141 6.42296C5.35713 6.4052 5.31988 6.37328 5.29537 6.3321C5.27086 6.29092 5.26046 6.24277 5.26577 6.19506C5.27227 5.57171 5.26903 4.94836 5.2681 4.32314C5.2716 4.25933 5.26707 4.19533 5.25462 4.13267C5.23339 4.05935 5.18674 3.99615 5.12314 3.95458C5.05954 3.913 4.98323 3.89583 4.90812 3.90617C4.82931 3.91298 4.75621 3.95036 4.70422 4.01043C4.65222 4.07049 4.62541 4.14855 4.62943 4.22814C4.61874 4.61376 4.62339 4.99844 4.62339 5.38311Z" fill="#97959E" />
                    <path d="M8.95508 4.51192C9.37312 4.45342 9.77211 4.39633 10.1725 4.34111L13.8847 3.82633C14.4119 3.75333 14.9381 3.67471 15.4662 3.60873C16.1444 3.52402 16.5982 3.93163 16.6005 4.61815C16.6005 4.95089 16.5963 5.28315 16.6033 5.61542C16.6056 5.7296 16.5871 5.77546 16.457 5.77499C14.1346 5.77094 11.8129 5.76985 9.49202 5.77172C9.43256 5.77172 9.36615 5.79325 9.34803 5.69544C9.26951 5.28518 9.13737 4.88721 8.95508 4.51192Z" fill="#97959E" />
                    <title>Pending Payment</title>
                </svg>

            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Pending Payment</span>
            }
        </div>
        <div class="sidebar-item" @onclick="@(()=> Navigation.NavigateTo($"activity"))">
            <span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.2188 14.2929C7.84509 14.2835 5.92426 12.3519 5.94152 9.99158C5.95925 7.61962 7.87867 5.70806 10.2328 5.7188C12.6005 5.72953 14.5209 7.66818 14.5055 10.0313C14.4891 12.385 12.5613 14.3022 10.2188 14.2929ZM9.47811 8.89246C9.47811 9.18895 9.48185 9.48591 9.47811 9.7824C9.47539 9.86957 9.49129 9.95633 9.52476 10.0368C9.55822 10.1174 9.60847 10.1898 9.67215 10.2493C10.1504 10.7253 10.6271 11.2022 11.1023 11.68C11.162 11.7435 11.2343 11.7939 11.3145 11.828C11.4338 11.8772 11.5666 11.8834 11.6899 11.8454C11.8133 11.8074 11.9196 11.7276 11.9907 11.6197C12.0617 11.5119 12.093 11.3826 12.0793 11.2541C12.0655 11.1257 12.0076 11.006 11.9153 10.9156C11.511 10.5047 11.1043 10.0979 10.6951 9.69509C10.6612 9.6646 10.6346 9.62695 10.6171 9.58487C10.5996 9.5428 10.5916 9.49735 10.5939 9.45183C10.5991 8.96343 10.5991 8.47502 10.5939 7.98662C10.5911 7.64904 10.3476 7.39691 10.0341 7.39691C9.72067 7.39691 9.48091 7.64624 9.47811 7.98616C9.47811 8.28779 9.47811 8.59036 9.47811 8.89246Z" fill="#97959E" />
                    <path d="M5.02984 3.97974C4.88431 3.97974 4.76303 3.98395 4.64222 3.97974C4.32271 3.96667 4.08249 3.73228 4.07782 3.43111C4.07602 3.35801 4.08882 3.28528 4.11545 3.21719C4.14209 3.1491 4.18203 3.087 4.23295 3.03456C4.28386 2.98212 4.34472 2.94038 4.41195 2.91178C4.47919 2.88318 4.55144 2.8683 4.6245 2.86801C5.25233 2.8565 5.88033 2.8565 6.50847 2.86801C6.83498 2.87408 7.04582 3.10567 7.04862 3.43298C7.0539 4.04434 7.0539 4.65554 7.04862 5.26658C7.04862 5.59576 6.80327 5.84137 6.49588 5.8423C6.18849 5.84323 5.94734 5.59716 5.93614 5.26939C5.92961 5.09663 5.93614 4.9234 5.93614 4.71888C5.62883 4.95042 5.34454 5.21108 5.08721 5.49724C1.90325 9.01829 3.24428 14.5812 7.68345 16.2598C8.42569 16.5345 9.21033 16.6768 10.0017 16.68C10.4215 16.6851 10.6878 17.0932 10.519 17.4602C10.4103 17.6937 10.2158 17.8002 9.95924 17.796C8.72642 17.7768 7.55378 17.5088 6.46089 16.9312C4.39081 15.8372 3.00407 14.1647 2.46905 11.8885C1.75212 8.83667 2.60898 6.2303 4.91836 4.10721C4.94401 4.0834 4.9706 4.06052 4.99579 4.03671C5.00839 4.0185 5.01977 3.99947 5.02984 3.97974Z" fill="#97959E" />
                    <path d="M15.6782 15.3483C15.7841 15.3633 15.8648 15.3675 15.9441 15.3866C16.0753 15.4185 16.1902 15.4975 16.267 15.6086C16.3438 15.7198 16.3771 15.8553 16.3606 15.9894C16.3473 16.121 16.2865 16.2432 16.1895 16.3331C16.0925 16.4229 15.9661 16.4742 15.834 16.4773C15.2002 16.4832 14.5663 16.4832 13.9323 16.4773C13.7908 16.4773 13.655 16.4215 13.5544 16.3219C13.4538 16.2224 13.3965 16.0871 13.3949 15.9455C13.3884 15.3108 13.3884 14.6763 13.3949 14.0419C13.3987 13.8974 13.4591 13.7602 13.563 13.6598C13.6669 13.5594 13.8061 13.504 13.9505 13.5054C14.0946 13.506 14.2329 13.5622 14.3366 13.6623C14.4403 13.7624 14.5015 13.8987 14.5074 14.0428C14.5139 14.188 14.5074 14.3337 14.5074 14.4794V14.922C14.8997 14.5703 15.2461 14.1704 15.5383 13.7318C16.191 12.7599 16.5771 11.6332 16.6578 10.4648C16.7384 9.29642 16.5108 8.12733 15.9977 7.07476C15.4846 6.02219 14.7042 5.12313 13.7345 4.46772C12.7649 3.8123 11.6402 3.42357 10.4731 3.34049C10.1331 3.31388 9.89239 3.06035 9.91338 2.75171C9.93531 2.42487 10.2054 2.20261 10.5487 2.22409C13.9491 2.43327 16.9213 4.98826 17.6102 8.33608C18.142 10.9205 17.5234 13.2192 15.7953 15.2157C15.7621 15.2545 15.73 15.2923 15.6782 15.3483Z" fill="#97959E" />
                    <title>Activity</title>
                </svg>

            </span>
            @if (sidebarExpanded)
            {
                <span class="header-text">Activity</span>
            }
        </div>
    </div>

    <!-- Footer (Version & Collapse Button) -->
    <div class="version-arrow-container">
        <div class="version">
            @if (sidebarExpanded)
            {
                <span>@($"V{appVersion}")</span>
            }
        </div>
        <div class="arrow-icon">
            @if (sidebarExpanded)
            {
                <span @onclick="ToggleSidebar">
                    <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.9374 10.0018C23.1852 10.0013 23.4254 10.0825 23.6163 10.2313C23.7237 10.3151 23.8125 10.4179 23.8775 10.534C23.9426 10.6501 23.9827 10.7771 23.9955 10.9078C24.0083 11.0385 23.9936 11.1703 23.9522 11.2956C23.9108 11.4209 23.8435 11.5374 23.7542 11.6382L19.002 16.9863L23.5844 22.3444C23.6725 22.4465 23.7383 22.5639 23.7781 22.69C23.8178 22.8161 23.8306 22.9482 23.8158 23.079C23.801 23.2097 23.7589 23.3364 23.6919 23.4518C23.6249 23.5672 23.5343 23.669 23.4253 23.7513C23.3156 23.8421 23.187 23.9107 23.0478 23.9526C22.9086 23.9945 22.7616 24.0088 22.6162 23.9948C22.4708 23.9807 22.33 23.9385 22.2027 23.8709C22.0755 23.8032 21.9644 23.7116 21.8766 23.6016L16.7532 17.6149C16.5971 17.4364 16.5118 17.2124 16.5118 16.9813C16.5118 16.7502 16.5971 16.5263 16.7532 16.3477L22.0569 10.361C22.1634 10.2403 22.2985 10.1448 22.4515 10.0824C22.6045 10.02 22.7709 9.99238 22.9374 10.0018Z" fill="#97959E" />
                        <path d="M16.4255 10.0018C16.6734 10.0013 16.9136 10.0825 17.1044 10.2313C17.2118 10.3151 17.3006 10.4179 17.3657 10.534C17.4308 10.6501 17.4709 10.7771 17.4837 10.9078C17.4965 11.0385 17.4818 11.1703 17.4403 11.2956C17.3989 11.4209 17.3316 11.5374 17.2423 11.6382L12.4901 16.9863L17.0726 22.3444C17.1607 22.4465 17.2265 22.5639 17.2662 22.69C17.3059 22.8161 17.3187 22.9482 17.304 23.079C17.2892 23.2097 17.2471 23.3364 17.1801 23.4518C17.113 23.5672 17.0224 23.669 16.9135 23.7513C16.8037 23.8421 16.6752 23.9107 16.536 23.9526C16.3967 23.9945 16.2498 24.0088 16.1044 23.9948C15.9589 23.9807 15.8182 23.9385 15.6909 23.8709C15.5636 23.8032 15.4526 23.7116 15.3648 23.6016L10.2413 17.6149C10.0853 17.4364 10 17.2124 10 16.9813C10 16.7502 10.0853 16.5263 10.2413 16.3477L15.5451 10.361C15.6515 10.2403 15.7867 10.1448 15.9397 10.0824C16.0926 10.02 16.2591 9.99238 16.4255 10.0018Z" fill="#97959E" />
                        <title>Hide Navigation Bar</title>
                    </svg>

                </span>
            }
            else
            {
                <span @onclick="ToggleSidebar">
                    <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g transform="scale(-1,1) translate(-34,0)">
                            <path d="M22.9374 10.0018C23.1852 10.0013 23.4254 10.0825 23.6163 10.2313C23.7237 10.3151 23.8125 10.4179 23.8775 10.534C23.9426 10.6501 23.9827 10.7771 23.9955 10.9078C24.0083 11.0385 23.9936 11.1703 23.9522 11.2956C23.9108 11.4209 23.8435 11.5374 23.7542 11.6382L19.002 16.9863L23.5844 22.3444C23.6725 22.4465 23.7383 22.5639 23.7781 22.69C23.8178 22.8161 23.8306 22.9482 23.8158 23.079C23.801 23.2097 23.7589 23.3364 23.6919 23.4518C23.6249 23.5672 23.5343 23.669 23.4253 23.7513C23.3156 23.8421 23.187 23.9107 23.0478 23.9526C22.9086 23.9945 22.7616 24.0088 22.6162 23.9948C22.4708 23.9807 22.33 23.9385 22.2027 23.8709C22.0755 23.8032 21.9644 23.7116 21.8766 23.6016L16.7532 17.6149C16.5971 17.4364 16.5118 17.2124 16.5118 16.9813C16.5118 16.7502 16.5971 16.5263 16.7532 16.3477L22.0569 10.361C22.1634 10.2403 22.2985 10.1448 22.4515 10.0824C22.6045 10.02 22.7709 9.99238 22.9374 10.0018Z" fill="#97959E" />
                            <path d="M16.4255 10.0018C16.6734 10.0013 16.9136 10.0825 17.1044 10.2313C17.2118 10.3151 17.3006 10.4179 17.3657 10.534C17.4308 10.6501 17.4709 10.7771 17.4837 10.9078C17.4965 11.0385 17.4818 11.1703 17.4403 11.2956C17.3989 11.4209 17.3316 11.5374 17.2423 11.6382L12.4901 16.9863L17.0726 22.3444C17.1607 22.4465 17.2265 22.5639 17.2662 22.69C17.3059 22.8161 17.3187 22.9482 17.304 23.079C17.2892 23.2097 17.2471 23.3364 17.1801 23.4518C17.113 23.5672 17.0224 23.669 16.9135 23.7513C16.8037 23.8421 16.6752 23.9107 16.536 23.9526C16.3967 23.9945 16.2498 24.0088 16.1044 23.9948C15.9589 23.9807 15.8182 23.9385 15.6909 23.8709C15.5636 23.8032 15.4526 23.7116 15.3648 23.6016L10.2413 17.6149C10.0853 17.4364 10 17.2124 10 16.9813C10 16.7502 10.0853 16.5263 10.2413 16.3477L15.5451 10.361C15.6515 10.2403 15.7867 10.1448 15.9397 10.0824C16.0926 10.02 16.2591 9.99238 16.4255 10.0018Z" fill="#97959E" />
                        </g>
                        <title>View Navigation Bar</title>
                    </svg>


                </span>
            }
        </div>
    </div>
</div>