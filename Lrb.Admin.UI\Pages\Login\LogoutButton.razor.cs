﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Pages.Login
{
    public partial class LogoutButton
    {
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public AuthenticationStateProvider Auth { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        public string? Token { get; set; }

        public async Task LogoutTenant()
        {
            Token = await LocalStorage.GetItemAsync<string>("token");
            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
            AdminUserActivity activityDto = new()
            {
                ActivityTime = DateTime.UtcNow,
                Activity = ActivityConst.Logout,
                AdminUserId = userActivityHistory.AdminUserId,
                TenantId = userActivityHistory.TenantId,
                Token = Token
            };
            await ActivityService.UserActivityAsync(activityDto);
            await LocalStorage.RemoveItemAsync("token");
            Navigation.NavigateTo("");
            await Auth.GetAuthenticationStateAsync();
        }
    }
}
