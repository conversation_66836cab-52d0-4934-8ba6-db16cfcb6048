﻿using Lrb.Admin.Data.Models.Subscription;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class SubscriptionsDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? TenantId { get; set; }
        public DateTime? LicenseCreated { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public double SoldPrice { get; set; }
        public int SoldLicenses { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public string? Payments { get; set; }
        public bool? IsDeleted { get; set; }
    }
}
