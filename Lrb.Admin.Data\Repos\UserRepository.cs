﻿using Dapper;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Lrb.Admin.Data.Repos
{
    public class UserRepository : IUserRepository
    {
        private readonly IDBConnectionFactory _dbFactory;

        public UserRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        public async Task<bool> CheckIfEmailAlreadyExists(string email, string readConnectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = $"Select \"Email\" from \"Identity\".\"Users\" where \"Email\" = '{email}'";
                var result = (await connection.QueryAsync<SubscriptionViewModel>(query)).FirstOrDefault();
                if(result == null)
                {
                    return false;
                }
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
            return true;
        }

        public async Task<bool> CheckIfUsersNameExists(string userName, string readConnectionString)
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var query = $"Select \"TenantId\" from \"Identity\".\"Users\" where \"UserName\" = '{userName}'";
            var details = await connection.QueryAsync<string>(query);
            if (details.Count() > 0)
            {
                return true;
            }
            return false;
        }

        public async Task<UserModelDto> GetAdminUserById(Guid id)
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var query = $"Select * from \"tenantmaster\".\"Users\" where \"Id\"='{id}'";
            var details = (await connection.QueryAsync<UserModelDto>(query)).FirstOrDefault();
            if(details != null)
            {
                return details;
            }
            else
            {
                return new();
            }
        }

        public async Task<string> GetDefaultCountryCode(string tenantId,string readConnectionString)
        {
            var connection = await _dbFactory.CreateReadConnectionAsync(readConnectionString);
            var query = $"SELECT \"CountryInfo\".\"DefaultCallingCode\" FROM \"LeadratBlack\".\"CountryInfo\" where \"TenantId\" = '{tenantId}' \r\nORDER BY \"LastModifiedOn\" Desc";
            var details = await connection.QueryAsync<string>(query);
            return details.FirstOrDefault();
        }

        public async Task<UserCountDto> GetUserCountsAsync (string tenantId, string ReadConnectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync(ReadConnectionString);
                var query = $"SELECT\r\n    \"TenantId\",\r\n    COUNT(*) FILTER (\r\n        WHERE NOT \"IsDeleted\" AND \"UserName\" !~~ '%.admin%'\r\n    ) AS TotalUsers,\r\n    COUNT(*) FILTER (\r\n        WHERE NOT \"IsDeleted\" AND \"IsActive\" AND \"UserName\" !~~ '%.admin%'\r\n    ) AS ActiveUsers,\r\n    COUNT(*) FILTER (\r\n        WHERE NOT \"IsDeleted\" AND NOT \"IsActive\" AND \"UserName\" !~~ '%.admin%'\r\n    ) AS InActiveUsers\r\nFROM \r\n    \"Identity\".\"Users\"\r\nWHERE \r\n    \"TenantId\" = '{tenantId}'\r\nGROUP BY \r\n    \"TenantId\";";
                var details = (await connection.QueryAsync<UserCountDto>(query)).FirstOrDefault();
                return details ?? new();
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
            
        }
    }
}
