﻿using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Tenant
{
    public class GetTenantUserModel
    {
        public string? Id { get; set; }
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? Name { get; set; }
        public string? UserName { get; set;}
        public string? Role { get; set; }
        public string? PhoneNumber {  get; set; }
        public int AssignedLeads { get; set; }
        public int AssignedProperties { get; set; }
        public bool IsActive {  get; set; }
        public string? ReportingTo { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string? Department { get; set; }
        public string? AppVersion { get; set; }
        public int LeadsCount { get; set; }
        public Platform Platform { get; set; }
        public string? Email {  get; set; }
    }


    public enum Platform
    {
        [Description("Android")]
        Android,
        [Description("iOS")]
        iOS
    }
}
