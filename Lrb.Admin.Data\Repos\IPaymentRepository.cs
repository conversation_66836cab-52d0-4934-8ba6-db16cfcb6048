﻿using Lrb.Admin.Data.Models.Invoice;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Wrappers;
using System.Threading.Tasks;
using SqlKata;

namespace Lrb.Admin.Data.Repos
{
    public interface IPaymentRepository
    {
        Task<Response<bool>> CreatePaymentAsync(LRBPayments model);
        Task<LRBPayments> GetPaymentDetailsById(Guid? paymentId);
        Task<LRBPayments> GetPaymentDetailsBySubscriptionId(Guid? subscriptionId);
        Task<int> UpdatePayment(LRBPayments dto);
        Task<Response<bool>> CreateInvoiceAsync(CreateInvoice model);

        Task<IEnumerable<PendingPaymentDetailDto>> GetPendingPayments();
        Task<PagedResponse<GetPendingPaymentModel, string>?> GetAllAsync(int pageNumber, int pageSize, PendingPaymentFilterParameter filter);
        Task<List<PendingPaymentViewModelExport>> GetPendingPaymentList(PendingPaymentFilterParameter filter);
        public Task<EmailEventDto> SendPaymentDueMails(); 
        Task<bool> DeletePayment(Guid PaymentId);
        public Task<IEnumerable<string>> SendAdminMails(string TenantId);
    }
}
