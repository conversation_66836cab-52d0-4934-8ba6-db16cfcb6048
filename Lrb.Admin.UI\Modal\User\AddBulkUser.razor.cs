﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.User
{
    public partial class AddBulkUser
    {
        [Inject]
        public IUserService UserService { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        private Response Response { get; set; }
        public bool IsFormatted { get; set; }
        public bool IsUploaded { get; set; }
        public bool IsTypeSelected { get; set; }
        public string TenantId { get; set; }
        public IBrowserFile File { get; set; }
        public string FileName { get; set; }
        public FileColumnsViewModel FileColumnModel { get; set; } = new();
        public GetMappedColumnsModel MappedColumnModel { get; set; } = new();
        public string? Token { get; set; }

        private async Task HandleFileChange(InputFileChangeEventArgs e)
        {
            File = e.File;
            FileName = e.File.Name;
        }
        private async Task UpLoadBulkUserAsync()
        {
            List<string> exts = new List<string>() { "xls", "xlsx", "csv" };
            if (File != null && exts.Contains(FileName.Split('.').LastOrDefault()))
            {
                if (IsFormatted)
                {
                    Response<bool> response = null;
                    try
                    {
                        response = await UserService.CreateBulkUserAsync(File, FileName);
                        if (response != null && response.Succeeded == true)
                        {
                            IsUploaded = response.Succeeded;
                            Token = await LocalStorage.GetItemAsync<string>("token");
                            var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                            AdminUserActivity adminUser = new()
                            {
                                AdminUserId = userActivityHistory.AdminUserId,
                                TenantId = TenantId,
                                ActivityTime = DateTime.UtcNow,
                                Activity = ActivityConst.BulkUser,
                                Token = Token
                            };
                            await ActivityService.UserActivityAsync(adminUser);
                        }
                        else
                        {
                            await JSRuntime.InvokeVoidAsync("alert", $"{response?.Message ?? "Something went wrong"}");
                        }
                    }
                    catch (Exception ex)
                    {
                        await JSRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
                    }
                }
            }
        }
    }
}
