﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Wrappers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface INotificationService
    {
        Task<Response<bool>> SendNotificationAsync(string tenantId,NotificationModelDto Model);
        Task<List<Guid>> GetAllUserIdsAsync(string tenantId);
        Task<Response<bool>> CaptureNotificationOfUserAsync(Notifications model);
        Task<NotificationDto> CheckDeviceTokenExpireAsync(string tenantId,Guid id);
        Task<PagedResponse<NotificationViewModel, string>> GetNotificationDetailsAsync(string tenantId , NotificationFilter filter);
    }
}
