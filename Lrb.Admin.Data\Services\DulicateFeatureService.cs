﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Lead;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class DulicateFeatureService : IDulicateFeatureService
    {
        private readonly IDuplicateFeatureRepo _duplicateFeatureRepo;

        public DulicateFeatureService(IDuplicateFeatureRepo duplicateFeatureRepo)
        {
            _duplicateFeatureRepo = duplicateFeatureRepo;
        }

        public async Task<PagedResponse<DuplicateFeatureViewModel, string>> GetAllDuplicateFeatureAsync(DuplicateFeatureFilter filter)
        {
            return await _duplicateFeatureRepo.GetAllDuplicateFeatureAsync(filter.PageNumber,filter.PageSize, filter);
        }

        public async Task<DuplicateFeatureViewModel> GetDuplicateFeatureById(string tenatId)
        {
            return await _duplicateFeatureRepo.GetDuplicateFeatureById(tenatId);
        }
    }
}
