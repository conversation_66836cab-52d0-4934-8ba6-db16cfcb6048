﻿using Lrb.Admin.Data.Filters;
using Microsoft.AspNetCore.Components;

namespace Lrb.Admin.UI.Pages.PendingPayment
{
    public partial class PendingPaymentFilter
    {
        [Parameter]
        public int PageNumber { get; set; }
        [Parameter]
        public int PageSize { get; set; }
        [Parameter]
        public List<int>? Count { get; set; }
        [Parameter]
        public PendingPaymentFilterParameter? Filter { get; set; } = new(); 
        public PendingPaymentFilterParameter? MailTemaplate { get; set;} = new();
        [Parameter]
        public Func<Task>? GetPendingPaymentsAsync { get; set; }
        private string? IsTestingAccount { get; set; }

        protected override void OnInitialized()
        {
            IsTestingAccount = Filter.IsTestingAccount.ToString();
        }

        private async Task CallGetPendingPaymentsAsync() 
        {
            if (IsTestingAccount == "True")
            {
                Filter.IsTestingAccount = true;
            }
            else if (IsTestingAccount == "False")
            {
                Filter.IsTestingAccount = false;
            }
            await GetPendingPaymentsAsync.Invoke(); 
            StateHasChanged(); 
        }

        public bool IsDateDisabled()
        {
            if(Filter.DateType == 0)
            {
                return true;
            }
            return false;
        }
        
        private async Task ResetFilter()
        {
            Filter.FromDate = default!;
            Filter.ToDate = default!;
            Filter.DateType = default!;
            Filter.PaymentFromDate = null;
            Filter.PaymentToDate = null;
            Filter.NoOfUsers = default!;
            Filter.BillingType = default!;
            Filter.PendingAmountFrom = default!;
            Filter.PendingAmountTo = default!;
            Filter.VendorNames = default!;
            Filter.LastInvoiceNumber = null;
            Filter.GSTNumber = null;
            Filter.IsTestingAccount = null;
            IsTestingAccount = "";

        }
    }
}
