﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Analytics;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    internal class AnalyticsService : IAnalyticsService
    {
        private readonly IHttpService _httpService;
        private readonly LrbApiEndPoints _apiEndPoints;
        private readonly LrbDomains _domains;
        private readonly GoDaddySettings _goDaddySetings;
        private readonly IDapperRepositoryAsync _dapperRepository;
        private readonly IConfiguration _config;
        public AnalyticsService(IHttpService httpService, IDapperRepositoryAsync dapperRepository, IOptions<LrbApiEndPoints> endPointsoptions, IOptions<LrbDomains> domainOptions, IOptions<GoDaddySettings> goDaddySetings)
        {
            _httpService = httpService;
            _apiEndPoints = endPointsoptions.Value;
            _domains = domainOptions.Value;
            _goDaddySetings = goDaddySetings.Value;
            _dapperRepository = dapperRepository;
        }
        public async Task<PagedResponse<AnalyticsRecord ,string>> GetAnalyticsRecordAsync(AnalyticsFilter filter)
        {
            try
            {
                string resource = $"api/v1/analytics?TenantId={filter.TenantId}";
                var response = await _httpService.GetAsync<PagedResponse<AnalyticsRecord, string>>(_apiEndPoints.WebApi, resource);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<AnalyticsRecord, string>> GetTodayRecordAsync(AnalyticsFilter filter)
        {
            try
            {
                filter.FromDate = DateTime.UtcNow.AddHours(-12);
                filter.ToDate = DateTime.UtcNow;
                string resource = $"api/v1/analytics?TenantId={filter.TenantId}&FromDate={filter.FromDate.Value.ToString("yyyyy/MM/dd")}" +
                    $"&ToDate={filter.ToDate.Value.ToString("yyyy/MM/dd")}";
                var response = await _httpService.GetAsync<PagedResponse<AnalyticsRecord, string>>(_apiEndPoints.WebApi, resource);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<PagedResponse<AnalyticsRecord, string>> GetYesterdayRecordAsync(AnalyticsFilter filter)
        {
            try
            {
                filter.FromDate = DateTime.UtcNow.AddDays(-1);
                filter.ToDate = DateTime.UtcNow;
                string resource = $"api/v1/analytics?TenantId={filter.TenantId}&FromDate={filter.FromDate.Value.ToString("yyyyy/MM/dd")}" +
                    $"&ToDate={filter.ToDate.Value.ToString("yyyy/MM/dd")}";
                var response = await _httpService.GetAsync<PagedResponse<AnalyticsRecord,string>>(_apiEndPoints.WebApi, resource);
                return response;
            }
            catch(Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<AnalyticsRecord, string>> GetLastWeekRecordAsync(AnalyticsFilter filter)
        {
            try
            {
                filter.FromDate = DateTime.UtcNow.AddDays(-7);
                filter.ToDate = DateTime.UtcNow;
                string resource = $"api/v1/analytics?TenantId={filter.TenantId}&FromDate={filter.FromDate.Value.ToString("yyyyy/MM/dd")}" +
                    $"&ToDate={filter.ToDate.Value.ToString("yyyy/MM/dd")}";
                var response = await _httpService.GetAsync<PagedResponse<AnalyticsRecord, string>>(_apiEndPoints.WebApi, resource);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<PagedResponse<AnalyticsRecord, string>> GetLastMonthRecordAsync(AnalyticsFilter filter)
        {
            try
            {
                filter.FromDate = DateTime.UtcNow.AddDays(-30);
                filter.ToDate = DateTime.UtcNow;
                string resource = $"api/v1/analytics?TenantId={filter.TenantId}&FromDate={filter.FromDate.Value.ToString("yyyyy/MM/dd")}" +
                    $"&ToDate={filter.ToDate.Value.ToString("yyyy/MM/dd")}";
                var response = await _httpService.GetAsync<PagedResponse<AnalyticsRecord, string>>(_apiEndPoints.WebApi, resource);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
