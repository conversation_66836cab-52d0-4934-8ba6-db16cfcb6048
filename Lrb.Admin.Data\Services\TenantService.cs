﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Common;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Settings;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.JSInterop;
using RestSharp;
using System.Diagnostics.Metrics;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Web.Helpers;
using System.Text.Json.Serialization;
using System.Text.Json;
using Newtonsoft.Json;
using Amazon.Runtime.Internal.Endpoints.StandardLibrary;
using System;


namespace Lrb.Admin.Data.Services
{
    public class TenantService : ITenantService
    {
        private readonly IJSRuntime _jsruntime;
        private readonly IHttpService _httpService;
        private readonly LrbApiEndPoints _apiEndPoints;
        private readonly LrbDomains _domains;
        private readonly GoDaddySettings _goDaddySetings;
        private readonly IDapperRepositoryAsync _dapperRepository;
        private readonly ITenantRepository _tenantRepository;
        private readonly IConfiguration _config;
        private readonly string _baseUri = "";
        private readonly string _baseUri1 = "https://localhost:7243/";

        public TenantService(IHttpService httpService, IDapperRepositoryAsync dapperRepository, IOptions<LrbApiEndPoints> endPointsoptions, IOptions<LrbDomains> domainOptions, IOptions<GoDaddySettings> goDaddySetings, ITenantRepository tenantRepository, IJSRuntime jsruntime)
        {
            _httpService = httpService;
            _apiEndPoints = endPointsoptions.Value;
            _domains = domainOptions.Value;
            _goDaddySetings = goDaddySetings.Value;
            _dapperRepository = dapperRepository;
            _tenantRepository = tenantRepository;
            _jsruntime = jsruntime;
        }

        public async Task<bool> CompareTenantIdAsync(string TenantId)
        {
            try
            {
                var response = await _tenantRepository.CompareTenantId(TenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> CreateTenantAsync(CreateTenantModel model)
        {
            try
            {
                var resource = $"api/tenants";
                Console.WriteLine($"Identity API Endpoint: {_apiEndPoints.IdentityApi}");
                Response<string>? response = await _httpService.PostAsync<CreateTenantModel, Response<string>>(_apiEndPoints.IdentityApi, resource, model);
                if (response?.Succeeded ?? false)
                {
                    await AddDNSRecord(model.Id);
                }
                return response?.Succeeded ?? false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<Response<bool>> CreateDifferentDatabseTenantAsync(string tenantId, string connectionString)
        {
            try
            {
                var responce = await _tenantRepository.CreateDifferentDatabseTenant(tenantId, connectionString);
                return responce;
            }
            catch (Exception ex)
            {
                return new Response<bool>(false);
            }
        }

        public async Task<string> UploadTDSCertificate(List<string> base64encodedFiles,string fileName = "test")
        {
            string bucketName = "";
            string folderName = "Tenant_TDSCertificates";
            string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "prd";
            string url = "";

            if (env == "Development")
            {
                bucketName = "dleadrat-black";
                url = $"https://lrb-dev-web.leadratd.com/api/blobstorage/doc/base64/anonymous/{bucketName}/{folderName}/{fileName}";
                //url = $"https://qa-lrb-webapi.leadrat.info/api/blobstorage/doc/base64/anonymous/{bucketName}/{folderName}/{fileName}";
            }
            else
            {
                bucketName = "leadrat-black";
                url = $"https://prd-lrb-webapi.leadrat.com/api/blobstorage/doc/base64/anonymous/{bucketName}/{folderName}/{fileName}";
            }
            

            try
            {
                RestClient client = new RestClient(url);
                var request = new RestRequest();
                request.Method = Method.Post;
                request.RequestFormat = DataFormat.Json;
                request.AddJsonBody(base64encodedFiles);
                var response = await client.ExecuteAsync(request);
                var result = response.Content;
                var data = System.Text.Json.JsonSerializer.Deserialize<ResponseData>(result) ?? new();
                var datatest = data.Data[0];
                return datatest;
                
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<bool> UpdateTenantCache(string tenantId)
        {
            string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "prd";
            try
            {
                if (env == "Development")
                {
                    return true;
                }
                else
                {
                    string url = $"https://connect.leadrat.com/api/v1/utility/update/tenantcache?tenantId={tenantId}";
                    RestClient client = new RestClient(url);
                    var request = new RestRequest();
                    request.Method = Method.Get;
                    request.AddHeader("accept", "application/json");

                    var response = await client.ExecuteAsync(request);
                    var result = response.Content;
                    if (string.IsNullOrWhiteSpace(result))
                        return false;

                    using var doc = JsonDocument.Parse(result);
                    var root = doc.RootElement;

                    if (root.TryGetProperty("data", out var dataElement))
                    {
                        return dataElement.GetBoolean();
                    }
                    return false;
                }
                    
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public async Task<LrbTenantInfo?> GetByIdAsync(string tenantId)
        {
            try
            {
                var resource = $"api/tenants/{tenantId}";
                Response<LrbTenantInfo> response = await _httpService.GetAsync<Response<LrbTenantInfo>>(_apiEndPoints.IdentityApi, resource);
                return response?.Data;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private async Task<bool> AddDNSRecord(string tenantName)
        {
            try
            {
                var resource = $"domains/{_domains.DomainName}/records";
                var payload = new List<AddDNSRecordRequest>()
                {
                    new AddDNSRecordRequest
                    {
                        name = tenantName,
                        data = _domains.DomainValue,
                        ttl = _domains.Ttl,
                        type = _domains.Type,
                    }
                };
                RestClient restClient = new RestClient(_goDaddySetings.BaseUrl);
                var request = new RestRequest(resource);
                request.AddJsonBody(payload);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Authorization", $"sso-key {_goDaddySetings.ApiKey}:{_goDaddySetings.Secret}");
                var response = await restClient.PatchAsync(request);
                return response.StatusCode == System.Net.HttpStatusCode.OK;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }

        //var contentLength = 0;
        //ASCIIEncoding encoding = new ASCIIEncoding();
        //byte[] bytes = encoding.GetBytes(JsonConvert.SerializeObject(payload));
        //var headers = new Dictionary<string, string>
        //{
        //    //{"Content-Length", $"{bytes.Length}" },
        //    { "Authorization", $"sso-key {_goDaddySetings.ApiKey}:{_goDaddySetings.Secret}" }
        //};
        //var response = await _httpService.PatchAsync<List<AddDNSRecordRequest>, AddDNSRecordResponse>(_goDaddySetings.BaseUrl, resource, headers);


        /*public async Task<PagedResponse<GetTenantModel ,string>> GetTenantCityAsync(string city,GetAllTenantParameter filter)
        {
            try
            {
                string resource = $"api/Tenant/Location?PageNumber={filter.PageNumber}&PageSize{filter.PageSize}" +
                    $"&TenantId={filter.City}";
                var response = await _httpService.GetAsync<PagedResponse<GetTenantModel, string>>(_baseUri, resource);
                return response;
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }*/

        public async Task<PagedResponse<GetTenantLeadsModel, string>> GetTenantLeadsAsync(string tenantId, GetAllTenantParameter filter)
        {
            try
            {

                var response = await _tenantRepository.GetTenantLeadAsync(tenantId, filter.PageNumber, filter.PageSize, filter);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }

        public async Task<PagedResponse<GetTenantIntegrationModel, string>> GetTenantIntegrationAsync(string tenantId, GetAllTenantParameter filter, string? readConnectionString)
        {
            try
            {
                var resource = await _tenantRepository.GetTenantIntegrationInfo(tenantId, filter.PageNumber, filter.PageSize, filter,readConnectionString);
                return resource;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<GetTenantSubscriptionModel, string>> GetTenantSubscriptionAsync(string tenantId, GetAllTenantParameter filter)
        {
            try
            {
                string resource = $"api/Tenant/Subscription?PageNumber={filter.PageNumber}&PageSize={filter.PageSize}" +
                    $"&TenantId={tenantId}";
                var response = await _httpService.GetAsync<PagedResponse<GetTenantSubscriptionModel, string>>(_baseUri, resource);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<GetTenantModel> GetTenantByIdAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.GetTenantByIdAsync(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<GetTenantLeadsModel> GetTenantLeadsByIdAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.GetLeadByIdAsync(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Address> GetTenantAddressAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.GetAddressAsync(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<GetTenantUserModel> GetTenantUserByIdAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.GetUserByIdAsync(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> UpdateTenantStatusAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.UpdateTenantStatusAsync(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<TenantProfile> GetTenantWebsiteAsync(string tenantId)
        {
            try
            {
                var response = await _tenantRepository.GetTenantWebsite(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<string>> GetAnalyticsTenantId()
        {
            try
            {
                var response = await _tenantRepository.GetTenantId();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> GetAdminCountAsync(string tenantId, string readConnectionString)
        {
            try
            {
                var response = await _tenantRepository.CountAdminAsync(tenantId, readConnectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTenantValidity(string tenantId, UpdateTenantValidityDto dto)
        {
            try
            {
                var resource = $"api/tenants/{tenantId}/upgrade";
                Console.WriteLine($"Identity API Endpoint: {_apiEndPoints.IdentityApi}");
                Response<string>? response = await _httpService.PostAsync<UpdateTenantValidityDto,Response<string>>(_apiEndPoints.IdentityApi, resource, dto);
                string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "prd";
                if (env == "prd")
                {
                    var idResource = $"https://identity-api-prd.azurewebsites.net/api/tenants/tenantcacheupdate";
                    RestClient client2 = new RestClient(idResource);
                    var idRequest = new RestRequest();
                    idRequest.Method = Method.Get;
                    idRequest.RequestFormat = DataFormat.Json;
                    var idResponse = await client2.ExecuteAsync(idRequest);
                    await _jsruntime.InvokeVoidAsync("console.log", $"Cache Update of Web {idResponse.StatusCode}");

                    var mobileResource = $"https://prd-mobile.leadrat.com/api/v1/utility/tenantcacheupdate";
                    RestClient client = new RestClient(mobileResource);
                    var mobileRequest = new RestRequest();
                    mobileRequest.Method = Method.Get;
                    mobileRequest.RequestFormat = DataFormat.Json;
                    var mobileResponse = await client.ExecuteAsync(mobileRequest);
                    await _jsruntime.InvokeVoidAsync("console.log", $"Cache Update of mobile {mobileResponse.StatusCode}");

                    var webResource = $"https://prd-lrb-webapi.leadrat.com/api/v1/utility/tenantcacheupdate";
                    RestClient client1 = new RestClient(webResource);
                    var webRequest = new RestRequest();
                    webRequest.Method = Method.Get;
                    webRequest.RequestFormat = DataFormat.Json;
                    var webResponse = await client1.ExecuteAsync(webRequest);
                    await _jsruntime.InvokeVoidAsync("console.log", $"Cache Update of Web {webResponse.StatusCode}");
                }
                return response?.Succeeded ?? false;

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<TenantViewModelExport>> GetExportAllTenantAsync(GetAllTenantParameter filter)
        {
            try
            {
                var response = await _tenantRepository.GetTenantExport(filter);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<TenantCompleteDetailsDto>> GetCompleteTenantData()
        {
            try
            {
                var response = await _tenantRepository.GetCompleteTenantData();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


        public async Task<GetTenantUserModel> GetUserfromIdAsync(string id)
        {
            try
            {
                var response = await _tenantRepository.GetUserFromIdAsync(id);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<string>> GetTenantIdsAsync()
        {
            try
            {
                var response = await _tenantRepository.GetAllTenantAsync();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<bool> ToggleActivivity(string userId, bool value)
        {
            try
            {
                var response = _tenantRepository.ToggleIsActive(userId,value);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task UpdateIsDormentedAsync()
        {
            try
            {
                var response = _tenantRepository.UpdateIsDormented();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<bool> UpdateTestingAccountAsync(string tenantId, bool toggleValue)
        {
            try
            {
                var response = _tenantRepository.UpdateTestingAccount(tenantId,toggleValue);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<bool> GetAccountStatusAsync(string tenantId)
        {
            try
            {
                var response = _tenantRepository.GetAccountStatus(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<TotalSubscriptionDetails> GetTotalSubscriptionDetailsAsync(string tenantId)
        {
            try
            {
                var response = _tenantRepository.GetTotalSubscriptionDetails(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public Task<Response<bool>> CreateTenantInfoAsync(TenantsInfo tenantsInfo, bool isTestIngAccount)
        {
            try
            {
                var response = _tenantRepository.CreateTenantInfo(tenantsInfo, isTestIngAccount);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<InvoiceDto, string>> GetInvoiceDetails(InvoiceFilter filter)
        {
            try
            {
                var response = await _tenantRepository.GetInvoiceDetail(filter.PageNumber, filter.PageSize, filter);
                return response;
            }
            catch (Exception ex)
            {
                return new PagedResponse<InvoiceDto, string>(null, 0)
                {
                    Succeeded = false,
                    Errors = new List<string>() { JsonConvert.SerializeObject(ex) },
                    Message = ex.Message
                };
            }
        }

        public async Task<bool> UpdateInvoiceNo(string OldInvoiceNo, string NewInvoiceNo, Guid PaymentId)
        {
            try
            {
                var response = await _tenantRepository.UpdateInvoiceNo(OldInvoiceNo,NewInvoiceNo, PaymentId);
                return response;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTransactionDate(InvoiceDto InvoiceUpdate)
        {
            try
            {
                var response = await _tenantRepository.UpdateTransactionDate(InvoiceUpdate);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UpdateInvoiceDto> GetInvoiceDto(Guid? paymentId)
        {
            var response = await _tenantRepository.GetInvoiceById(paymentId);
            return response;
        }

       

        public async Task<bool> UpdateTDSPercentage(InvoiceDto InvoiceTDS)
        {
            try
            {
                var response = await _tenantRepository.UpdateTDSPercentage(InvoiceTDS);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> ToggleSubscriptionIsActiveAsync(Guid SubscriptionId, bool value)
        {
            try
            {
                var response = await _tenantRepository.ToggleSubscriptionIsActive(SubscriptionId, value);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<bool> ToggleSubscriptionAddonIsActiveAsync(Guid SubscriptionAddonId, bool value)
        {
            try
            {
                var response = await _tenantRepository.ToggleSubscriptionAddonIsActive(SubscriptionAddonId, value);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<string>> GetInvoiceNo()
        {
            try
            {
                var response = await _tenantRepository.GetInvoiceNo();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<GetTenantModel, string>> GetAllDataAsync(GetAllTenantParameter filter)
        {
            try
            {
                var response = await _tenantRepository.GetAllData(filter.PageNumber, filter.PageSize, filter);
                return response;
            }
            catch (Exception ex)
            {
                return new PagedResponse<GetTenantModel, string>(null, 0)

                {
                    Succeeded = false,
                    Errors = new List<string>() { JsonConvert.SerializeObject(ex) },
                    Message = ex.Message
                };
            }
        }

        public async Task<ProfileDto> GetProfileDetailsAsync(string tenantId,string readConnectionString)
        {
            try
            {
                var response = await _tenantRepository.GetProfileDetails(tenantId,readConnectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTenantProfileAsync(ProfileDto profileData, string connectionString)
        {
            try
            {
                var response = await _tenantRepository.UpdateTenantProfile(profileData,connectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<PagedResponse<GetTenantUserModel, string>> GetUserByTenantIdAsync(UserFilter filter,string? readConnectionString)
        {
            try
            {
                var response = await _tenantRepository.GetUserByTenantId(filter, readConnectionString);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> PostCustomListingSourcesasync(string tenantId,string connectionstring)
        {
            try
            {
                var response = await _tenantRepository.PostCustomListingSources(tenantId,connectionstring);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}