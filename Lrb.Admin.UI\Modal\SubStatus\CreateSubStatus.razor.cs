﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.SubStatus
{
    public partial class CreateSubStatus
    {
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        private ICustomSubStatusService CustomSubStatusService {  get; set; }
        [Parameter]
        public CustomSubStatusDto CustomSubStatus { get; set; }
        [Parameter]
        public string? TenantId {  get; set; }
        public string? InputValue { get; set; }

        private async Task SubmitString()
        {
            CustomSubStatus.DisplayName = InputValue;
            CustomSubStatus.Status = InputValue.Replace(' ', '_').ToLower();
            CustomSubStatus.ActionName = InputValue;

            string? result = await CustomSubStatusService.CreateMasterLeadSubStatus(TenantId, CustomSubStatus);
            if(result != null)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{result} is successfully added");
                await JsRuntime.InvokeVoidAsync("location.reload");
            }
            else
            {
                await JsRuntime.InvokeVoidAsync("alert", $"Looks like some issue occured");
            }
        }
    }
}

