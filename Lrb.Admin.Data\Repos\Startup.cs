﻿using Lrb.Admin.Data.Repos.Factory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Admin.Data.Repos
{
    public static class Startup
    {
        public static IServiceCollection AddRepos(this IServiceCollection services, IConfiguration config)
        {
            services.AddTransient<IDBConnectionFactory, NpgSqlConnectionFactory>();
            services.AddTransient<IDapperRepositoryAsync, DapperRepositoryAsync>();
            services.AddTransient<ITenantRepository, TenantRepository>();
            services.AddTransient<IVendorRepository, VendorRepository>();
            services.AddTransient<IAuthenticationRepository, AuthenticationRepository>();
            services.AddTransient<IActivityRepository, ActivityRepository>();
            services.AddTransient<ICustomMasterLeadRepository, CustomMasterLeadRepository>();
            services.AddTransient<IGlobalSettingRepository, GlobalSettingRepository>();
            services.AddTransient<IDuplicateFeatureRepo, DuplicateFeatureRepo>();
            services.AddTransient<ISubscritionRepository, SubscritionRepository>();
            services.AddTransient<IPaymentRepository , PaymentRepository>();
            services.AddTransient<INotificationRepository , NotificationRepository>();
            services.AddTransient<IUserRepository, UserRepository>();
            return services;
        }
    }
}
