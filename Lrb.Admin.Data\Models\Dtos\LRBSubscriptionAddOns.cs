﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class LRBSubscriptionAddOns
    {
        public Guid Id { get; set; }
        public int SoldLicenses { get; set; }
        public double NetAmount { get; set; }
        public double GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public DateTime? PaymentDate { get; set; }
        public bool IsActive { get; set; }
        public bool IsExpired { get; set; }
        public DateTime? ExpiredOn { get; set; }
        public Guid SubscriptionId { get; set; }
        public string TenantId { get; set; }
        public bool IsDeleted { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public DateTime? DueDate { get; set; }
        public bool IsAddOn { get; set; }
    }
}
