﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class PaymentDetailsDto
    {
        public int Id { get; set; }
        public string? CompanyName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? CompanyEmail { get; set; }
        public string? TeamSize { get; set; }
        public string? GSTNO { get; set; }
        public string? OrderAmount { get; set; }
        public string? Status { get; set; }
        public string? PaymentId { get; set; }
        public string? PaymentDate { get; set; }
        public string? AdminName { get; set; }
        public string? AdminNumber { get; set; }
        public string? AdminEmail { get; set; }
        public string? ReraNumber { get; set; }
        public string? PaymentMethod { get; set; }
        public string? CompanyAdress { get; set; }
        public string? MerchantTransactionId { get; set; }
        public string? TransactionId { get; set; }

    }
    public class GetPaymentDetailsResponse
    {

    }
}
