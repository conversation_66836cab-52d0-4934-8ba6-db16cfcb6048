﻿* {
    font-family: 'Lexend Deca', sans-serif;
}

.tenantmasterheader {
    display: flex;
}
.searchButton{
    display:none;
}
.headerText {
    width: 70%;
    margin-bottom: 1%;
}

.headername {
    font-weight: 500;
    font-size: 20px;
    color: #292A2B;
}

.headerbuttons {
    width: 30%;
    display: flex;
    margin-bottom: 1%;
    justify-content: flex-end;
    gap: 10px;
}

    .headerbuttons span {
        cursor: pointer;
    }

.TenantMasterTable {
    background-color: #ffffff;
    padding-top: 1%;
    padding-right: 1%;
    padding-left:1%;

}

.TenantMasterTableHeader {
    border: 1px solid #DDDDDD;
    border-radius: 10px;
    display: flex;
    padding: 5px;
}

.SearchBar {
    display: flex;
    width: 50%;
}

.searchicon {
    margin-top: 0.5%;
}

.SearchBar input {
    border: none;
    outline: none;
    width: 100%;
    font-weight: 400;
    font-size: 12px;
    color: #292A2B;
}

    .SearchBar input:focus, input:active {
        border: none;
        outline: none;
        font-weight: 400;
        font-size: 12px;
        color: #292A2B;
    }

.buttons {
    display: flex;
    font-weight: 500;
    font-size: 12px;
    align-items: center;
    justify-content: end;
    width: 49%;
    gap: 1%;
}

.buttonbutton {
    border-left: 1px solid #e0e0e0;
    padding-left: 1%;
}

    .buttonbutton:hover {
        cursor: pointer;
    }

.all-tables {
    margin-top: 2%;
    overflow: hidden;
}

/* Ensure the table container allows horizontal scrolling */
.table-container {
    max-height: 60vh; /* Set desired height */
    overflow-y: auto;
    overflow-x: auto; /* Enable horizontal scrolling */
    border: 1px solid #ddd;
    position: relative;
    scrollbar-color: #97959E #f1f1f1;
    scrollbar-width: thin;
}

    /* Table Styling */
    .table-container table {
        width: 100%;
        min-width: max-content; /* Prevent content from wrapping */
        border-collapse: collapse;
        white-space: nowrap; /* Prevent text wrapping */
    }

    /* Sticky Header */
    .table-container thead {
        position: sticky;
        top: 0;
        background-color: #f2f2f2;
        z-index: 2;
    }

    /* Header Styling */
    .table-container th {
        font-weight: 600 !important;
        font-size: 12px;
        padding: 15px  10px;
        text-align: left;
        border-bottom: 2px solid #ddd;
    }

    /* Table Data Styling */
    .table-container tbody td {
        padding: 12px 10px;
        font-weight: 400;
        font-size: 12px;
        text-align: left;
        color: #292A2B;
        border-bottom: 1px solid #ddd;
        white-space: nowrap; /* Ensures no line breaks */
    }
    .table-container::-webkit-scrollbar {
        width: 10px; /* Width of the vertical scrollbar */
        height: 10px; /* Height of the horizontal scrollbar */
        padding: 5px; /* Padding around the scrollbar */
    }

    .table-container::-webkit-scrollbar-track {
        background: #f1f1f1; /* Light background for the track */
        border-radius: 10px;
    }

    .table-container::-webkit-scrollbar-thumb {
        background: #97959E; /* Scrollbar color */
        border-radius: 10px;
        border: 3px solid #f1f1f1; /* Creates padding effect */
    }



.tenantmasterfooter {
    display: flex;
    justify-content: end;
    gap: 3%;
    margin-right: 1%;
    font-weight: 400;
    font-size: 12px;
}

.pagesize {
    display: flex;
    justify-content: flex-end;
    gap: 1%;
    align-items: center;
    width: 100%
}

.showEntriesperPage {
}

.pagesizebox {
}

#pagesizeinput {
    height: 70%;
    font-weight: 400;
    font-size: 12px;
    margin-top: 2%;
}

.pagenumber {
    display: flex;
    justify-content: flex-end;
    gap: 1%;
    align-items: center;
}

#pagenumbercircle {
    border: 1px solid;
    padding: 5% 25%;
    border-radius: 50%;
    margin: 0% 10%;
}

.pagenumberbutton:hover {
    cursor: pointer;
}

.popupStyle {
    display: none;
    position: absolute;
    height: auto;
    width: 82%;
    margin: 2px;
    border: var(--rz-panel-border);
    background-color: var(--rz-panel-background-color);
    text-shadow: 1px 1px 1px #000;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

@media only screen and (max-width: 700px) {
    .tenantmasterheader {
        display: flex;
        flex-direction: column;
    }
    .headerbuttons {
        width: 100%;
        display: flex;
        margin-bottom: 1%;
        justify-content: space-between;
        gap: 10px;
    }
    .TenantMasterTableHeader {
        border: 1px solid #DDDDDD;
        border-radius: 10px;
        display: flex;
        flex-direction:column;
    }
    .buttons {
        display: flex;
        font-weight: 500;
        font-size: 12px;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 1%;
        padding-top:2px;
    }
    .tenantmasterfooter {
        display: flex;
        gap: 3px;
        margin-right: 1%;
        font-weight: 400;
        font-size: 12px;
    }
    .pagesizebox {
        width: 100%;
    }
    .SearchBar {
        display: flex;
        width: 100%;
    }
    .searchButton {
        display:block;
        cursor: pointer;
        background-color: #343739;
        text-align:center;
        color: #FFFFFF;
        width: 100px;
        border:1px solid;
        padding:1px;
        border-radius: 5px;
    }
    .searchicon {
        margin-top: 1%;
    }
}
