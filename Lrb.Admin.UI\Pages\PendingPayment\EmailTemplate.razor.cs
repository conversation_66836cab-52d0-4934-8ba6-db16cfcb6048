﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.PendingPayment;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using <PERSON><PERSON><PERSON>;
using iText.Kernel.Pdf;
using iText.Forms.Fields;
using iText.Forms;
using RestSharp;
using Radzen.Blazor.Rendering;
using Microsoft.IdentityModel.Tokens;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;


namespace Lrb.Admin.UI.Pages.PendingPayment
{
    public partial class EmailTemplate
    {
        [Parameter]
        public PendingPaymentFilterParameter? Filter { get; set; } = new();
        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        [Inject]
        public IPaymentService paymentService { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        public EmailModelDto Model { get; set; } = new();
        private bool ShowWarning { get; set; } = false;
        //Example Emails For shown to the frontend
        private string From { get; set; } = "<EMAIL>"; // Default from company email
        private List<string> ToEmails { get; set; } = new List<string> { "Admin Email", "Merchant Email" };
        private List<string> CcEmails { get; set; } = new List<string> { "Vendor Email", "<EMAIL>", "<EMAIL>" };
        private List<string> BccEmails { get; set; } = new List<string> { };
        private string? To { get; set; }
        private string? Cc { get; set; }
        private string? Bcc { get; set; }

        #region Email Dummy Body 
        //Non-Editable Email-Body For shown to the frontend
        string bodyContent = "Hi TenantName,<br /><br />" +
                             "We would like to remind you that your payment is due. Below are the details:<br /><br />" +
                             "Net Due Amount: PendingAmount<br />" +
                             "Payment Due Date: NextDueDate<br />" +
                             "Invoice Number: LastInvoiceNumber<br />" +
                             "GST Number: GSTNumber<br />" +
                             "Total Amount: TotalAmount<br /><br />" +
                             "Company Bank Account Details:<br /><br />" +
                             "Bank Name: ICICI BANK<br />" +
                             "Bank A/C Name: DHINWA SOLUTIONS PRIVATE LIMITED<br />" +
                             "Account Number: ************<br />" +
                             "IFSC Code: ICIC0007295<br /><br />" +
                             "You can also use the QR code below to make the payment:<br />" +
                             "Click On This Link For QR Code(QR Code)<br /><br />" +
                             "Thanks,<br /><br />(Logo Leadrat)";
        private string Subject { get; set; } = "Pending Payment Due Reminder";
        #endregion
        private async Task ShowValidationPopup(string message)
        {
            await JSRuntime.InvokeVoidAsync("alert", message);
        }
        public async Task ShowValidationPopup()
        {
            await JSRuntime.InvokeVoidAsync("alert", "All email fields must be filled.");
        }
        public async Task HandleSendEmails()
        {
            if (ToEmails.Any() || CcEmails.Any() || BccEmails.Any())
            {
                ShowWarning = false;
                await SendDueAmountReminderEmail();
            }
            else
            {
                ShowWarning = true;
            }
        }
        
        public Task<List<string>> LoadEmailsFromDatabase(string type)
        {
            var emails = new List<string>();

            if (type == "To")
            {
                emails.AddRange(new[] { "" });
            }
            else if (type == "Cc")
            {
                emails.AddRange(new[] { "", "" });
            }
            else if (type == "Bcc")
            {
                emails.AddRange(new[] { "", "" });
            }

            return Task.FromResult(emails);
        }

        // Handling To email key press
        private void HandleKeyPressToEmail(KeyboardEventArgs e)
        {

            if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(To))
            {
                if (IsValidEmail(To))
                {
                    AddEmailToList(ToEmails, To);
                    To = "";

                    var selectedEmails = emailGroups
                    .Where(g => g.IsSelected)
                    .SelectMany(g => g.Emails)
                    .ToList();
                }
                else
                {
                    //Console.WriteLine("Pls Enter Valid Email");
                    ShowValidationError("please Enter Valid Email");
                }

            }
        }

        // Handling Cc email key press
        private void HandleKeyPressCcEmail(KeyboardEventArgs e)
        {
            if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(Cc))
            {
                if (IsValidEmail(Cc))
                {
                    AddEmailToList(CcEmails, Cc);
                    Cc = "";

                    var selectedEmails = emailGroups
                    .Where(g => g.IsSelected)
                    .SelectMany(g => g.Emails)
                    .ToList();
                    //e.StopPropagation();
                }
                else
                {
                    ShowValidationError("please Enter Valid Email");
                }
            }
        }

        private void HandleKeyPressBccEmail(KeyboardEventArgs e)
        {
            if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(Bcc))
            {
                if (IsValidEmail(Bcc))
                {
                    AddEmailToList(BccEmails, Bcc);
                    Bcc = "";

                    var selectedEmails = emailGroups
                    .Where(g => g.IsSelected)
                    .SelectMany(g => g.Emails)
                    .ToList();
                }
                else
                {
                    ShowValidationError("please Enter Valid Email");
                }
            }
        }

        private bool IsValidEmail(string email)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
        }

        private void ShowValidationError(string message)
        {
            StateHasChanged();
        }

        private void AddEmailToList(List<string> emailList, string email)
        {
            if (!emailList.Contains(email))
            {
                emailList.Add(email);

                var selectedEmails = emailGroups
                .Where(g => g.IsSelected)
                .SelectMany(g => g.Emails)
                .ToList();
            }
        }

        private void RemoveToEmail(string email)
        {
            ToEmails.Remove(email);
        }

        private void RemoveCcEmail(string email)
        {
            CcEmails.Remove(email);
        }
        private void RemoveBccEmail(string email)
        {
            BccEmails.Remove(email);
        }

        #region DummyEmails 

        public static List<string> DummyEmails = new List<string>
        {
            "<EMAIL>",
            "<EMAIL>","<EMAIL>","<EMAIL>",
            "<EMAIL>","<EMAIL>"
        };

        #endregion
        public async Task SendDueAmountReminderEmail()
        {
            try
            {
                var filteredTenants = (await PaymentService.GetAllPendingPaymentAsync(Filter)).Items ?? new List<GetPendingPaymentModel>();
                EmailEventDto emailEventDto = await PaymentService.SendEmailViaUtility();
                if (emailEventDto == null || string.IsNullOrEmpty(emailEventDto.Body))
                {
                    return; 
                }

                if (filteredTenants == null || !filteredTenants.Any())
                {
                    return; 
                }

                var dbToEmails = await LoadEmailsFromDatabase(To);
                var dbCcEmails = await LoadEmailsFromDatabase(Cc);
                var dbBccEmails = await LoadEmailsFromDatabase(Bcc);

                bool isFrontendModified = !string.IsNullOrEmpty(To) || !string.IsNullOrEmpty(Cc) || !string.IsNullOrEmpty(Bcc);

                foreach (var tenant in filteredTenants)
                {

                    List<string> toRecipients = new List<string>();
                    List<string> ccRecipients = new List<string>();
                    List<string> bccRecipients = new List<string>();

                    toRecipients = ToEmails;
                    ccRecipients = CcEmails;
                    bccRecipients = BccEmails;

                    if (!toRecipients.Any() && !ccRecipients.Any() && !bccRecipients.Any())
                    {
                        //toRecipients.AddRange(EmailHelper.DummyEmails);
                    }

                    var adminEmails = await PaymentService.GetAdminEmailAsync(tenant.TenantId);
                    if(toRecipients.Contains("Admin Email"))
                    {
                        toRecipients.Remove("Admin Email");
                        toRecipients.AddRange(adminEmails.Distinct());
                    }

                    if (!toRecipients.Any() && !ccRecipients.Any() && !bccRecipients.Any())
                    {
                        await ShowValidationPopup("At least one email address must be provided.");
                        return;
                    }

                    tenant.ToRecipients = toRecipients.Distinct().ToList();
                    tenant.CcRecipients = ccRecipients.Distinct().ToList();
                    tenant.BccRecipients = bccRecipients.Distinct().ToList();

                    string invoiceFilePath = await GenerateInvoiceFileAsync(tenant);
                    if (string.IsNullOrEmpty(invoiceFilePath) || !System.IO.File.Exists(invoiceFilePath))
                    {
                        throw new FileNotFoundException($"Invoice file not found at path: {invoiceFilePath}");
                    }

                    tenant.AttachedFiles = new List<string> { invoiceFilePath };


                    emailEventDto.ToRecipients = (emailEventDto.ToRecipients ?? new List<string>())
                                                 .Concat(dbToEmails)
                                                 .Distinct()
                                                 .ToList();

                    emailEventDto.CcRecipients = (emailEventDto.CcRecipients ?? new List<string>())
                                                 .Concat(dbCcEmails)
                                                 .Distinct()
                                                 .ToList();

                    emailEventDto.BccRecipients = (emailEventDto.BccRecipients ?? new List<string>())
                                                  .Concat(dbBccEmails)
                                                  .Distinct()
                                                  .ToList();


                    EmailEventDto emailBody = await EmailHelper.SendPendingPaymentDueMail(tenant, emailEventDto);

                    // Assign basic email details
                    tenant.Sender = emailBody.Sender;
                    tenant.Subject = emailBody.Subject;
                    tenant.ContentBody = emailBody.Body;

                    // Combine all emails (frontend + backend) for To, Cc, and Bcc recipients
                    var combinedToRecipients = emailBody.ToRecipients.Concat(dbToEmails).Distinct().ToList();
                    var combinedCcRecipients = emailBody.CcRecipients.Concat(dbCcEmails).Distinct().ToList();
                    var combinedBccRecipients = emailBody.BccRecipients.Concat(dbBccEmails).Distinct().ToList();

                    // Assign the combined recipient lists to the tenant
                    tenant.ToRecipients = combinedToRecipients.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                    tenant.CcRecipients = combinedCcRecipients.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                    tenant.BccRecipients = combinedBccRecipients.Where(i => !string.IsNullOrWhiteSpace(i)).ToList();

                    // Reset the email template instance after use
                    emailEventDto = await PaymentService.SendEmailViaUtility(); // Reload a new instance
                    if(tenant.ToRecipients.Count != 0)
                    {
                        bool isEmailSent = await PaymentService.SendPendingPaymentEmailAsync(tenant);
                        if (!isEmailSent)
                        {
                            Console.WriteLine("Failed to send email for tenant: " + tenant.TenantId);
                        }
                    }
                    else
                    {
                        await JSRuntime.InvokeVoidAsync("alert", $"Add ToRecipients in Mail");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in filtering or sending emails: {ex.Message}");
            }
        }
        public async Task<string> GenerateInvoiceFileAsync(GetPendingPaymentModel tenant)
        {
            LRBPayments payments = await paymentService.GetPaymentDetailsBySubscriptionIdAsync(tenant.Id);
            try
            {
                string inputFile = "https://leadrat-black.s3.ap-south-1.amazonaws.com/Documents/Tenant_TDSCertificates/PendingPaymentInvoiceTemplate.pdf";
                string outputFile = "Proforma_Invoice_document.pdf";
                string filename = tenant.Address + "_" + tenant.GSTNumber + "_Leadrat.pdf";

                using (PdfReader reader = new PdfReader(inputFile))
                using (PdfWriter writer = new PdfWriter(outputFile))
                using (PdfDocument pdfDoc = new PdfDocument(reader, writer))
                {  
                    PdfAcroForm form = PdfAcroForm.GetAcroForm(pdfDoc, true);
                    PdfFormField companyNameField = form.GetField("CompanyName");
                    companyNameField.SetValue(tenant.TenantId).SetReadOnly(true);
                    PdfFormField CompanyAddress = form.GetField("CompanyAddress");
                    CompanyAddress.SetValue(tenant.Address).SetReadOnly(true);
                    PdfFormField GST = form.GetField("GSTNo").SetReadOnly(true);
                    if (tenant?.GSTNumber?.ToUpper().Trim() != "NA")
                    {
                        GST.SetValue(tenant?.GSTNumber);
                    }
                    PdfFormField InvoiceNo = form.GetField("InvoiceNo").SetReadOnly(true);
                    InvoiceNo.SetValue(tenant.InvoiceNumber);
                    PdfFormField InvoiceDate = form.GetField("InvoiceDate").SetReadOnly(true);
                    InvoiceDate.SetValue(tenant.TransactionDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                    PdfFormField AmountDue = form.GetField("AmountDue").SetReadOnly(true);
                    if (tenant.TotalAmount == tenant.TotalAmountPaid)
                    {
                        AmountDue.SetValue(("0/-").ToString());
                    }
                    else
                    {
                        AmountDue.SetValue((tenant.TotalAmount + "/-").ToString());
                    }
                    PdfFormField DueDate = form.GetField("DueDate").SetReadOnly(true);
                    DueDate.SetValue(tenant.NextDueDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                    if (tenant.Source == "AddOnSubscription")
                    {
                        PdfFormField Description = form.GetField("Description").SetReadOnly(true);
                        Description.SetValue("CRM Addon");
                    }
                    else if (tenant.Source == "Subscription")
                    {
                        PdfFormField Description = form.GetField("Description").SetReadOnly(true);
                        Description.SetValue("CRM Application");
                    }
                    PdfFormField PaymentDate = form.GetField("PaymentDate").SetReadOnly(true);
                    PaymentDate.SetValue(tenant.TransactionDate?.ToLocalTime().ToString("dd/MM/yyyy"));
                    PdfFormField NoofLicense = form.GetField("NoOfLicence").SetReadOnly(true);
                    NoofLicense.SetValue(tenant.NoOfLicenses.ToString());
                    PdfFormField Validity = form.GetField("Validity").SetReadOnly(true);
                    Validity.SetValue(tenant.LicenseValidity?.ToLocalTime().ToString("dd/MM/yyyy"));
                    PdfFormField GSTAmount = form.GetField("GSTAmount").SetReadOnly(true);
                    GSTAmount.SetValue(payments.GSTAmount.ToString() + "/-");
                    PdfFormField NetAmount = form.GetField("NetAmount").SetReadOnly(true);
                    NetAmount.SetValue(payments.NetAmount.ToString() + "/-");
                    PdfFormField TotalPaid = form.GetField("TotalPaid").SetReadOnly(true);
                    TotalPaid.SetValue(payments.TotalAmount.ToString() + "/-");
                    PdfFormField AmountDueafterPayment = form.GetField("AmountDueafterPayment").SetReadOnly(true);
                    AmountDueafterPayment.SetValue(tenant.PendingAmount.ToString() + "/-");
                    PdfFormField PaymentMode = form.GetField("PaymentMode").SetReadOnly(true);
                    PaymentMode.SetValue(tenant.PaymentMode.ToString());

                    form.GetField("PaymentMode")?.SetValue(tenant.PaymentMode.ToString()).SetReadOnly(true);

                    pdfDoc.Close();
                }

                return outputFile;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating invoice PDF: {ex.Message}");
                throw;
            }
        }
        public Task Reset()
        {
            Model = new();
            Model.Sender = default;
            Model.Subject = default;

            return Task.CompletedTask;
        }

        public string BodyContent { get; set; }
        // List of dynamic email groups fetched from the backend
        public List<EmailGroupViewModel> emailGroups = new List<EmailGroupViewModel>();
        public async Task HandleSendEmail()
        {
            // Combine selected groups' emails
            var selectedEmails = emailGroups
                .Where(g => g.IsSelected)
                .SelectMany(g => g.Emails)
                .ToList();

            // Ensure at least one recipient is selected
            if (!selectedEmails.Any())
            {
                // Show some warning to the user
                return;
            }

            // Prepare email model for sending
            var emailModel = new EmailModel
            {
                To = selectedEmails,
                Subject = Subject,
                Body = BodyContent
            };
        }

        public class EmailGroupViewModel
        {
            public string GroupName { get; set; }
            public List<string> Emails { get; set; }
            public bool IsSelected { get; set; }
        }

        public class EmailModel
        {
            public List<string> To { get; set; }
            public string Subject { get; set; }
            public string Body { get; set; }
        }

        public class EmailGroup
        {
            public string GroupName { get; set; }
            public List<string> Emails { get; set; }
        }


        public GetPendingPaymentModel tenant = new GetPendingPaymentModel
        {
            ToRecipients = new List<string>(),
            CcRecipients = new List<string>(),
            BccRecipients = new List<string>()
        };
    }
}





















  