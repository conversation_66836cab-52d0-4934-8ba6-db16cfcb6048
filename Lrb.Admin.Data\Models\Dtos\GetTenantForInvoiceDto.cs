﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class GetTenantForInvoiceDto
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public double DueAmount { get; set; }
        public DateTime NextDueDate { get; set; }
        public int SoldLicenses { get; set; }
        public DateTime LicenseValidity { get; set; }
        public double SoldPrice { get; set; }
        public double GSTAmount { get; set; }
        public PaymentMode Mode { get; set; }
        public double TotalAmount { get; set; }
        public BillingType BillingType { get; set; }
        public string? GSTNumber {  get; set; }
    }
}
