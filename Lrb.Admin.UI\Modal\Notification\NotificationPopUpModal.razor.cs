﻿using Blazored.Modal;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Lrb.Admin.UI.Modal.Notification
{
    public partial class NotificationPopUpModal
    {
        [Inject]
        public INotificationService Notification { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        private ModalParameters Parameter { get; set; }
        [CascadingParameter]
        public GetTenantUserModel userModel { get; set; } = new();
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public string? Id { get; set; }
        public NotificationModelDto Model { get; set; } = new();

        public async Task SendNotificationToAllUser()
        {
            Model.UserIds = await Notification.GetAllUserIdsAsync(TenantId);
            var response = await Notification.SendNotificationAsync(TenantId, Model);
        }
    }
}
