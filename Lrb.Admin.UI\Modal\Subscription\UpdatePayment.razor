﻿<div class="form-container">
    <div class="form-group">
        <label for="payment-date">Payment Date</label>
        <input type="date" @bind-value="PaymentModel.CreatedOn"/>
    </div>
    <div class="form-group">
        <label for="payment-mode">Payment Mode</label>
        <select id="PaymentMode" @bind="PaymentModel.Mode">
            @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.PaymentMode)))
            {
                <option value="@item">@item</option>
            }
        </select>
    </div>
    <div class="form-group">
        <label for="payment-mode">Payment Type</label>
        <select id="PaymentType" @bind="PaymentModel.Type">
            @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.PaymentType)))
            {
                <option value="@item">@item</option>
            }
        </select>
    </div>
    <div class="form-group">
        <label for="sold-price">Net Amount</label>
        <input type="number" @bind-value="netinput">
    </div>
    <div class="form-group">
        <label for="gst-amount">Gst Amount</label>
        <input type="number" @bind-value="gstinput">
    </div>
    <div class="form-group">
        <label for="Total-amount">Total Amount</label>
        <input type="number" @bind-value="paidinput">
    </div>
    <div class="form-group">
        <label for="pending-amount">Balance Amount</label>
        <input type="number" @bind-value="pendinginput">
    </div>
    <div class="form-group">
        <label for="payment-date">NextDue Date</label>
        <input type="date" @bind-value="PaymentModel.NextDueDate" />
    </div>
    <div class="form-group">
        <label for="text-field">Description</label>
        <input type="text" @bind-value="PaymentModel.Description" />
    </div>
</div>
<div class="footer">
    <span @onclick="@UpdateSubscription">
        <svg width="87" height="34" viewBox="0 0 87 34" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="87" height="34" rx="4" fill="#121212" />
            <path d="M25.792 22.12C25.304 22.12 24.856 22.06 24.448 21.94C24.04 21.812 23.672 21.624 23.344 21.376C23.016 21.128 22.724 20.828 22.468 20.476L23.488 19.324C23.88 19.868 24.264 20.244 24.64 20.452C25.016 20.66 25.44 20.764 25.912 20.764C26.184 20.764 26.432 20.724 26.656 20.644C26.88 20.556 27.056 20.44 27.184 20.296C27.312 20.144 27.376 19.972 27.376 19.78C27.376 19.644 27.348 19.52 27.292 19.408C27.244 19.288 27.168 19.184 27.064 19.096C26.96 19 26.832 18.912 26.68 18.832C26.528 18.752 26.356 18.684 26.164 18.628C25.972 18.572 25.76 18.52 25.528 18.472C25.088 18.384 24.704 18.268 24.376 18.124C24.048 17.972 23.772 17.788 23.548 17.572C23.324 17.348 23.16 17.1 23.056 16.828C22.952 16.548 22.9 16.232 22.9 15.88C22.9 15.528 22.976 15.204 23.128 14.908C23.288 14.612 23.504 14.356 23.776 14.14C24.048 13.924 24.364 13.756 24.724 13.636C25.084 13.516 25.472 13.456 25.888 13.456C26.36 13.456 26.78 13.512 27.148 13.624C27.524 13.736 27.852 13.904 28.132 14.128C28.42 14.344 28.656 14.608 28.84 14.92L27.808 15.94C27.648 15.692 27.468 15.488 27.268 15.328C27.068 15.16 26.852 15.036 26.62 14.956C26.388 14.868 26.144 14.824 25.888 14.824C25.6 14.824 25.348 14.864 25.132 14.944C24.924 15.024 24.76 15.14 24.64 15.292C24.52 15.436 24.46 15.612 24.46 15.82C24.46 15.98 24.496 16.124 24.568 16.252C24.64 16.372 24.74 16.48 24.868 16.576C25.004 16.672 25.176 16.756 25.384 16.828C25.592 16.9 25.828 16.964 26.092 17.02C26.532 17.108 26.928 17.228 27.28 17.38C27.632 17.524 27.932 17.7 28.18 17.908C28.428 18.108 28.616 18.34 28.744 18.604C28.872 18.86 28.936 19.148 28.936 19.468C28.936 20.02 28.804 20.496 28.54 20.896C28.284 21.288 27.92 21.592 27.448 21.808C26.976 22.016 26.424 22.12 25.792 22.12ZM32.5834 22.12C32.1274 22.12 31.7314 22.02 31.3954 21.82C31.0594 21.612 30.8034 21.32 30.6274 20.944C30.4514 20.56 30.3634 20.104 30.3634 19.576V15.676H31.8394V19.264C31.8394 19.6 31.8914 19.888 31.9954 20.128C32.0994 20.36 32.2514 20.54 32.4514 20.668C32.6514 20.796 32.8914 20.86 33.1714 20.86C33.3794 20.86 33.5674 20.828 33.7354 20.764C33.9114 20.692 34.0634 20.596 34.1914 20.476C34.3194 20.348 34.4194 20.2 34.4914 20.032C34.5634 19.856 34.5994 19.668 34.5994 19.468V15.676H36.0754V22H34.6354L34.6114 20.68L34.8754 20.536C34.7794 20.84 34.6154 21.112 34.3834 21.352C34.1594 21.584 33.8914 21.772 33.5794 21.916C33.2674 22.052 32.9354 22.12 32.5834 22.12ZM41.2974 22.12C41.0174 22.12 40.7414 22.08 40.4694 22C40.2054 21.912 39.9654 21.796 39.7494 21.652C39.5334 21.508 39.3534 21.348 39.2094 21.172C39.0654 20.988 38.9694 20.804 38.9214 20.62L39.2694 20.464L39.2334 21.976H37.8054V13.12H39.2814V17.14L39.0174 17.02C39.0574 16.828 39.1454 16.648 39.2814 16.48C39.4254 16.304 39.6014 16.148 39.8094 16.012C40.0174 15.868 40.2454 15.756 40.4934 15.676C40.7414 15.588 40.9934 15.544 41.2494 15.544C41.8174 15.544 42.3174 15.684 42.7494 15.964C43.1894 16.244 43.5334 16.632 43.7814 17.128C44.0374 17.624 44.1654 18.188 44.1654 18.82C44.1654 19.46 44.0414 20.028 43.7934 20.524C43.5454 21.02 43.2014 21.412 42.7614 21.7C42.3294 21.98 41.8414 22.12 41.2974 22.12ZM40.9854 20.824C41.3214 20.824 41.6214 20.74 41.8854 20.572C42.1494 20.396 42.3574 20.16 42.5094 19.864C42.6614 19.56 42.7374 19.212 42.7374 18.82C42.7374 18.436 42.6614 18.096 42.5094 17.8C42.3654 17.504 42.1614 17.272 41.8974 17.104C41.6334 16.936 41.3294 16.852 40.9854 16.852C40.6414 16.852 40.3374 16.936 40.0734 17.104C39.8094 17.272 39.6014 17.504 39.4494 17.8C39.2974 18.096 39.2214 18.436 39.2214 18.82C39.2214 19.212 39.2974 19.56 39.4494 19.864C39.6014 20.16 39.8094 20.396 40.0734 20.572C40.3374 20.74 40.6414 20.824 40.9854 20.824ZM45.4572 22V15.676H46.9092L46.9332 16.948L46.6932 17.044C46.7652 16.836 46.8732 16.644 47.0172 16.468C47.1612 16.284 47.3332 16.128 47.5332 16C47.7332 15.864 47.9452 15.76 48.1692 15.688C48.3932 15.608 48.6212 15.568 48.8532 15.568C49.1972 15.568 49.5012 15.624 49.7652 15.736C50.0372 15.84 50.2612 16.008 50.4372 16.24C50.6212 16.472 50.7572 16.768 50.8452 17.128L50.6172 17.08L50.7132 16.888C50.8012 16.696 50.9252 16.524 51.0852 16.372C51.2452 16.212 51.4252 16.072 51.6252 15.952C51.8252 15.824 52.0332 15.728 52.2492 15.664C52.4732 15.6 52.6932 15.568 52.9092 15.568C53.3892 15.568 53.7892 15.664 54.1092 15.856C54.4292 16.048 54.6692 16.34 54.8292 16.732C54.9892 17.124 55.0692 17.608 55.0692 18.184V22H53.5812V18.28C53.5812 17.96 53.5372 17.696 53.4492 17.488C53.3692 17.28 53.2412 17.128 53.0652 17.032C52.8972 16.928 52.6852 16.876 52.4292 16.876C52.2292 16.876 52.0372 16.912 51.8532 16.984C51.6772 17.048 51.5252 17.144 51.3972 17.272C51.2692 17.392 51.1692 17.532 51.0972 17.692C51.0252 17.852 50.9892 18.028 50.9892 18.22V22H49.5012V18.268C49.5012 17.964 49.4572 17.712 49.3692 17.512C49.2812 17.304 49.1532 17.148 48.9852 17.044C48.8172 16.932 48.6132 16.876 48.3732 16.876C48.1732 16.876 47.9852 16.912 47.8092 16.984C47.6332 17.048 47.4812 17.14 47.3532 17.26C47.2252 17.38 47.1252 17.52 47.0532 17.68C46.9812 17.84 46.9452 18.016 46.9452 18.208V22H45.4572ZM56.8758 22V15.676H58.3518V22H56.8758ZM57.5958 14.332C57.2998 14.332 57.0678 14.26 56.8998 14.116C56.7398 13.964 56.6598 13.752 56.6598 13.48C56.6598 13.224 56.7438 13.016 56.9118 12.856C57.0798 12.696 57.3078 12.616 57.5958 12.616C57.8998 12.616 58.1318 12.692 58.2918 12.844C58.4598 12.988 58.5438 13.2 58.5438 13.48C58.5438 13.728 58.4598 13.932 58.2918 14.092C58.1238 14.252 57.8918 14.332 57.5958 14.332ZM60.9074 22V14.068H62.3834V22H60.9074ZM59.6594 17.044V15.676H63.7634V17.044H59.6594Z" fill="white" />
        </svg>
    </span>
</div>