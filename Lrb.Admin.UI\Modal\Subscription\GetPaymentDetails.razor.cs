﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Utility;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using Radzen;

namespace Lrb.Admin.UI.Modal.Subscription
{
    partial class GetPaymentDetails
    {
        private readonly string _baseURL = "https://leadrat.com/";
        private readonly string _apiName = "getPaymentDetails.php";
        
        [Inject]
        private IPaymentService PaymentService { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public NotificationService NotificationService { get; set; }

        private async Task GetAllPaymetDetails()
        {
            var result = await PaymentService.GetPaymenDeatilstAsync(_baseURL, _apiName);
            if (result.Data != null && result.Data.Count() > 0)
            {
                
                var phonepeDetatils = result.Data.Where(x => x?.PaymentMethod?.ToLowerInvariant()?.Contains("phonepe") ?? false).ToList();
                var cashfreeDetais = result.Data.Where(x => x?.PaymentMethod?.ToLowerInvariant()?.Contains("cashfree") ?? false).ToList();
                Dictionary<string, List<PaymentDetailsDto>> paymentDict = new Dictionary<string, List<PaymentDetailsDto>>() 
                {
                    {"Phonepe",phonepeDetatils}, 
                    {"Cashfree",cashfreeDetais}
                };
                byte[] data = ExcelHelper.CreateExcelFromList<PaymentDetailsDto>(paymentDict);
                string fileName = "payment_details.xlsx";
                string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            else
            {
                NotificationMessage notificationMessage = new NotificationMessage()
                {
                    Summary = "Not Found Payment Details",
                    Severity = NotificationSeverity.Error,
                };

                NotificationService.Notify(notificationMessage);
            }
        }
    }
}
