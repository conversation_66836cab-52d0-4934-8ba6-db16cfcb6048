﻿@page "/tenant"
@page "/tenant/{Tenantid}"
<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/open-iconic/1.1.1/font/css/open-iconic.min.css">

</head>
@* up part *@
<div class="summary-container">
    <div class="tenant-profile">
        <div class="tenant-info">
              <div class="logo">
                <img src="https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/face_human_blank_user_avatar_mannequin_dummy-512.webp" alt="">
              </div>
              <div class="info">
                <span class="info-name">@TenantModel?.TenantName</span>
                @if (ProfileData == null)
                {
                    <span class="info-Address">No Address Found</span>
                }
                else
                {
                    <span class="info-Address">
                        @if (ProfileData?.SubLocality != null && ProfileData?.SubLocality != "")
                        {
                            <span>@ProfileData?.SubLocality,</span>
                        }
                        @if (ProfileData?.Locality != null && ProfileData?.Locality != "")
                        {
                            <span>@ProfileData?.Locality,</span>
                        }
                        @if (ProfileData?.City != null && ProfileData?.City != "")
                        {
                            <span>@ProfileData?.City,</span>
                        }
                        @if (ProfileData?.State != null && ProfileData?.State != "")
                        {
                            <span>@ProfileData?.State,</span>
                        }
                        @if (ProfileData?.PostalCode != null && ProfileData?.PostalCode != "")
                        {
                            <span>@ProfileData?.PostalCode</span>
                        }
                    </span>
                }
                <span class="info-CRMlink"><a href="@TenantWeblink" target="_blank">CRM HomePage</a></span>
              </div>
        </div>
        <div class="buttons">
            @* MOU *@
            <svg width="67" height="26" viewBox="0 0 67 26" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="@(async () => {CollectionType = CollectionType.Subscription; await UpdateAndDownloadPdf(@TenantAddress,@TenantModel.TenantName);})">
                <rect width="67" height="26" rx="4" fill="#439B89" />
                <path d="M18.4 9C18.4 8.77909 18.2209 8.6 18 8.6C17.7791 8.6 17.6 8.77909 17.6 9H18.4ZM18 14.7778L17.7048 15.0477C17.7806 15.1306 17.8877 15.1778 18 15.1778C18.1123 15.1778 18.2194 15.1306 18.2952 15.0477L18 14.7778ZM20.073 13.1032C20.2221 12.9402 20.2107 12.6872 20.0477 12.5381C19.8846 12.3891 19.6316 12.4004 19.4826 12.5634L20.073 13.1032ZM16.5174 12.5634C16.3684 12.4004 16.1154 12.3891 15.9523 12.5381C15.7893 12.6872 15.7779 12.9402 15.927 13.1032L16.5174 12.5634ZM19.3333 16.6H16.6667V17.4H19.3333V16.6ZM16.6667 16.6C16.0268 16.6 15.5872 16.5992 15.2569 16.5548C14.9382 16.5119 14.7819 16.4352 14.6734 16.3266L14.1077 16.8923C14.3897 17.1743 14.7429 17.2928 15.1503 17.3476C15.5463 17.4008 16.0494 17.4 16.6667 17.4V16.6ZM13.6 14.3333C13.6 14.9506 13.5992 15.4537 13.6524 15.8497C13.7072 16.2571 13.8257 16.6103 14.1077 16.8923L14.6734 16.3266C14.5648 16.2181 14.4881 16.0618 14.4452 15.7431C14.4008 15.4128 14.4 14.9732 14.4 14.3333H13.6ZM21.6 14.3333C21.6 14.9732 21.5992 15.4128 21.5548 15.7431C21.5119 16.0618 21.4352 16.2181 21.3266 16.3266L21.8923 16.8923C22.1743 16.6103 22.2928 16.2571 22.3476 15.8497C22.4008 15.4537 22.4 14.9506 22.4 14.3333H21.6ZM19.3333 17.4C19.9506 17.4 20.4537 17.4008 20.8497 17.3476C21.2571 17.2928 21.6103 17.1743 21.8923 16.8923L21.3266 16.3266C21.2181 16.4352 21.0618 16.5119 20.7431 16.5548C20.4128 16.5992 19.9732 16.6 19.3333 16.6V17.4ZM17.6 9V14.7778H18.4V9H17.6ZM18.2952 15.0477L20.073 13.1032L19.4826 12.5634L17.7048 14.5079L18.2952 15.0477ZM18.2952 14.5079L16.5174 12.5634L15.927 13.1032L17.7048 15.0477L18.2952 14.5079Z" fill="#F5F5F5" />
                <path d="M29.034 17V9.3H30.387L33.192 13.865L32.4 13.854L35.238 9.3H36.525V17H35.106V14.107C35.106 13.447 35.1207 12.853 35.15 12.325C35.1867 11.797 35.2453 11.2727 35.326 10.752L35.502 11.225L33.115 14.91H32.389L30.079 11.258L30.233 10.752C30.3137 11.2433 30.3687 11.7493 30.398 12.27C30.4347 12.7833 30.453 13.3957 30.453 14.107V17H29.034ZM41.9165 17.11C41.3665 17.11 40.8569 17.011 40.3875 16.813C39.9182 16.615 39.5075 16.34 39.1555 15.988C38.8109 15.6287 38.5432 15.207 38.3525 14.723C38.1619 14.239 38.0665 13.711 38.0665 13.139C38.0665 12.567 38.1619 12.039 38.3525 11.555C38.5432 11.071 38.8109 10.653 39.1555 10.301C39.5075 9.94167 39.9182 9.663 40.3875 9.465C40.8569 9.267 41.3665 9.168 41.9165 9.168C42.4739 9.168 42.9872 9.267 43.4565 9.465C43.9259 9.663 44.3329 9.94167 44.6775 10.301C45.0222 10.6603 45.2899 11.082 45.4805 11.566C45.6712 12.0427 45.7665 12.567 45.7665 13.139C45.7665 13.7037 45.6712 14.228 45.4805 14.712C45.2899 15.196 45.0222 15.6177 44.6775 15.977C44.3329 16.3363 43.9259 16.615 43.4565 16.813C42.9872 17.011 42.4739 17.11 41.9165 17.11ZM41.9165 15.746C42.2685 15.746 42.5875 15.6837 42.8735 15.559C43.1669 15.427 43.4199 15.2437 43.6325 15.009C43.8452 14.767 44.0102 14.4883 44.1275 14.173C44.2522 13.8577 44.3145 13.513 44.3145 13.139C44.3145 12.765 44.2522 12.4203 44.1275 12.105C44.0102 11.7897 43.8452 11.5147 43.6325 11.28C43.4199 11.038 43.1669 10.8547 42.8735 10.73C42.5875 10.598 42.2685 10.532 41.9165 10.532C41.5719 10.532 41.2529 10.598 40.9595 10.73C40.6662 10.8547 40.4132 11.0343 40.2005 11.269C39.9879 11.5037 39.8229 11.7787 39.7055 12.094C39.5882 12.4093 39.5295 12.7577 39.5295 13.139C39.5295 13.513 39.5882 13.8613 39.7055 14.184C39.8229 14.4993 39.9879 14.7743 40.2005 15.009C40.4132 15.2437 40.6662 15.427 40.9595 15.559C41.2529 15.6837 41.5719 15.746 41.9165 15.746ZM50.3972 17.066C49.7885 17.066 49.2459 16.9377 48.7692 16.681C48.2925 16.417 47.9149 16.0577 47.6362 15.603C47.3649 15.141 47.2292 14.6203 47.2292 14.041V9.289H48.6702V13.942C48.6702 14.2867 48.7472 14.5947 48.9012 14.866C49.0625 15.13 49.2752 15.3427 49.5392 15.504C49.8032 15.6653 50.0892 15.746 50.3972 15.746C50.7345 15.746 51.0389 15.6653 51.3102 15.504C51.5815 15.3427 51.7942 15.13 51.9482 14.866C52.1095 14.5947 52.1902 14.2867 52.1902 13.942V9.289H53.5762V14.041C53.5762 14.6203 53.4369 15.141 53.1582 15.603C52.8869 16.0577 52.5129 16.417 52.0362 16.681C51.5595 16.9377 51.0132 17.066 50.3972 17.066Z" fill="white" />
            </svg>
            @* Edit *@
            <svg width="62" height="26" viewBox="0 0 62 26" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="@UpdateProfileData">
                <rect width="62" height="26" rx="4" fill="#439B89" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M18.9889 8.68311C19.4543 8.215 20.2089 8.215 20.6743 8.68311L21.5171 9.5307C21.9825 9.9988 21.9825 10.7578 21.5171 11.2259L20.7699 11.9774C20.7112 11.9481 20.6495 11.9166 20.5855 11.8827C20.1548 11.6546 19.6437 11.3345 19.2623 10.9509C18.8808 10.5673 18.5626 10.0532 18.3358 9.62005C18.3021 9.55568 18.2707 9.4936 18.2417 9.4346L18.9889 8.68311ZM18.7154 11.5009C19.1668 11.9549 19.7408 12.3122 20.1955 12.5551L17.2571 15.5103C17.0748 15.6937 16.8382 15.8127 16.5829 15.8494L14.8132 16.1036C14.4199 16.1601 14.0828 15.8211 14.139 15.4256L14.3918 13.6456C14.4283 13.3889 14.5465 13.151 14.7289 12.9676L17.6673 10.0123C17.9088 10.4695 18.264 11.0469 18.7154 11.5009ZM16.9684 16.8876C16.7548 16.8876 16.5817 17.0617 16.5817 17.2765C16.5817 17.4913 16.7548 17.6654 16.9684 17.6654H21.4795C21.693 17.6654 21.8661 17.4913 21.8661 17.2765C21.8661 17.0617 21.693 16.8876 21.4795 16.8876H16.9684Z" fill="white" />
                <path d="M29.034 17V9.3H34.072V10.598H30.442V15.702H34.116V17H29.034ZM29.694 13.7V12.424H33.511V13.7H29.694ZM37.9453 17.11C37.432 17.11 36.97 16.9817 36.5593 16.725C36.156 16.461 35.8333 16.1053 35.5913 15.658C35.3566 15.2033 35.2393 14.6827 35.2393 14.096C35.2393 13.5167 35.3566 12.9997 35.5913 12.545C35.826 12.0903 36.145 11.7347 36.5483 11.478C36.959 11.214 37.421 11.082 37.9343 11.082C38.213 11.082 38.4806 11.126 38.7373 11.214C39.0013 11.302 39.236 11.423 39.4413 11.577C39.6466 11.7237 39.808 11.8887 39.9253 12.072C40.05 12.2553 40.1196 12.446 40.1343 12.644L39.7713 12.688V8.86H41.1243V17H39.8153L39.7823 15.636L40.0463 15.658C40.039 15.8413 39.973 16.0173 39.8483 16.186C39.731 16.3547 39.577 16.5087 39.3863 16.648C39.1956 16.7873 38.972 16.901 38.7153 16.989C38.466 17.0697 38.2093 17.11 37.9453 17.11ZM38.2093 15.966C38.5246 15.966 38.8033 15.8853 39.0453 15.724C39.2873 15.5627 39.4743 15.3427 39.6063 15.064C39.7456 14.7853 39.8153 14.4627 39.8153 14.096C39.8153 13.7367 39.7456 13.4177 39.6063 13.139C39.4743 12.853 39.2873 12.633 39.0453 12.479C38.8033 12.3177 38.5246 12.237 38.2093 12.237C37.894 12.237 37.6153 12.3177 37.3733 12.479C37.1313 12.633 36.9406 12.853 36.8013 13.139C36.6693 13.4177 36.6033 13.7367 36.6033 14.096C36.6033 14.4627 36.6693 14.7853 36.8013 15.064C36.9406 15.3427 37.1313 15.5627 37.3733 15.724C37.6153 15.8853 37.894 15.966 38.2093 15.966ZM42.8142 17V11.203H44.1672V17H42.8142ZM43.4742 9.971C43.2028 9.971 42.9902 9.905 42.8362 9.773C42.6895 9.63367 42.6162 9.43933 42.6162 9.19C42.6162 8.95533 42.6932 8.76467 42.8472 8.618C43.0012 8.47133 43.2102 8.398 43.4742 8.398C43.7528 8.398 43.9655 8.46767 44.1122 8.607C44.2662 8.739 44.3432 8.93333 44.3432 9.19C44.3432 9.41733 44.2662 9.60433 44.1122 9.751C43.9582 9.89767 43.7455 9.971 43.4742 9.971ZM46.5098 17V9.729H47.8628V17H46.5098ZM45.3658 12.457V11.203H49.1278V12.457H45.3658Z" fill="white" />
            </svg>
            @* Global Settings *@
            <svg width="123" height="26" viewBox="0 0 123 26" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="@( async () => {await ShowGlobolSettingPopUpModal();})">
                <rect width="123" height="26" rx="4" fill="#439B89" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M17.9993 8.33203C17.4631 8.33203 16.9747 8.61236 15.9979 9.17302L15.6675 9.36264C14.6907 9.9233 14.2023 10.2036 13.9342 10.6654C13.666 11.1271 13.666 11.6878 13.666 12.8091V13.1883C13.666 14.3096 13.666 14.8703 13.9342 15.332C14.2023 15.7938 14.6907 16.0741 15.6675 16.6348L15.9979 16.8244C16.9747 17.385 17.4631 17.6654 17.9993 17.6654C18.5356 17.6654 19.024 17.385 20.0008 16.8244L20.3312 16.6348C21.308 16.0741 21.7964 15.7938 22.0645 15.332C22.3327 14.8703 22.3327 14.3096 22.3327 13.1883V12.8091C22.3327 11.6878 22.3327 11.1271 22.0645 10.6654C21.7964 10.2036 21.308 9.9233 20.3312 9.36264L20.0008 9.17302C19.024 8.61236 18.5356 8.33203 17.9993 8.33203ZM16.1938 12.9987C16.1938 12.0322 17.0022 11.2487 17.9993 11.2487C18.9965 11.2487 19.8049 12.0322 19.8049 12.9987C19.8049 13.9652 18.9965 14.7487 17.9993 14.7487C17.0022 14.7487 16.1938 13.9652 16.1938 12.9987Z" fill="white" />
                <path d="M32.532 17.11C31.9527 17.11 31.4173 17.011 30.926 16.813C30.4347 16.6077 30.0057 16.3253 29.639 15.966C29.2797 15.6067 29.001 15.1887 28.803 14.712C28.605 14.228 28.506 13.7037 28.506 13.139C28.506 12.5817 28.605 12.0647 28.803 11.588C29.0083 11.104 29.2943 10.686 29.661 10.334C30.0277 9.97467 30.4567 9.696 30.948 9.498C31.4467 9.3 31.982 9.201 32.554 9.201C32.9573 9.201 33.346 9.256 33.72 9.366C34.094 9.476 34.4313 9.63 34.732 9.828C35.0327 10.026 35.282 10.2497 35.48 10.499L34.578 11.445C34.3727 11.247 34.16 11.082 33.94 10.95C33.7273 10.818 33.5037 10.7153 33.269 10.642C33.0343 10.5687 32.7923 10.532 32.543 10.532C32.1763 10.532 31.8353 10.598 31.52 10.73C31.212 10.862 30.9407 11.0453 30.706 11.28C30.4787 11.5147 30.299 11.7897 30.167 12.105C30.035 12.4203 29.969 12.765 29.969 13.139C29.969 13.4983 30.035 13.8393 30.167 14.162C30.3063 14.4847 30.497 14.767 30.739 15.009C30.981 15.251 31.2597 15.4417 31.575 15.581C31.8977 15.713 32.2387 15.779 32.598 15.779C32.8693 15.779 33.1223 15.7387 33.357 15.658C33.5917 15.5773 33.8007 15.4673 33.984 15.328C34.1673 15.1813 34.3067 15.0127 34.402 14.822C34.5047 14.6313 34.556 14.426 34.556 14.206V13.964L34.754 14.195H32.477V12.919H35.964C35.9787 12.9997 35.9897 13.0913 35.997 13.194C36.0043 13.2893 36.008 13.3847 36.008 13.48C36.0153 13.568 36.019 13.645 36.019 13.711C36.019 14.217 35.931 14.679 35.755 15.097C35.579 15.5077 35.3297 15.8633 35.007 16.164C34.6917 16.4647 34.3213 16.6993 33.896 16.868C33.478 17.0293 33.0233 17.11 32.532 17.11ZM37.2893 17V8.86H38.6533V17H37.2893ZM42.7868 17.11C42.2074 17.11 41.6904 16.9817 41.2358 16.725C40.7811 16.461 40.4218 16.1017 40.1578 15.647C39.8938 15.1923 39.7618 14.6753 39.7618 14.096C39.7618 13.5167 39.8938 12.9997 40.1578 12.545C40.4218 12.0903 40.7811 11.7347 41.2358 11.478C41.6904 11.214 42.2074 11.082 42.7868 11.082C43.3588 11.082 43.8721 11.214 44.3268 11.478C44.7814 11.7347 45.1371 12.0903 45.3938 12.545C45.6578 12.9997 45.7898 13.5167 45.7898 14.096C45.7898 14.6753 45.6578 15.1923 45.3938 15.647C45.1371 16.1017 44.7814 16.461 44.3268 16.725C43.8721 16.9817 43.3588 17.11 42.7868 17.11ZM42.7868 15.911C43.1021 15.911 43.3808 15.8303 43.6228 15.669C43.8721 15.5077 44.0664 15.2913 44.2058 15.02C44.3524 14.7487 44.4221 14.4407 44.4148 14.096C44.4221 13.744 44.3524 13.4323 44.2058 13.161C44.0664 12.8897 43.8721 12.677 43.6228 12.523C43.3808 12.3617 43.1021 12.281 42.7868 12.281C42.4714 12.281 42.1854 12.3617 41.9288 12.523C41.6794 12.677 41.4851 12.8933 41.3458 13.172C41.2064 13.4433 41.1368 13.7513 41.1368 14.096C41.1368 14.4407 41.2064 14.7487 41.3458 15.02C41.4851 15.2913 41.6794 15.5077 41.9288 15.669C42.1854 15.8303 42.4714 15.911 42.7868 15.911ZM50.201 17.11C49.9443 17.11 49.6913 17.0733 49.442 17C49.2 16.9193 48.98 16.813 48.782 16.681C48.584 16.549 48.419 16.4023 48.287 16.241C48.155 16.0723 48.067 15.9037 48.023 15.735L48.342 15.592L48.309 16.978H47V8.86H48.353V12.545L48.111 12.435C48.1477 12.259 48.2283 12.094 48.353 11.94C48.485 11.7787 48.6463 11.6357 48.837 11.511C49.0277 11.379 49.2367 11.2763 49.464 11.203C49.6913 11.1223 49.9223 11.082 50.157 11.082C50.6777 11.082 51.136 11.2103 51.532 11.467C51.9353 11.7237 52.2507 12.0793 52.478 12.534C52.7127 12.9887 52.83 13.5057 52.83 14.085C52.83 14.6717 52.7163 15.1923 52.489 15.647C52.2617 16.1017 51.9463 16.461 51.543 16.725C51.147 16.9817 50.6997 17.11 50.201 17.11ZM49.915 15.922C50.223 15.922 50.498 15.845 50.74 15.691C50.982 15.5297 51.1727 15.3133 51.312 15.042C51.4513 14.7633 51.521 14.4443 51.521 14.085C51.521 13.733 51.4513 13.4213 51.312 13.15C51.18 12.8787 50.993 12.666 50.751 12.512C50.509 12.358 50.2303 12.281 49.915 12.281C49.5997 12.281 49.321 12.358 49.079 12.512C48.837 12.666 48.6463 12.8787 48.507 13.15C48.3677 13.4213 48.298 13.733 48.298 14.085C48.298 14.4443 48.3677 14.7633 48.507 15.042C48.6463 15.3133 48.837 15.5297 49.079 15.691C49.321 15.845 49.5997 15.922 49.915 15.922ZM56.2251 17.11C55.7485 17.11 55.3158 16.978 54.9271 16.714C54.5385 16.45 54.2268 16.0907 53.9921 15.636C53.7575 15.1813 53.6401 14.6643 53.6401 14.085C53.6401 13.5057 53.7575 12.9887 53.9921 12.534C54.2268 12.0793 54.5458 11.7237 54.9491 11.467C55.3525 11.2103 55.8071 11.082 56.3131 11.082C56.6065 11.082 56.8741 11.126 57.1161 11.214C57.3581 11.2947 57.5708 11.412 57.7541 11.566C57.9375 11.72 58.0878 11.896 58.2051 12.094C58.3298 12.292 58.4141 12.5047 58.4581 12.732L58.1611 12.655V11.203H59.5251V17H58.1501V15.614L58.4691 15.559C58.4178 15.757 58.3225 15.9513 58.1831 16.142C58.0511 16.3253 57.8825 16.4903 57.6771 16.637C57.4791 16.7763 57.2555 16.89 57.0061 16.978C56.7641 17.066 56.5038 17.11 56.2251 17.11ZM56.5991 15.911C56.9145 15.911 57.1931 15.834 57.4351 15.68C57.6771 15.526 57.8641 15.3133 57.9961 15.042C58.1355 14.7633 58.2051 14.4443 58.2051 14.085C58.2051 13.733 58.1355 13.4213 57.9961 13.15C57.8641 12.8787 57.6771 12.666 57.4351 12.512C57.1931 12.358 56.9145 12.281 56.5991 12.281C56.2838 12.281 56.0051 12.358 55.7631 12.512C55.5285 12.666 55.3451 12.8787 55.2131 13.15C55.0811 13.4213 55.0151 13.733 55.0151 14.085C55.0151 14.4443 55.0811 14.7633 55.2131 15.042C55.3451 15.3133 55.5285 15.526 55.7631 15.68C56.0051 15.834 56.2838 15.911 56.5991 15.911ZM61.1048 17V8.86H62.4688V17H61.1048ZM69.976 17.11C69.5287 17.11 69.118 17.055 68.744 16.945C68.37 16.8277 68.0327 16.6553 67.732 16.428C67.4313 16.2007 67.1637 15.9257 66.929 15.603L67.864 14.547C68.2233 15.0457 68.5753 15.3903 68.92 15.581C69.2647 15.7717 69.6533 15.867 70.086 15.867C70.3353 15.867 70.5627 15.8303 70.768 15.757C70.9733 15.6763 71.1347 15.57 71.252 15.438C71.3693 15.2987 71.428 15.141 71.428 14.965C71.428 14.8403 71.4023 14.7267 71.351 14.624C71.307 14.514 71.2373 14.4187 71.142 14.338C71.0467 14.25 70.9293 14.1693 70.79 14.096C70.6507 14.0227 70.493 13.9603 70.317 13.909C70.141 13.8577 69.9467 13.81 69.734 13.766C69.3307 13.6853 68.9787 13.579 68.678 13.447C68.3773 13.3077 68.1243 13.139 67.919 12.941C67.7137 12.7357 67.5633 12.5083 67.468 12.259C67.3727 12.0023 67.325 11.7127 67.325 11.39C67.325 11.0673 67.3947 10.7703 67.534 10.499C67.6807 10.2277 67.8787 9.993 68.128 9.795C68.3773 9.597 68.667 9.443 68.997 9.333C69.327 9.223 69.6827 9.168 70.064 9.168C70.4967 9.168 70.8817 9.21933 71.219 9.322C71.5637 9.42467 71.8643 9.57867 72.121 9.784C72.385 9.982 72.6013 10.224 72.77 10.51L71.824 11.445C71.6773 11.2177 71.5123 11.0307 71.329 10.884C71.1457 10.73 70.9477 10.6163 70.735 10.543C70.5223 10.4623 70.2987 10.422 70.064 10.422C69.8 10.422 69.569 10.4587 69.371 10.532C69.1803 10.6053 69.03 10.7117 68.92 10.851C68.81 10.983 68.755 11.1443 68.755 11.335C68.755 11.4817 68.788 11.6137 68.854 11.731C68.92 11.841 69.0117 11.94 69.129 12.028C69.2537 12.116 69.4113 12.193 69.602 12.259C69.7927 12.325 70.009 12.3837 70.251 12.435C70.6543 12.5157 71.0173 12.6257 71.34 12.765C71.6627 12.897 71.9377 13.0583 72.165 13.249C72.3923 13.4323 72.5647 13.645 72.682 13.887C72.7993 14.1217 72.858 14.3857 72.858 14.679C72.858 15.185 72.737 15.6213 72.495 15.988C72.2603 16.3473 71.9267 16.626 71.494 16.824C71.0613 17.0147 70.5553 17.11 69.976 17.11ZM76.9055 17.11C76.2968 17.11 75.7578 16.9817 75.2885 16.725C74.8265 16.4683 74.4635 16.12 74.1995 15.68C73.9428 15.2327 73.8145 14.723 73.8145 14.151C73.8145 13.6963 73.8878 13.282 74.0345 12.908C74.1812 12.534 74.3828 12.2113 74.6395 11.94C74.9035 11.6613 75.2152 11.4487 75.5745 11.302C75.9412 11.148 76.3408 11.071 76.7735 11.071C77.1548 11.071 77.5105 11.1443 77.8405 11.291C78.1705 11.4377 78.4565 11.6393 78.6985 11.896C78.9405 12.1453 79.1238 12.446 79.2485 12.798C79.3805 13.1427 79.4428 13.5203 79.4355 13.931L79.4245 14.404H74.7275L74.4745 13.48H78.2915L78.1155 13.667V13.425C78.0935 13.1977 78.0202 12.9997 77.8955 12.831C77.7708 12.655 77.6095 12.5193 77.4115 12.424C77.2208 12.3213 77.0082 12.27 76.7735 12.27C76.4142 12.27 76.1098 12.3397 75.8605 12.479C75.6185 12.6183 75.4352 12.82 75.3105 13.084C75.1858 13.3407 75.1235 13.6633 75.1235 14.052C75.1235 14.426 75.2005 14.7523 75.3545 15.031C75.5158 15.3097 75.7395 15.526 76.0255 15.68C76.3188 15.8267 76.6598 15.9 77.0485 15.9C77.3198 15.9 77.5692 15.856 77.7965 15.768C78.0238 15.68 78.2695 15.5223 78.5335 15.295L79.2045 16.23C79.0065 16.4133 78.7792 16.571 78.5225 16.703C78.2732 16.8277 78.0092 16.9267 77.7305 17C77.4518 17.0733 77.1768 17.11 76.9055 17.11ZM81.2608 17V9.729H82.6138V17H81.2608ZM80.1168 12.457V11.203H83.8788V12.457H80.1168ZM85.5362 17V9.729H86.8892V17H85.5362ZM84.3922 12.457V11.203H88.1542V12.457H84.3922ZM89.3386 17V11.203H90.6916V17H89.3386ZM89.9986 9.971C89.7272 9.971 89.5146 9.905 89.3606 9.773C89.2139 9.63367 89.1406 9.43933 89.1406 9.19C89.1406 8.95533 89.2176 8.76467 89.3716 8.618C89.5256 8.47133 89.7346 8.398 89.9986 8.398C90.2772 8.398 90.4899 8.46767 90.6366 8.607C90.7906 8.739 90.8676 8.93333 90.8676 9.19C90.8676 9.41733 90.7906 9.60433 90.6366 9.751C90.4826 9.89767 90.2699 9.971 89.9986 9.971ZM92.4072 17V11.203H93.7272L93.7492 12.391L93.4962 12.523C93.5696 12.259 93.7126 12.0207 93.9252 11.808C94.1379 11.588 94.3909 11.412 94.6842 11.28C94.9776 11.148 95.2782 11.082 95.5862 11.082C96.0262 11.082 96.3929 11.17 96.6862 11.346C96.9869 11.522 97.2106 11.786 97.3572 12.138C97.5112 12.49 97.5882 12.93 97.5882 13.458V17H96.2352V13.557C96.2352 13.2637 96.1949 13.0217 96.1142 12.831C96.0336 12.633 95.9089 12.49 95.7402 12.402C95.5716 12.3067 95.3662 12.2627 95.1242 12.27C94.9262 12.27 94.7429 12.303 94.5742 12.369C94.4129 12.4277 94.2699 12.5157 94.1452 12.633C94.0279 12.743 93.9326 12.8713 93.8592 13.018C93.7932 13.1647 93.7602 13.326 93.7602 13.502V17H93.0892C92.9572 17 92.8326 17 92.7152 17C92.6052 17 92.5026 17 92.4072 17ZM101.717 19.53C101.285 19.53 100.852 19.464 100.419 19.332C99.994 19.2 99.6494 19.0277 99.3854 18.815L99.8694 17.814C100.023 17.924 100.196 18.0193 100.386 18.1C100.577 18.1807 100.779 18.243 100.991 18.287C101.211 18.331 101.428 18.353 101.64 18.353C102.029 18.353 102.352 18.2907 102.608 18.166C102.872 18.0487 103.07 17.8653 103.202 17.616C103.334 17.374 103.4 17.0697 103.4 16.703V15.636L103.598 15.713C103.554 15.9403 103.433 16.1567 103.235 16.362C103.037 16.5673 102.788 16.736 102.487 16.868C102.187 16.9927 101.879 17.055 101.563 17.055C101.013 17.055 100.526 16.9267 100.1 16.67C99.6824 16.4133 99.3487 16.065 99.0994 15.625C98.8574 15.1777 98.7364 14.6643 98.7364 14.085C98.7364 13.5057 98.8574 12.9923 99.0994 12.545C99.3414 12.0903 99.6714 11.7347 100.089 11.478C100.515 11.214 100.991 11.082 101.519 11.082C101.739 11.082 101.952 11.1077 102.157 11.159C102.363 11.2103 102.55 11.2837 102.718 11.379C102.894 11.4743 103.052 11.5807 103.191 11.698C103.331 11.8153 103.444 11.94 103.532 12.072C103.62 12.204 103.675 12.3323 103.697 12.457L103.411 12.545L103.433 11.203H104.764V16.604C104.764 17.0733 104.695 17.4877 104.555 17.847C104.416 18.2063 104.214 18.5107 103.95 18.76C103.686 19.0167 103.367 19.2073 102.993 19.332C102.619 19.464 102.194 19.53 101.717 19.53ZM101.772 15.9C102.102 15.9 102.392 15.823 102.641 15.669C102.898 15.515 103.096 15.3023 103.235 15.031C103.375 14.7597 103.444 14.448 103.444 14.096C103.444 13.7367 103.371 13.4213 103.224 13.15C103.085 12.8713 102.891 12.655 102.641 12.501C102.392 12.347 102.102 12.27 101.772 12.27C101.442 12.27 101.153 12.3507 100.903 12.512C100.654 12.666 100.456 12.8823 100.309 13.161C100.17 13.4323 100.1 13.744 100.1 14.096C100.1 14.4407 100.17 14.7523 100.309 15.031C100.456 15.3023 100.654 15.515 100.903 15.669C101.153 15.823 101.442 15.9 101.772 15.9ZM108.158 17.11C107.644 17.11 107.182 17.0257 106.772 16.857C106.368 16.681 106.038 16.4317 105.782 16.109L106.662 15.35C106.882 15.5993 107.127 15.779 107.399 15.889C107.67 15.999 107.956 16.054 108.257 16.054C108.381 16.054 108.491 16.0393 108.587 16.01C108.689 15.9807 108.777 15.9367 108.851 15.878C108.924 15.8193 108.979 15.7533 109.016 15.68C109.06 15.5993 109.082 15.5113 109.082 15.416C109.082 15.24 109.016 15.1007 108.884 14.998C108.81 14.9467 108.693 14.8917 108.532 14.833C108.378 14.7743 108.176 14.7157 107.927 14.657C107.531 14.5543 107.201 14.437 106.937 14.305C106.673 14.1657 106.467 14.0117 106.321 13.843C106.196 13.7037 106.101 13.546 106.035 13.37C105.976 13.194 105.947 13.0033 105.947 12.798C105.947 12.5413 106.002 12.3103 106.112 12.105C106.229 11.8923 106.387 11.709 106.585 11.555C106.783 11.401 107.014 11.2837 107.278 11.203C107.542 11.1223 107.817 11.082 108.103 11.082C108.396 11.082 108.678 11.1187 108.95 11.192C109.228 11.2653 109.485 11.3717 109.72 11.511C109.962 11.643 110.167 11.8043 110.336 11.995L109.588 12.82C109.448 12.688 109.294 12.5707 109.126 12.468C108.964 12.3653 108.799 12.2847 108.631 12.226C108.462 12.16 108.304 12.127 108.158 12.127C108.018 12.127 107.894 12.1417 107.784 12.171C107.674 12.193 107.582 12.2297 107.509 12.281C107.435 12.3323 107.377 12.3983 107.333 12.479C107.296 12.5523 107.278 12.6403 107.278 12.743C107.285 12.831 107.307 12.9153 107.344 12.996C107.388 13.0693 107.446 13.1317 107.52 13.183C107.6 13.2343 107.721 13.293 107.883 13.359C108.044 13.425 108.253 13.4873 108.51 13.546C108.869 13.6413 109.17 13.7477 109.412 13.865C109.654 13.9823 109.844 14.118 109.984 14.272C110.123 14.404 110.222 14.558 110.281 14.734C110.339 14.91 110.369 15.1043 110.369 15.317C110.369 15.6617 110.27 15.9697 110.072 16.241C109.881 16.5123 109.617 16.725 109.28 16.879C108.95 17.033 108.576 17.11 108.158 17.11Z" fill="white" />
            </svg>

        </div>
    </div>
    <div class="purchase-info">
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Onboarding Date
            </span>
            <span class="purchase-details-info">
                @TenantModel?.DateOfOnBoard?.ToLocalTime().ToString("dd/MM/yyyy")
            </span>
        </div>
        @if (EditExpiryDate)
        {
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Edit Expiry Date:
            </span>
            <span class="purchase-details-info">
                    <form id="editForm">
                        <input type="date" id="expiry-date" name="expiryDate" @bind-value="@TenantModel.LicenseValidity">
                        <button type="button" @onclick="( async () => { await EditExpiryDateAsync(TenantModel); await OnInitializedAsync();})">Save</button>
                        <button type="button" class="cancel-btn" @onclick="(async () => {EditExpiryDate = false;})">Cancel</button>
                    </form>
            </span>
            </div>
        }
        else
        {
            <div class="purchase-details">
                <span class="purchase-details-heading">
                    Expiry Date
                </span>
                <span class="purchase-details-info">
                    @TenantModel?.LicenseValidity?.ToLocalTime().ToString("dd/MM/yyyy")
                    <AuthorizeView Roles="Admin">
                        <span @onclick="( async () => { await EditDate();})" class="edit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil" viewBox="0 0 16 16">
                                <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2L2 11.207V13h1.793L14 3.793 11.207 2z" />
                            </svg>
                        </span>
                    </AuthorizeView>
                </span>
            </div>
        }
        <div class="purchase-details">
            <span class="purchase-details-heading">
                IsTestingAccount
            </span>
            <span class="purchase-details-info">
                @if (TestingAccount)
                {
                    <span>true</span>
                }
                else
                {
                    <span>false</span>
                }
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                NoOfUsers
            </span>
            <span class="purchase-details-info">
                @userCounts.TotalUsers
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Users Permitted
            </span>
            <span class="purchase-details-info">
                @UserLimit
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                No Of Admin
            </span>
            <span class="purchase-details-info">
                @AdminCount
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Active Users
            </span>
            <span class="purchase-details-info">
                @userCounts.ActiveUsers
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Inactive Users
            </span>
            <span class="purchase-details-info">
                @userCounts.InActiveUsers
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                IsPaid
            </span>
            <span class="purchase-details-info">
                @(IsPaid() ? "Yes" : "No")
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Total Paid
            </span>
            <span class="purchase-details-info">
                @SubscriptionDetails.TotalPaidAmount
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Total Pending
            </span>
            <span class="purchase-details-info">
                @SubscriptionDetails.TotalDueAmount
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Next Due Date
            </span>
            <span class="purchase-details-info">
                @if (SubscriptionDetails.TotalDueAmount == 0)
                {
                    <span>Completed</span>
                }
                else
                {
                    <span>@SubscriptionDetails.NextOrLastDueDate?.ToString("dd/MM/yyyy")</span>
                }
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Vendors
            </span>
            <span class="purchase-details-info">
                @if (VendorModel != null && !EditAccountManager)
                {
                    <select>
                        @foreach (var vendorName in VendorModel)
                        {
                            <option>@vendorName.FirstName @vendorName.LastName</option>
                        }
                    </select>
                }
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                GST No
            </span>
            <span class="purchase-details-info">
                @ProfileData?.GSTNumber
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Next Payment Amount
            </span>
            <span class="purchase-details-info">
                @SubscriptionDetails.NextOrLastDueAmount
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                MonthlyCost / User :
            </span>
            <span class="purchase-details-info">
                @monthlyCostPerUser
            </span>
        </div>
        <div class="purchase-details">
            <span class="purchase-details-heading">
                Days Left :
            </span>
            <span class="purchase-details-info">
                @leftDays
            </span>
        </div>
        <div class="purchase-details">
            @if (IsExpiryExtended == true)
                    {
            <span class="purchase-details-heading">
                Extended
            </span>
            <span class="purchase-details-info">
                    @if (ExtendedDays > 1)
                    {
                    <span> @ExtendedDays Days</span>
                    }
                    else
                    {
                    <span> @ExtendedDays Day</span>
                    }
            </span>
                    }
        </div>
        
    </div>
</div>

@* down part *@
<div class="crm-container">
    <div class="options-header">
        <div class="options belowBorder">
            <span class="option-option @(CollectionType == CollectionType.Subscription ? "selected" : "")"
                  @onclick="@(async () => {CollectionType = CollectionType.Subscription; await GetDataAsync(CollectionType);})">
                Subscription
            </span>
            <span class="option-option @(CollectionType == CollectionType.User ? "selected" : "")"
                  @onclick="@(async () => {CollectionType = CollectionType.User; await GetDataAsync(CollectionType);})">
                Users
            </span>
            <span class="option-option @(CollectionType == CollectionType.Integration ? "selected" : "")"
                  @onclick="@(async () => {CollectionType = CollectionType.Integration; await GetDataAsync(CollectionType);})">
                Integration
            </span>
            <span class="option-option @(CollectionType == CollectionType.Notification ? "selected" : "")"
                  @onclick="@(async () => {CollectionType = CollectionType.Notification; await GetDataAsync(CollectionType);})">
                Notification
            </span>
        </div>

    </div>
    @if (CollectionType == CollectionType.Subscription)
    {
        <div class="options-header">
            <div class="options">
                <span class="option-option @(SubscriptionFilterType == "All" ? "selected" : "" )" @onclick="ShowAllSubscriptions">All</span>
                <span class="option-option @(SubscriptionFilterType == "Active" ? "selected" : "" )" @onclick="ShowActiveSubscriptions">Active</span>
                <span class="option-option @(SubscriptionFilterType == "Upcoming" ? "selected" : "" )" @onclick="ShowUpcomingSubscription">Upcoming</span>
                <span class="option-option @(SubscriptionFilterType == "Expired" ? "selected" : "" )" @onclick="ShowExpiredSubscription">Expired</span>
                <span class="option-option @(SubscriptionFilterType == "Deleted" ? "selected" : "" )" @onclick="ShowDeletedSubscription">Deleted</span>
            </div>
            <div class="header-buttons">
                @*Add New Subscription*@
                <span class="options-rightside" >
                    <svg width="166" height="33" viewBox="0 0 166 33" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="AddSubscriptionAsync">
                    <rect x="0.5" width="165" height="33" rx="4" fill="#343739" />
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M25.9436 16.4991C25.9436 19.506 23.506 21.9436 20.4991 21.9436C17.4922 21.9436 15.0547 19.506 15.0547 16.4991C15.0547 13.4922 17.4922 11.0547 20.4991 11.0547C23.506 11.0547 25.9436 13.4922 25.9436 16.4991ZM20.4991 13.913C20.7246 13.913 20.9075 14.0958 20.9075 14.3214V16.0908H22.6769C22.9024 16.0908 23.0852 16.2736 23.0852 16.4991C23.0852 16.7246 22.9024 16.9075 22.6769 16.9075H20.9075V18.6769C20.9075 18.9024 20.7246 19.0852 20.4991 19.0852C20.2736 19.0852 20.0908 18.9024 20.0908 18.6769V16.9075H18.3214C18.0958 16.9075 17.913 16.7246 17.913 16.4991C17.913 16.2736 18.0958 16.0908 18.3214 16.0908H20.0908V14.3214C20.0908 14.0958 20.2736 13.913 20.4991 13.913Z" fill="white" />
                    <path d="M31.676 20.5L34.679 12.8H36.021L39.002 20.5H37.517L35.867 16.122C35.8303 16.034 35.779 15.8947 35.713 15.704C35.6543 15.5133 35.5883 15.308 35.515 15.088C35.4417 14.8607 35.3757 14.6517 35.317 14.461C35.2583 14.263 35.2143 14.12 35.185 14.032L35.46 14.021C35.416 14.1677 35.3647 14.3327 35.306 14.516C35.2473 14.6993 35.185 14.89 35.119 15.088C35.053 15.286 34.987 15.4767 34.921 15.66C34.8623 15.8433 34.8073 16.0083 34.756 16.155L33.106 20.5H31.676ZM33.04 18.74L33.535 17.497H37.044L37.572 18.74H33.04ZM42.0898 20.61C41.5765 20.61 41.1145 20.4817 40.7038 20.225C40.3005 19.961 39.9778 19.6053 39.7358 19.158C39.5012 18.7033 39.3838 18.1827 39.3838 17.596C39.3838 17.0167 39.5012 16.4997 39.7358 16.045C39.9705 15.5903 40.2895 15.2347 40.6928 14.978C41.1035 14.714 41.5655 14.582 42.0788 14.582C42.3575 14.582 42.6252 14.626 42.8818 14.714C43.1458 14.802 43.3805 14.923 43.5858 15.077C43.7912 15.2237 43.9525 15.3887 44.0698 15.572C44.1945 15.7553 44.2642 15.946 44.2788 16.144L43.9158 16.188V12.36H45.2688V20.5H43.9598L43.9268 19.136L44.1908 19.158C44.1835 19.3413 44.1175 19.5173 43.9928 19.686C43.8755 19.8547 43.7215 20.0087 43.5308 20.148C43.3402 20.2873 43.1165 20.401 42.8598 20.489C42.6105 20.5697 42.3538 20.61 42.0898 20.61ZM42.3538 19.466C42.6692 19.466 42.9478 19.3853 43.1898 19.224C43.4318 19.0627 43.6188 18.8427 43.7508 18.564C43.8902 18.2853 43.9598 17.9627 43.9598 17.596C43.9598 17.2367 43.8902 16.9177 43.7508 16.639C43.6188 16.353 43.4318 16.133 43.1898 15.979C42.9478 15.8177 42.6692 15.737 42.3538 15.737C42.0385 15.737 41.7598 15.8177 41.5178 15.979C41.2758 16.133 41.0852 16.353 40.9458 16.639C40.8138 16.9177 40.7478 17.2367 40.7478 17.596C40.7478 17.9627 40.8138 18.2853 40.9458 18.564C41.0852 18.8427 41.2758 19.0627 41.5178 19.224C41.7598 19.3853 42.0385 19.466 42.3538 19.466ZM49.1367 20.61C48.6234 20.61 48.1614 20.4817 47.7507 20.225C47.3474 19.961 47.0247 19.6053 46.7827 19.158C46.548 18.7033 46.4307 18.1827 46.4307 17.596C46.4307 17.0167 46.548 16.4997 46.7827 16.045C47.0174 15.5903 47.3364 15.2347 47.7397 14.978C48.1504 14.714 48.6124 14.582 49.1257 14.582C49.4044 14.582 49.672 14.626 49.9287 14.714C50.1927 14.802 50.4274 14.923 50.6327 15.077C50.838 15.2237 50.9994 15.3887 51.1167 15.572C51.2414 15.7553 51.311 15.946 51.3257 16.144L50.9627 16.188V12.36H52.3157V20.5H51.0067L50.9737 19.136L51.2377 19.158C51.2304 19.3413 51.1644 19.5173 51.0397 19.686C50.9224 19.8547 50.7684 20.0087 50.5777 20.148C50.387 20.2873 50.1634 20.401 49.9067 20.489C49.6574 20.5697 49.4007 20.61 49.1367 20.61ZM49.4007 19.466C49.716 19.466 49.9947 19.3853 50.2367 19.224C50.4787 19.0627 50.6657 18.8427 50.7977 18.564C50.937 18.2853 51.0067 17.9627 51.0067 17.596C51.0067 17.2367 50.937 16.9177 50.7977 16.639C50.6657 16.353 50.4787 16.133 50.2367 15.979C49.9947 15.8177 49.716 15.737 49.4007 15.737C49.0854 15.737 48.8067 15.8177 48.5647 15.979C48.3227 16.133 48.132 16.353 47.9927 16.639C47.8607 16.9177 47.7947 17.2367 47.7947 17.596C47.7947 17.9627 47.8607 18.2853 47.9927 18.564C48.132 18.8427 48.3227 19.0627 48.5647 19.224C48.8067 19.3853 49.0854 19.466 49.4007 19.466ZM57.327 20.5V12.8H58.614L63.08 18.817L62.838 18.861C62.8086 18.6557 62.783 18.4467 62.761 18.234C62.739 18.014 62.717 17.7867 62.695 17.552C62.6803 17.3173 62.6656 17.0717 62.651 16.815C62.6436 16.5583 62.6363 16.2907 62.629 16.012C62.6216 15.726 62.618 15.4253 62.618 15.11V12.8H64.037V20.5H62.728L58.24 14.571L58.526 14.494C58.5626 14.9047 58.592 15.2567 58.614 15.55C58.6433 15.836 58.6653 16.0853 58.68 16.298C58.6946 16.5033 58.7056 16.6757 58.713 16.815C58.7276 16.9543 58.735 17.0827 58.735 17.2C58.7423 17.31 58.746 17.4163 58.746 17.519V20.5H57.327ZM68.5783 20.61C67.9697 20.61 67.4307 20.4817 66.9613 20.225C66.4993 19.9683 66.1363 19.62 65.8723 19.18C65.6157 18.7327 65.4873 18.223 65.4873 17.651C65.4873 17.1963 65.5607 16.782 65.7073 16.408C65.854 16.034 66.0557 15.7113 66.3123 15.44C66.5763 15.1613 66.888 14.9487 67.2473 14.802C67.614 14.648 68.0137 14.571 68.4463 14.571C68.8277 14.571 69.1833 14.6443 69.5133 14.791C69.8433 14.9377 70.1293 15.1393 70.3713 15.396C70.6133 15.6453 70.7967 15.946 70.9213 16.298C71.0533 16.6427 71.1157 17.0203 71.1083 17.431L71.0973 17.904H66.4003L66.1473 16.98H69.9643L69.7883 17.167V16.925C69.7663 16.6977 69.693 16.4997 69.5683 16.331C69.4437 16.155 69.2823 16.0193 69.0843 15.924C68.8937 15.8213 68.681 15.77 68.4463 15.77C68.087 15.77 67.7827 15.8397 67.5333 15.979C67.2913 16.1183 67.108 16.32 66.9833 16.584C66.8587 16.8407 66.7963 17.1633 66.7963 17.552C66.7963 17.926 66.8733 18.2523 67.0273 18.531C67.1887 18.8097 67.4123 19.026 67.6983 19.18C67.9917 19.3267 68.3327 19.4 68.7213 19.4C68.9927 19.4 69.242 19.356 69.4693 19.268C69.6967 19.18 69.9423 19.0223 70.2063 18.795L70.8773 19.73C70.6793 19.9133 70.452 20.071 70.1953 20.203C69.946 20.3277 69.682 20.4267 69.4033 20.5C69.1247 20.5733 68.8497 20.61 68.5783 20.61ZM73.5632 20.5L71.6162 14.703L73.0022 14.692L74.3002 18.828L74.0582 18.806L75.4662 15.572H76.2802L77.7212 18.806L77.4352 18.839L78.7442 14.703H80.1302L78.1722 20.5H77.2042L75.7192 17.024L75.9282 17.046L74.5202 20.5H73.5632ZM87.0434 20.61C86.596 20.61 86.1854 20.555 85.8114 20.445C85.4374 20.3277 85.1 20.1553 84.7994 19.928C84.4987 19.7007 84.231 19.4257 83.9964 19.103L84.9314 18.047C85.2907 18.5457 85.6427 18.8903 85.9874 19.081C86.332 19.2717 86.7207 19.367 87.1534 19.367C87.4027 19.367 87.63 19.3303 87.8354 19.257C88.0407 19.1763 88.202 19.07 88.3194 18.938C88.4367 18.7987 88.4954 18.641 88.4954 18.465C88.4954 18.3403 88.4697 18.2267 88.4184 18.124C88.3744 18.014 88.3047 17.9187 88.2094 17.838C88.114 17.75 87.9967 17.6693 87.8574 17.596C87.718 17.5227 87.5604 17.4603 87.3844 17.409C87.2084 17.3577 87.014 17.31 86.8014 17.266C86.398 17.1853 86.046 17.079 85.7454 16.947C85.4447 16.8077 85.1917 16.639 84.9864 16.441C84.781 16.2357 84.6307 16.0083 84.5354 15.759C84.44 15.5023 84.3924 15.2127 84.3924 14.89C84.3924 14.5673 84.462 14.2703 84.6014 13.999C84.748 13.7277 84.946 13.493 85.1954 13.295C85.4447 13.097 85.7344 12.943 86.0644 12.833C86.3944 12.723 86.75 12.668 87.1314 12.668C87.564 12.668 87.949 12.7193 88.2864 12.822C88.631 12.9247 88.9317 13.0787 89.1884 13.284C89.4524 13.482 89.6687 13.724 89.8374 14.01L88.8914 14.945C88.7447 14.7177 88.5797 14.5307 88.3964 14.384C88.213 14.23 88.015 14.1163 87.8024 14.043C87.5897 13.9623 87.366 13.922 87.1314 13.922C86.8674 13.922 86.6364 13.9587 86.4384 14.032C86.2477 14.1053 86.0974 14.2117 85.9874 14.351C85.8774 14.483 85.8224 14.6443 85.8224 14.835C85.8224 14.9817 85.8554 15.1137 85.9214 15.231C85.9874 15.341 86.079 15.44 86.1964 15.528C86.321 15.616 86.4787 15.693 86.6694 15.759C86.86 15.825 87.0764 15.8837 87.3184 15.935C87.7217 16.0157 88.0847 16.1257 88.4074 16.265C88.73 16.397 89.005 16.5583 89.2324 16.749C89.4597 16.9323 89.632 17.145 89.7494 17.387C89.8667 17.6217 89.9254 17.8857 89.9254 18.179C89.9254 18.685 89.8044 19.1213 89.5624 19.488C89.3277 19.8473 88.994 20.126 88.5614 20.324C88.1287 20.5147 87.6227 20.61 87.0434 20.61ZM93.2689 20.61C92.8509 20.61 92.4879 20.5183 92.1799 20.335C91.8719 20.1443 91.6372 19.8767 91.4759 19.532C91.3145 19.18 91.2339 18.762 91.2339 18.278V14.703H92.5869V17.992C92.5869 18.3 92.6345 18.564 92.7299 18.784C92.8252 18.9967 92.9645 19.1617 93.1479 19.279C93.3312 19.3963 93.5512 19.455 93.8079 19.455C93.9985 19.455 94.1709 19.4257 94.3249 19.367C94.4862 19.301 94.6255 19.213 94.7429 19.103C94.8602 18.9857 94.9519 18.85 95.0179 18.696C95.0839 18.5347 95.1169 18.3623 95.1169 18.179V14.703H96.4699V20.5H95.1499L95.1279 19.29L95.3699 19.158C95.2819 19.4367 95.1315 19.686 94.9189 19.906C94.7135 20.1187 94.4679 20.291 94.1819 20.423C93.8959 20.5477 93.5915 20.61 93.2689 20.61ZM101.257 20.61C101 20.61 100.747 20.5733 100.498 20.5C100.256 20.4193 100.036 20.313 99.8377 20.181C99.6397 20.049 99.4747 19.9023 99.3427 19.741C99.2107 19.5723 99.1227 19.4037 99.0787 19.235L99.3977 19.092L99.3647 20.478H98.0557V12.36H99.4087V16.045L99.1667 15.935C99.2033 15.759 99.284 15.594 99.4087 15.44C99.5407 15.2787 99.702 15.1357 99.8927 15.011C100.083 14.879 100.292 14.7763 100.52 14.703C100.747 14.6223 100.978 14.582 101.213 14.582C101.733 14.582 102.192 14.7103 102.588 14.967C102.991 15.2237 103.306 15.5793 103.534 16.034C103.768 16.4887 103.886 17.0057 103.886 17.585C103.886 18.1717 103.772 18.6923 103.545 19.147C103.317 19.6017 103.002 19.961 102.599 20.225C102.203 20.4817 101.755 20.61 101.257 20.61ZM100.971 19.422C101.279 19.422 101.554 19.345 101.796 19.191C102.038 19.0297 102.228 18.8133 102.368 18.542C102.507 18.2633 102.577 17.9443 102.577 17.585C102.577 17.233 102.507 16.9213 102.368 16.65C102.236 16.3787 102.049 16.166 101.807 16.012C101.565 15.858 101.286 15.781 100.971 15.781C100.655 15.781 100.377 15.858 100.135 16.012C99.8927 16.166 99.702 16.3787 99.5627 16.65C99.4233 16.9213 99.3537 17.233 99.3537 17.585C99.3537 17.9443 99.4233 18.2633 99.5627 18.542C99.702 18.8133 99.8927 19.0297 100.135 19.191C100.377 19.345 100.655 19.422 100.971 19.422ZM107.028 20.61C106.514 20.61 106.052 20.5257 105.642 20.357C105.238 20.181 104.908 19.9317 104.652 19.609L105.532 18.85C105.752 19.0993 105.997 19.279 106.269 19.389C106.54 19.499 106.826 19.554 107.127 19.554C107.251 19.554 107.361 19.5393 107.457 19.51C107.559 19.4807 107.647 19.4367 107.721 19.378C107.794 19.3193 107.849 19.2533 107.886 19.18C107.93 19.0993 107.952 19.0113 107.952 18.916C107.952 18.74 107.886 18.6007 107.754 18.498C107.68 18.4467 107.563 18.3917 107.402 18.333C107.248 18.2743 107.046 18.2157 106.797 18.157C106.401 18.0543 106.071 17.937 105.807 17.805C105.543 17.6657 105.337 17.5117 105.191 17.343C105.066 17.2037 104.971 17.046 104.905 16.87C104.846 16.694 104.817 16.5033 104.817 16.298C104.817 16.0413 104.872 15.8103 104.982 15.605C105.099 15.3923 105.257 15.209 105.455 15.055C105.653 14.901 105.884 14.7837 106.148 14.703C106.412 14.6223 106.687 14.582 106.973 14.582C107.266 14.582 107.548 14.6187 107.82 14.692C108.098 14.7653 108.355 14.8717 108.59 15.011C108.832 15.143 109.037 15.3043 109.206 15.495L108.458 16.32C108.318 16.188 108.164 16.0707 107.996 15.968C107.834 15.8653 107.669 15.7847 107.501 15.726C107.332 15.66 107.174 15.627 107.028 15.627C106.888 15.627 106.764 15.6417 106.654 15.671C106.544 15.693 106.452 15.7297 106.379 15.781C106.305 15.8323 106.247 15.8983 106.203 15.979C106.166 16.0523 106.148 16.1403 106.148 16.243C106.155 16.331 106.177 16.4153 106.214 16.496C106.258 16.5693 106.316 16.6317 106.39 16.683C106.47 16.7343 106.591 16.793 106.753 16.859C106.914 16.925 107.123 16.9873 107.38 17.046C107.739 17.1413 108.04 17.2477 108.282 17.365C108.524 17.4823 108.714 17.618 108.854 17.772C108.993 17.904 109.092 18.058 109.151 18.234C109.209 18.41 109.239 18.6043 109.239 18.817C109.239 19.1617 109.14 19.4697 108.942 19.741C108.751 20.0123 108.487 20.225 108.15 20.379C107.82 20.533 107.446 20.61 107.028 20.61ZM112.917 20.61C112.374 20.61 111.886 20.478 111.454 20.214C111.021 19.95 110.68 19.5907 110.431 19.136C110.181 18.6813 110.057 18.168 110.057 17.596C110.057 17.024 110.181 16.5107 110.431 16.056C110.68 15.6013 111.021 15.242 111.454 14.978C111.886 14.714 112.374 14.582 112.917 14.582C113.437 14.582 113.91 14.681 114.336 14.879C114.761 15.077 115.091 15.352 115.326 15.704L114.578 16.606C114.468 16.452 114.328 16.3127 114.16 16.188C113.991 16.0633 113.811 15.9643 113.621 15.891C113.43 15.8177 113.239 15.781 113.049 15.781C112.726 15.781 112.436 15.8617 112.18 16.023C111.93 16.177 111.732 16.3933 111.586 16.672C111.439 16.9433 111.366 17.2513 111.366 17.596C111.366 17.9407 111.439 18.2487 111.586 18.52C111.74 18.7913 111.945 19.0077 112.202 19.169C112.458 19.3303 112.744 19.411 113.06 19.411C113.25 19.411 113.434 19.3817 113.61 19.323C113.793 19.257 113.965 19.1653 114.127 19.048C114.288 18.9307 114.438 18.784 114.578 18.608L115.326 19.521C115.076 19.8437 114.732 20.1077 114.292 20.313C113.859 20.511 113.401 20.61 112.917 20.61ZM116.36 20.5V14.703H117.691L117.713 16.551L117.526 16.133C117.607 15.8397 117.746 15.5757 117.944 15.341C118.142 15.1063 118.37 14.923 118.626 14.791C118.89 14.6517 119.165 14.582 119.451 14.582C119.576 14.582 119.693 14.593 119.803 14.615C119.921 14.637 120.016 14.6627 120.089 14.692L119.726 16.177C119.646 16.133 119.547 16.0963 119.429 16.067C119.312 16.0377 119.195 16.023 119.077 16.023C118.894 16.023 118.718 16.0597 118.549 16.133C118.388 16.199 118.245 16.2943 118.12 16.419C117.996 16.5437 117.897 16.6903 117.823 16.859C117.757 17.0203 117.724 17.2037 117.724 17.409V20.5H116.36ZM121.144 20.5V14.703H122.497V20.5H121.144ZM121.804 13.471C121.533 13.471 121.32 13.405 121.166 13.273C121.02 13.1337 120.946 12.9393 120.946 12.69C120.946 12.4553 121.023 12.2647 121.177 12.118C121.331 11.9713 121.54 11.898 121.804 11.898C122.083 11.898 122.296 11.9677 122.442 12.107C122.596 12.239 122.673 12.4333 122.673 12.69C122.673 12.9173 122.596 13.1043 122.442 13.251C122.288 13.3977 122.076 13.471 121.804 13.471ZM124.213 22.92V14.703H125.544L125.566 16.023L125.335 15.924C125.379 15.6967 125.504 15.484 125.709 15.286C125.914 15.0807 126.164 14.9157 126.457 14.791C126.758 14.6663 127.069 14.604 127.392 14.604C127.905 14.604 128.36 14.7323 128.756 14.989C129.159 15.2457 129.475 15.5977 129.702 16.045C129.937 16.4923 130.054 17.0093 130.054 17.596C130.054 18.1753 129.937 18.6923 129.702 19.147C129.475 19.5943 129.159 19.95 128.756 20.214C128.36 20.4707 127.913 20.599 127.414 20.599C127.077 20.599 126.75 20.533 126.435 20.401C126.127 20.269 125.863 20.0967 125.643 19.884C125.43 19.6713 125.298 19.4477 125.247 19.213L125.577 19.059V22.92H124.213ZM127.139 19.4C127.447 19.4 127.722 19.323 127.964 19.169C128.206 19.015 128.393 18.8023 128.525 18.531C128.664 18.2597 128.734 17.948 128.734 17.596C128.734 17.244 128.668 16.936 128.536 16.672C128.404 16.4007 128.217 16.188 127.975 16.034C127.733 15.88 127.454 15.803 127.139 15.803C126.816 15.803 126.534 15.88 126.292 16.034C126.057 16.1807 125.87 16.3897 125.731 16.661C125.592 16.9323 125.522 17.244 125.522 17.596C125.522 17.948 125.592 18.2597 125.731 18.531C125.87 18.8023 126.057 19.015 126.292 19.169C126.534 19.323 126.816 19.4 127.139 19.4ZM131.887 20.5V13.229H133.24V20.5H131.887ZM130.743 15.957V14.703H134.505V15.957H130.743ZM135.689 20.5V14.703H137.042V20.5H135.689ZM136.349 13.471C136.078 13.471 135.865 13.405 135.711 13.273C135.564 13.1337 135.491 12.9393 135.491 12.69C135.491 12.4553 135.568 12.2647 135.722 12.118C135.876 11.9713 136.085 11.898 136.349 11.898C136.628 11.898 136.84 11.9677 136.987 12.107C137.141 12.239 137.218 12.4333 137.218 12.69C137.218 12.9173 137.141 13.1043 136.987 13.251C136.833 13.3977 136.62 13.471 136.349 13.471ZM141.409 20.61C140.829 20.61 140.312 20.4817 139.858 20.225C139.403 19.961 139.044 19.6017 138.78 19.147C138.516 18.6923 138.384 18.1753 138.384 17.596C138.384 17.0167 138.516 16.4997 138.78 16.045C139.044 15.5903 139.403 15.2347 139.858 14.978C140.312 14.714 140.829 14.582 141.409 14.582C141.981 14.582 142.494 14.714 142.949 14.978C143.403 15.2347 143.759 15.5903 144.016 16.045C144.28 16.4997 144.412 17.0167 144.412 17.596C144.412 18.1753 144.28 18.6923 144.016 19.147C143.759 19.6017 143.403 19.961 142.949 20.225C142.494 20.4817 141.981 20.61 141.409 20.61ZM141.409 19.411C141.724 19.411 142.003 19.3303 142.245 19.169C142.494 19.0077 142.688 18.7913 142.828 18.52C142.974 18.2487 143.044 17.9407 143.037 17.596C143.044 17.244 142.974 16.9323 142.828 16.661C142.688 16.3897 142.494 16.177 142.245 16.023C142.003 15.8617 141.724 15.781 141.409 15.781C141.093 15.781 140.807 15.8617 140.551 16.023C140.301 16.177 140.107 16.3933 139.968 16.672C139.828 16.9433 139.759 17.2513 139.759 17.596C139.759 17.9407 139.828 18.2487 139.968 18.52C140.107 18.7913 140.301 19.0077 140.551 19.169C140.807 19.3303 141.093 19.411 141.409 19.411ZM145.622 20.5V14.703H146.942L146.964 15.891L146.711 16.023C146.784 15.759 146.927 15.5207 147.14 15.308C147.353 15.088 147.606 14.912 147.899 14.78C148.192 14.648 148.493 14.582 148.801 14.582C149.241 14.582 149.608 14.67 149.901 14.846C150.202 15.022 150.425 15.286 150.572 15.638C150.726 15.99 150.803 16.43 150.803 16.958V20.5H149.45V17.057C149.45 16.7637 149.41 16.5217 149.329 16.331C149.248 16.133 149.124 15.99 148.955 15.902C148.786 15.8067 148.581 15.7627 148.339 15.77C148.141 15.77 147.958 15.803 147.789 15.869C147.628 15.9277 147.485 16.0157 147.36 16.133C147.243 16.243 147.147 16.3713 147.074 16.518C147.008 16.6647 146.975 16.826 146.975 17.002V20.5H146.304C146.172 20.5 146.047 20.5 145.93 20.5C145.82 20.5 145.717 20.5 145.622 20.5Z" fill="white" />
                </svg>
                </span>
                <span class="options-rightside" @onclick="@(async (e) => {Filter = new(); Paginate(1); await GetSubscriptionAsync();})">
                @*Refresh*@
                <svg width="87" height="33" viewBox="0 0 87 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" width="86" height="33" rx="4" fill="#343739" />
                    <path d="M18.5092 20.719C18.5851 20.7552 18.6681 20.7739 18.7522 20.7737C18.8815 20.7737 19.007 20.7296 19.108 20.6488C19.2089 20.568 19.2794 20.4552 19.3077 20.329C19.336 20.2028 19.3205 20.0708 19.2637 19.9546C19.207 19.8383 19.1123 19.7449 18.9954 19.6897C18.2721 19.3485 17.6861 18.7718 17.3336 18.054C16.981 17.3361 16.8828 16.5198 17.055 15.7388C17.2271 14.9579 17.6595 14.2586 18.2813 13.7555C18.6464 13.4601 19.0641 13.2427 19.5088 13.1124L19.081 13.968C19.0377 14.0547 19.0172 14.1511 19.0216 14.2479C19.026 14.3448 19.055 14.4389 19.1059 14.5214C19.1569 14.6039 19.2281 14.6719 19.3127 14.7192C19.3973 14.7665 19.4927 14.7913 19.5896 14.7914C19.6955 14.7918 19.7993 14.7626 19.8894 14.7069C19.9795 14.6513 20.0521 14.5715 20.0991 14.4766L21.0097 12.6555C21.0772 12.5205 21.0882 12.3642 21.0404 12.221C20.9927 12.0779 20.89 11.9595 20.7549 11.8921C20.6715 11.8504 20.58 11.8303 20.4884 11.8322C20.4822 11.8321 20.476 11.832 20.4697 11.832C19.4121 11.839 18.3881 12.2051 17.5658 12.8703C16.7434 13.5355 16.1714 14.4604 15.9436 15.4932C15.7157 16.5261 15.8455 17.6058 16.3116 18.5552C16.7778 19.5047 17.5527 20.2676 18.5092 20.719Z" fill="white" />
                    <path d="M20.5157 21.1652C20.5198 21.1653 20.5239 21.1653 20.528 21.1653H20.5315C21.5892 21.1583 22.6132 20.7922 23.4355 20.127C24.2579 19.4618 24.8299 18.537 25.0577 17.5041C25.2856 16.4712 25.1558 15.3916 24.6897 14.4421C24.2235 13.4927 23.4487 12.7298 22.4921 12.2784C22.4163 12.2421 22.3333 12.2233 22.2493 12.2233C22.1414 12.2237 22.0357 12.2546 21.9446 12.3124C21.8534 12.3702 21.7804 12.4526 21.734 12.5501C21.7021 12.6176 21.6839 12.6908 21.6803 12.7655C21.6767 12.8401 21.6879 12.9147 21.7132 12.985C21.7384 13.0553 21.7773 13.12 21.8276 13.1753C21.8778 13.2306 21.9385 13.2754 22.0061 13.3072C22.7294 13.6485 23.3153 14.2253 23.6678 14.9432C24.0203 15.661 24.1185 16.4773 23.9463 17.2583C23.7741 18.0393 23.3417 18.7386 22.72 19.2417C22.3549 19.5371 21.9372 19.7546 21.4926 19.8848L21.9203 19.0295C21.9877 18.8944 21.9988 18.7382 21.951 18.595C21.9032 18.4518 21.8005 18.3335 21.6655 18.2661C21.5305 18.1986 21.3742 18.1876 21.2311 18.2353C21.0879 18.2831 20.9696 18.3858 20.9021 18.5208L19.9916 20.342C19.9482 20.4287 19.9278 20.525 19.9322 20.6219C19.9365 20.7187 19.9656 20.8129 20.0165 20.8953C20.0675 20.9778 20.1386 21.0459 20.2233 21.0932C20.3079 21.1404 20.4032 21.1653 20.5002 21.1654C20.5053 21.1654 20.5105 21.1654 20.5157 21.1652Z" fill="white" />
                    <path d="M32.534 20.5V12.8H35.878C36.34 12.8 36.7617 12.91 37.143 13.13C37.5243 13.3427 37.825 13.636 38.045 14.01C38.2723 14.3767 38.386 14.791 38.386 15.253C38.386 15.693 38.2723 16.1 38.045 16.474C37.825 16.8407 37.5243 17.134 37.143 17.354C36.769 17.5667 36.3473 17.673 35.878 17.673H33.931V20.5H32.534ZM37 20.5L35.042 17.024L36.516 16.749L38.694 20.511L37 20.5ZM33.931 16.43H35.889C36.1017 16.43 36.285 16.3823 36.439 16.287C36.6003 16.1843 36.725 16.045 36.813 15.869C36.901 15.693 36.945 15.4987 36.945 15.286C36.945 15.044 36.89 14.835 36.78 14.659C36.67 14.483 36.516 14.3437 36.318 14.241C36.12 14.1383 35.8927 14.087 35.636 14.087H33.931V16.43ZM42.4641 20.61C41.8554 20.61 41.3164 20.4817 40.8471 20.225C40.3851 19.9683 40.0221 19.62 39.7581 19.18C39.5014 18.7327 39.3731 18.223 39.3731 17.651C39.3731 17.1963 39.4464 16.782 39.5931 16.408C39.7397 16.034 39.9414 15.7113 40.1981 15.44C40.4621 15.1613 40.7737 14.9487 41.1331 14.802C41.4997 14.648 41.8994 14.571 42.3321 14.571C42.7134 14.571 43.0691 14.6443 43.3991 14.791C43.7291 14.9377 44.0151 15.1393 44.2571 15.396C44.4991 15.6453 44.6824 15.946 44.8071 16.298C44.9391 16.6427 45.0014 17.0203 44.9941 17.431L44.9831 17.904H40.2861L40.0331 16.98H43.8501L43.6741 17.167V16.925C43.6521 16.6977 43.5787 16.4997 43.4541 16.331C43.3294 16.155 43.1681 16.0193 42.9701 15.924C42.7794 15.8213 42.5667 15.77 42.3321 15.77C41.9727 15.77 41.6684 15.8397 41.4191 15.979C41.1771 16.1183 40.9937 16.32 40.8691 16.584C40.7444 16.8407 40.6821 17.1633 40.6821 17.552C40.6821 17.926 40.7591 18.2523 40.9131 18.531C41.0744 18.8097 41.2981 19.026 41.5841 19.18C41.8774 19.3267 42.2184 19.4 42.6071 19.4C42.8784 19.4 43.1277 19.356 43.3551 19.268C43.5824 19.18 43.8281 19.0223 44.0921 18.795L44.7631 19.73C44.5651 19.9133 44.3377 20.071 44.0811 20.203C43.8317 20.3277 43.5677 20.4267 43.2891 20.5C43.0104 20.5733 42.7354 20.61 42.4641 20.61ZM46.7204 20.5V14.186C46.7204 13.834 46.7974 13.5223 46.9514 13.251C47.1054 12.9723 47.3181 12.756 47.5894 12.602C47.8607 12.4407 48.1761 12.36 48.5354 12.36C48.7847 12.36 49.0157 12.404 49.2284 12.492C49.4411 12.5727 49.6207 12.6863 49.7674 12.833L49.3494 13.856C49.2541 13.7753 49.1514 13.713 49.0414 13.669C48.9387 13.625 48.8397 13.603 48.7444 13.603C48.5977 13.603 48.4731 13.6287 48.3704 13.68C48.2751 13.724 48.2017 13.7937 48.1504 13.889C48.1064 13.9843 48.0844 14.098 48.0844 14.23V20.5H47.4024C47.2704 20.5 47.1457 20.5 47.0284 20.5C46.9184 20.5 46.8157 20.5 46.7204 20.5ZM45.8184 16.012V14.824H49.3934V16.012H45.8184ZM50.5108 20.5V14.703H51.8418L51.8638 16.551L51.6768 16.133C51.7574 15.8397 51.8968 15.5757 52.0948 15.341C52.2928 15.1063 52.5201 14.923 52.7768 14.791C53.0408 14.6517 53.3158 14.582 53.6018 14.582C53.7264 14.582 53.8438 14.593 53.9538 14.615C54.0711 14.637 54.1664 14.6627 54.2398 14.692L53.8768 16.177C53.7961 16.133 53.6971 16.0963 53.5798 16.067C53.4624 16.0377 53.3451 16.023 53.2278 16.023C53.0444 16.023 52.8684 16.0597 52.6998 16.133C52.5384 16.199 52.3954 16.2943 52.2708 16.419C52.1461 16.5437 52.0471 16.6903 51.9738 16.859C51.9078 17.0203 51.8748 17.2037 51.8748 17.409V20.5H50.5108ZM57.6106 20.61C57.0019 20.61 56.4629 20.4817 55.9936 20.225C55.5316 19.9683 55.1686 19.62 54.9046 19.18C54.6479 18.7327 54.5196 18.223 54.5196 17.651C54.5196 17.1963 54.5929 16.782 54.7396 16.408C54.8862 16.034 55.0879 15.7113 55.3446 15.44C55.6086 15.1613 55.9202 14.9487 56.2796 14.802C56.6462 14.648 57.0459 14.571 57.4786 14.571C57.8599 14.571 58.2156 14.6443 58.5456 14.791C58.8756 14.9377 59.1616 15.1393 59.4036 15.396C59.6456 15.6453 59.8289 15.946 59.9536 16.298C60.0856 16.6427 60.1479 17.0203 60.1406 17.431L60.1296 17.904H55.4326L55.1796 16.98H58.9966L58.8206 17.167V16.925C58.7986 16.6977 58.7252 16.4997 58.6006 16.331C58.4759 16.155 58.3146 16.0193 58.1166 15.924C57.9259 15.8213 57.7132 15.77 57.4786 15.77C57.1192 15.77 56.8149 15.8397 56.5656 15.979C56.3236 16.1183 56.1402 16.32 56.0156 16.584C55.8909 16.8407 55.8286 17.1633 55.8286 17.552C55.8286 17.926 55.9056 18.2523 56.0596 18.531C56.2209 18.8097 56.4446 19.026 56.7306 19.18C57.0239 19.3267 57.3649 19.4 57.7536 19.4C58.0249 19.4 58.2742 19.356 58.5016 19.268C58.7289 19.18 58.9746 19.0223 59.2386 18.795L59.9096 19.73C59.7116 19.9133 59.4842 20.071 59.2276 20.203C58.9782 20.3277 58.7142 20.4267 58.4356 20.5C58.1569 20.5733 57.8819 20.61 57.6106 20.61ZM63.2749 20.61C62.7615 20.61 62.2995 20.5257 61.8889 20.357C61.4855 20.181 61.1555 19.9317 60.8989 19.609L61.7789 18.85C61.9989 19.0993 62.2445 19.279 62.5159 19.389C62.7872 19.499 63.0732 19.554 63.3739 19.554C63.4985 19.554 63.6085 19.5393 63.7039 19.51C63.8065 19.4807 63.8945 19.4367 63.9679 19.378C64.0412 19.3193 64.0962 19.2533 64.1329 19.18C64.1769 19.0993 64.1989 19.0113 64.1989 18.916C64.1989 18.74 64.1329 18.6007 64.0009 18.498C63.9275 18.4467 63.8102 18.3917 63.6489 18.333C63.4949 18.2743 63.2932 18.2157 63.0439 18.157C62.6479 18.0543 62.3179 17.937 62.0539 17.805C61.7899 17.6657 61.5845 17.5117 61.4379 17.343C61.3132 17.2037 61.2179 17.046 61.1519 16.87C61.0932 16.694 61.0639 16.5033 61.0639 16.298C61.0639 16.0413 61.1189 15.8103 61.2289 15.605C61.3462 15.3923 61.5039 15.209 61.7019 15.055C61.8999 14.901 62.1309 14.7837 62.3949 14.703C62.6589 14.6223 62.9339 14.582 63.2199 14.582C63.5132 14.582 63.7955 14.6187 64.0669 14.692C64.3455 14.7653 64.6022 14.8717 64.8369 15.011C65.0789 15.143 65.2842 15.3043 65.4529 15.495L64.7049 16.32C64.5655 16.188 64.4115 16.0707 64.2429 15.968C64.0815 15.8653 63.9165 15.7847 63.7479 15.726C63.5792 15.66 63.4215 15.627 63.2749 15.627C63.1355 15.627 63.0109 15.6417 62.9009 15.671C62.7909 15.693 62.6992 15.7297 62.6259 15.781C62.5525 15.8323 62.4939 15.8983 62.4499 15.979C62.4132 16.0523 62.3949 16.1403 62.3949 16.243C62.4022 16.331 62.4242 16.4153 62.4609 16.496C62.5049 16.5693 62.5635 16.6317 62.6369 16.683C62.7175 16.7343 62.8385 16.793 62.9999 16.859C63.1612 16.925 63.3702 16.9873 63.6269 17.046C63.9862 17.1413 64.2869 17.2477 64.5289 17.365C64.7709 17.4823 64.9615 17.618 65.1009 17.772C65.2402 17.904 65.3392 18.058 65.3979 18.234C65.4565 18.41 65.4859 18.6043 65.4859 18.817C65.4859 19.1617 65.3869 19.4697 65.1889 19.741C64.9982 20.0123 64.7342 20.225 64.3969 20.379C64.0669 20.533 63.6929 20.61 63.2749 20.61ZM66.6777 20.5V12.36H68.0197V15.891L67.7667 16.023C67.8401 15.759 67.9831 15.5207 68.1957 15.308C68.4084 15.088 68.6614 14.912 68.9547 14.78C69.2481 14.648 69.5487 14.582 69.8567 14.582C70.2967 14.582 70.6634 14.67 70.9567 14.846C71.2574 15.022 71.4811 15.286 71.6277 15.638C71.7817 15.99 71.8587 16.43 71.8587 16.958V20.5H70.5057V17.057C70.5057 16.7637 70.4654 16.5217 70.3847 16.331C70.3041 16.133 70.1794 15.99 70.0107 15.902C69.8421 15.8067 69.6367 15.7627 69.3947 15.77C69.1967 15.77 69.0134 15.803 68.8447 15.869C68.6834 15.9277 68.5404 16.0157 68.4157 16.133C68.2984 16.243 68.2031 16.3713 68.1297 16.518C68.0637 16.6647 68.0307 16.826 68.0307 17.002V20.5H67.3597C67.2277 20.5 67.1031 20.5 66.9857 20.5C66.8757 20.5 66.7731 20.5 66.6777 20.5Z" fill="white" />
                </svg>
                </span>

            </div>
        </div>
        <div class="all-tables">
            <div class="table-container">
                <table>
                    <tr>
                        <th> SL.No </th>
                        <th> Created On </th>
                        <th> License Expiry </th>
                        <th> Payment Date </th>
                        <th> Total License </th>
                        <th> Net Amount </th>
                        <th> GST Amount </th>
                        <th> Total Amount </th>
                        <th> Paid Amount </th>
                        <th> Pending Amount </th>
                        <th> Actions </th>
                    </tr>
                    @foreach (var items in GetFilteredSubscriptions().Select((value, i) => new { value, i }))
                    {
                        <tr>
                            <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                            <td>@items.value?.CreatedOn.ToString("dd/MM/yyyy")</td>
                            <td>
                                @if (!items.value.IsActive || items.value.IsDeleted)
                                {
                                    <div>
                                        @items.value?.LicenseValidity.ToString("dd/MM/yyyy")
                                        <AuthorizeView Roles="Admin">
                                            <button class="ActiveInactive-button" title="Active/InActive" @onclick="() =>ToggleSubscriptionIsActive(items.value.Id,items.value.LicenseValidity,true)">
                                                <b>X</b>
                                            </button>
                                        </AuthorizeView>
                                    </div>
                                }
                                else
                                {
                                    <div>
                                        @items.value?.LicenseValidity.ToString("dd/MM/yyyy")
                                        <AuthorizeView Roles="Admin">
                                            <button class="ActiveInactive-button" title="Active/InActive" @onclick="() =>ToggleSubscriptionIsActive(items.value.Id,items.value.LicenseValidity,false)">
                                                <b>&#10004;</b>
                                            </button>
                                        </AuthorizeView>
                                    </div>
                                }
                            </td>
                            <td>@items.value?.PaymentDate?.ToString("dd/MM/yyyy")</td>
                            <td>@items.value?.SoldLicenses</td>
                            <td>@items.value?.NetAmount</td>
                            <td>@items.value?.GSTAmount</td>
                            <td>@items.value?.TotalAmount</td>
                            <td>@items.value?.PaidAmount</td>
                            <td>@items.value?.DueAmount</td>
                            <td>
                                @* Add Addon *@
                                <span class="options-rightside" @onclick="@(async () => await GetAddOnSubscriptions(@items.value?.Id))">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="20" height="20" rx="4" fill="#0C0C0C" />
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.6673 9.9987C14.6673 12.576 12.578 14.6654 10.0007 14.6654C7.42332 14.6654 5.33398 12.576 5.33398 9.9987C5.33398 7.42137 7.42332 5.33203 10.0007 5.33203C12.578 5.33203 14.6673 7.42137 14.6673 9.9987ZM10.0007 7.78203C10.194 7.78203 10.3507 7.93873 10.3507 8.13203V9.6487H11.8673C12.0606 9.6487 12.2173 9.8054 12.2173 9.9987C12.2173 10.192 12.0606 10.3487 11.8673 10.3487H10.3507V11.8654C10.3507 12.0587 10.194 12.2154 10.0007 12.2154C9.80735 12.2154 9.65065 12.0587 9.65065 11.8654V10.3487H8.13398C7.94068 10.3487 7.78398 10.192 7.78398 9.9987C7.78398 9.8054 7.94068 9.6487 8.13398 9.6487H9.65065V8.13203C9.65065 7.93873 9.80735 7.78203 10.0007 7.78203Z" fill="white" />
                                        <title>AddOn Subscription</title>
                                    </svg>

                                </span>
                                @* Payment *@
                                <span class="options-rightside" @onclick="@(async () => await GetPaymentsOnSubscription(@items.value?.Id))">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="20" height="20" rx="4" fill="#59A7EF" />
                                        <g clip-path="url(#clip0_324_17806)">
                                            <path d="M5.00056 10.0017C5.00056 8.68997 5.00056 7.37845 5.00056 6.06712C5.00056 5.42712 5.42235 5.00334 6.06019 5.00306C6.6727 5.00306 7.28214 5.00584 7.89298 5C7.99014 5 8.0024 5.0295 8.00212 5.11492C7.99878 5.94747 7.99989 6.78029 8.00017 7.61311C8.00017 8.05832 8.27607 8.33408 8.7193 8.33435H11.2807C11.7234 8.33435 11.9993 8.0561 11.9993 7.61256C11.9993 6.77649 11.9993 5.94032 11.9993 5.10407C11.9993 5.02699 12.0121 4.99694 12.0981 5.00056C12.2782 5.00835 12.46 5.00501 12.6393 5.00223C12.6914 5.00017 12.7433 5.00914 12.7916 5.02857C12.8399 5.04799 12.8836 5.07742 12.9197 5.11492C13.5745 5.77049 14.2299 6.4256 14.8859 7.08025C14.9233 7.11613 14.9529 7.15939 14.9727 7.20729C14.9925 7.25519 15.0022 7.30668 15.0011 7.35851C15.0011 9.56601 15.0011 11.7727 15.0011 13.9785C15.0011 14.5629 14.5646 14.993 13.9793 14.9964C13.7953 14.9964 13.6113 14.9917 13.4275 14.9989C13.344 15.0022 13.3337 14.9747 13.3337 14.9009C13.3361 13.9432 13.3361 12.9856 13.3337 12.0282C13.3337 11.5427 13.0439 11.1584 12.5898 11.0332C12.4958 11.0088 12.399 10.9972 12.3019 10.9987C10.7675 10.9987 9.23316 10.9987 7.69892 10.9987C7.10201 10.9987 6.6688 11.4322 6.6688 12.0282C6.66769 12.9823 6.66834 13.9364 6.67075 14.8906C6.67075 14.9761 6.65293 15.0019 6.56467 14.9972C6.32246 14.9833 6.07829 15.0178 5.83747 14.978C5.34746 14.897 5.00223 14.4897 5.00223 13.9863C5 12.6587 4.99944 11.3304 5.00056 10.0017Z" fill="white" />
                                            <path d="M9.99421 14.9993C9.14032 14.9993 8.28643 14.9993 7.43282 15.0012C7.35096 15.0012 7.33176 14.9809 7.33203 14.9002C7.33482 13.9494 7.33371 12.9989 7.33399 12.0483C7.33399 11.7979 7.46317 11.668 7.71012 11.668H12.2917C12.5319 11.668 12.6664 11.801 12.6664 12.0397C12.6664 12.9972 12.6664 13.9547 12.6686 14.9122C12.6686 14.9873 12.6472 15.0012 12.5765 15.0009C11.716 14.9978 10.8552 14.9972 9.99421 14.9993Z" fill="white" />
                                            <path d="M10.0097 5.00181C10.4229 5.00181 10.8361 5.00181 11.2492 5.00014C11.3172 5.00014 11.3344 5.01739 11.3341 5.08362C11.3315 5.91635 11.3315 6.74917 11.3341 7.58209C11.3341 7.64998 11.3169 7.66724 11.249 7.66696C10.4159 7.66436 9.58284 7.66436 8.74965 7.66696C8.68171 7.66696 8.66445 7.6497 8.66612 7.58209C8.66835 6.74917 8.66835 5.91635 8.66612 5.08362C8.66612 5.01572 8.68339 4.99819 8.75132 5.00014C9.16977 5.0032 9.58989 5.00181 10.0097 5.00181Z" fill="white" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_324_17806">
                                                <rect width="10" height="10" fill="white" transform="translate(5 5)" />
                                            </clipPath>
                                        </defs>
                                        <title>Payment Details</title>
                                    </svg>

                                </span>
                                @* Edit *@
                                <span class="options-rightside" @onclick="@(async () => await UpdateSubscriptionDataById(items.value?.Id))">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="20" height="20" rx="4" fill="#78AECC" />
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9889 5.68311C11.4543 5.215 12.2089 5.215 12.6743 5.68311L13.5171 6.5307C13.9825 6.9988 13.9825 7.75776 13.5171 8.22586L12.7699 8.97736C12.7112 8.94815 12.6495 8.91656 12.5855 8.88268C12.1548 8.65462 11.6437 8.33455 11.2623 7.95091C10.8808 7.56727 10.5626 7.05323 10.3358 6.62005C10.3021 6.55568 10.2707 6.4936 10.2417 6.4346L10.9889 5.68311ZM10.7154 8.50088C11.1668 8.95485 11.7408 9.31215 12.1955 9.55506L9.2571 12.5103C9.07476 12.6937 8.83819 12.8127 8.58291 12.8494L6.81315 13.1036C6.41987 13.1601 6.08278 12.8211 6.13896 12.4256L6.39178 10.6456C6.42825 10.3889 6.54654 10.151 6.72888 9.96757L9.66725 7.0123C9.90878 7.46954 10.264 8.0469 10.7154 8.50088ZM8.96837 13.8876C8.75482 13.8876 8.5817 14.0617 8.5817 14.2765C8.5817 14.4913 8.75482 14.6654 8.96837 14.6654H13.4795C13.693 14.6654 13.8661 14.4913 13.8661 14.2765C13.8661 14.0617 13.693 13.8876 13.4795 13.8876H8.96837Z" fill="white" />
                                        <title>Update Subscription</title>
                                    </svg>

                                </span>
                                <AuthorizeView Roles="Admin">
                                    @* Delete *@
                                    <span class="options-rightside" @onclick="@(async () => await DeleteSubscription(items.value?.Id))">
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="20" height="20" rx="4" fill="#EF595C" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0502 14.6654C12.0188 14.6654 12.8227 13.9085 12.8917 12.9315L13.2093 8.43469C12.2321 8.09011 11.1452 7.89804 10 7.89804C8.85484 7.89804 7.76794 8.09011 6.79073 8.43469L7.10837 12.9315C7.17737 13.9085 7.98125 14.6654 8.9498 14.6654H11.0502ZM9.07696 9.1815C9.26813 9.1815 9.42311 9.33821 9.42311 9.53153V12.3318C9.42311 12.5251 9.26813 12.6818 9.07696 12.6818C8.88579 12.6818 8.73081 12.5251 8.73081 12.3318V9.53153C8.73081 9.33821 8.88579 9.1815 9.07696 9.1815ZM11.2692 9.53153C11.2692 9.33821 11.1143 9.1815 10.9231 9.1815C10.7319 9.1815 10.5769 9.33821 10.5769 9.53153V12.3318C10.5769 12.5251 10.7319 12.6818 10.9231 12.6818C11.1143 12.6818 11.2692 12.5251 11.2692 12.3318V9.53153Z" fill="white" />
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M10 5.33203C9.29904 5.33203 8.7308 5.90666 8.7308 6.61549V6.6928C7.63006 6.82826 6.60172 7.13726 5.69459 7.58429C5.52274 7.66898 5.45131 7.87851 5.53506 8.05229C5.61881 8.22607 5.82601 8.2983 5.99786 8.21361C7.14783 7.64691 8.52114 7.31556 10 7.31556C11.4789 7.31556 12.8522 7.64691 14.0021 8.21361C14.174 8.2983 14.3812 8.22607 14.4649 8.05229C14.5487 7.87851 14.4773 7.66898 14.3054 7.58429C13.3983 7.13726 12.37 6.82826 11.2692 6.6928V6.61549C11.2692 5.90666 10.701 5.33203 10 5.33203Z" fill="white" />
                                            <title>Delete Subscription</title>
                                        </svg>

                                    </span>
                                </AuthorizeView>
                            </td>
                        </tr>
                    }
                </table>
            </div>
            
        </div>
        <div class="tenantmasterfooter">
            <div class="pagesize">
                <div class="pagesizebox">
                    <span> @ShowingCount of @TotalCount</span>
                </div>
            </div>
            <div class="pagenumber">
                @if (UserCollection?.Count < PageSize)
                {
                    <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)">Back</span>
                }
                else
                {
                    @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                    {
                        if (i < PageNumber || i == 0)
                        {
                            <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)"> &lt;&lt; </span>
                        }
                        else if (i == PageNumber)
                        {
                            <span id="pagenumbercircle">@i</span>
                        }
                        else if (i > PageNumber)
                        {
                            <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber(CollectionType)">&gt;&gt;</span>
                        }
                    }
                }



            </div>
        </div>
    }
    else if (CollectionType == CollectionType.User)
    {
        <div class="options-header">
            <div class="options">
                <span class="option-option" @onclick="@(()=> Navigation.NavigateTo($"add-user/{TenantId}"))">Add New User</span>
                <span class="option-option" @onclick="ShowBulkUserPopUpModal">Add Bulk User</span>
                <span class="option-option" @onclick="ShowNotificationPopUpModal">Broadcast</span>
            </div>
            <div class="header-buttons">
                <span class="options-rightside">
                    <select id="pagesizeinput" @bind="PageSize" @bind:after="@GetUserAsync">
                        @foreach (var dv in DropdownValues)
                        {
                            <option value="@dv">@dv</option>
                        }
                    </select>
                </span>
                <span>
                    <input type="text" placeholder=" Press Enter to Search" class="Searchbarbar" @bind-value="userFilter.SearchId" @oninput="e => userFilter.SearchId = e.Value?.ToString()" @onkeydown="HandleKeyDownUser">
                </span>
                @*Refresh*@
                <span class="options-rightside" @onclick="@(async (e) => {userFilter = new(); Paginate(1); await GetUserAsync();})">
                    <svg width="86" height="34" viewBox="0 0 86 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="86" height="34" rx="4" fill="#343739"/>
                    <path d="M18.0092 21.219C18.0851 21.2552 18.1681 21.2739 18.2522 21.2737C18.3815 21.2737 18.507 21.2296 18.608 21.1488C18.7089 21.068 18.7794 20.9552 18.8077 20.829C18.836 20.7028 18.8205 20.5708 18.7637 20.4546C18.707 20.3383 18.6123 20.2449 18.4954 20.1897C17.7721 19.8485 17.1861 19.2718 16.8336 18.554C16.481 17.8361 16.3828 17.0198 16.555 16.2388C16.7271 15.4579 17.1595 14.7586 17.7813 14.2555C18.1464 13.9601 18.5641 13.7427 19.0088 13.6124L18.581 14.468C18.5377 14.5547 18.5172 14.6511 18.5216 14.7479C18.526 14.8448 18.555 14.9389 18.6059 15.0214C18.6569 15.1039 18.7281 15.1719 18.8127 15.2192C18.8973 15.2665 18.9927 15.2913 19.0896 15.2914C19.1955 15.2918 19.2993 15.2626 19.3894 15.2069C19.4795 15.1513 19.5521 15.0715 19.5991 14.9766L20.5097 13.1555C20.5772 13.0205 20.5882 12.8642 20.5404 12.721C20.4927 12.5779 20.39 12.4595 20.2549 12.3921C20.1715 12.3504 20.08 12.3303 19.9884 12.3322C19.9822 12.3321 19.976 12.332 19.9697 12.332C18.9121 12.339 17.8881 12.7051 17.0658 13.3703C16.2434 14.0355 15.6714 14.9604 15.4436 15.9932C15.2157 17.0261 15.3455 18.1058 15.8116 19.0552C16.2778 20.0047 17.0527 20.7676 18.0092 21.219Z" fill="white"/>
                    <path d="M20.0157 21.6652C20.0198 21.6653 20.0239 21.6653 20.028 21.6653H20.0315C21.0892 21.6583 22.1132 21.2922 22.9355 20.627C23.7579 19.9618 24.3299 19.037 24.5577 18.0041C24.7856 16.9712 24.6558 15.8916 24.1897 14.9421C23.7235 13.9927 22.9487 13.2298 21.9921 12.7784C21.9163 12.7421 21.8333 12.7233 21.7493 12.7233C21.6414 12.7237 21.5357 12.7546 21.4446 12.8124C21.3534 12.8702 21.2804 12.9526 21.234 13.0501C21.2021 13.1176 21.1839 13.1908 21.1803 13.2655C21.1767 13.3401 21.1879 13.4147 21.2132 13.485C21.2384 13.5553 21.2773 13.62 21.3276 13.6753C21.3778 13.7306 21.4385 13.7754 21.5061 13.8072C22.2294 14.1485 22.8153 14.7253 23.1678 15.4432C23.5203 16.161 23.6185 16.9773 23.4463 17.7583C23.2741 18.5393 22.8417 19.2386 22.22 19.7417C21.8549 20.0371 21.4372 20.2546 20.9926 20.3848L21.4203 19.5295C21.4877 19.3944 21.4988 19.2382 21.451 19.095C21.4032 18.9518 21.3005 18.8335 21.1655 18.7661C21.0305 18.6986 20.8742 18.6876 20.7311 18.7353C20.5879 18.7831 20.4696 18.8858 20.4021 19.0208L19.4916 20.842C19.4482 20.9287 19.4278 21.025 19.4322 21.1219C19.4365 21.2187 19.4656 21.3129 19.5165 21.3953C19.5675 21.4778 19.6386 21.5459 19.7233 21.5932C19.8079 21.6404 19.9032 21.6653 20.0002 21.6654C20.0053 21.6654 20.0105 21.6654 20.0157 21.6652Z" fill="white"/>
                    <path d="M32.034 21V13.3H35.378C35.84 13.3 36.2617 13.41 36.643 13.63C37.0243 13.8427 37.325 14.136 37.545 14.51C37.7723 14.8767 37.886 15.291 37.886 15.753C37.886 16.193 37.7723 16.6 37.545 16.974C37.325 17.3407 37.0243 17.634 36.643 17.854C36.269 18.0667 35.8473 18.173 35.378 18.173H33.431V21H32.034ZM36.5 21L34.542 17.524L36.016 17.249L38.194 21.011L36.5 21ZM33.431 16.93H35.389C35.6017 16.93 35.785 16.8823 35.939 16.787C36.1003 16.6843 36.225 16.545 36.313 16.369C36.401 16.193 36.445 15.9987 36.445 15.786C36.445 15.544 36.39 15.335 36.28 15.159C36.17 14.983 36.016 14.8437 35.818 14.741C35.62 14.6383 35.3927 14.587 35.136 14.587H33.431V16.93ZM41.9641 21.11C41.3554 21.11 40.8164 20.9817 40.3471 20.725C39.8851 20.4683 39.5221 20.12 39.2581 19.68C39.0014 19.2327 38.8731 18.723 38.8731 18.151C38.8731 17.6963 38.9464 17.282 39.0931 16.908C39.2397 16.534 39.4414 16.2113 39.6981 15.94C39.9621 15.6613 40.2737 15.4487 40.6331 15.302C40.9997 15.148 41.3994 15.071 41.8321 15.071C42.2134 15.071 42.5691 15.1443 42.8991 15.291C43.2291 15.4377 43.5151 15.6393 43.7571 15.896C43.9991 16.1453 44.1824 16.446 44.3071 16.798C44.4391 17.1427 44.5014 17.5203 44.4941 17.931L44.4831 18.404H39.7861L39.5331 17.48H43.3501L43.1741 17.667V17.425C43.1521 17.1977 43.0787 16.9997 42.9541 16.831C42.8294 16.655 42.6681 16.5193 42.4701 16.424C42.2794 16.3213 42.0667 16.27 41.8321 16.27C41.4727 16.27 41.1684 16.3397 40.9191 16.479C40.6771 16.6183 40.4937 16.82 40.3691 17.084C40.2444 17.3407 40.1821 17.6633 40.1821 18.052C40.1821 18.426 40.2591 18.7523 40.4131 19.031C40.5744 19.3097 40.7981 19.526 41.0841 19.68C41.3774 19.8267 41.7184 19.9 42.1071 19.9C42.3784 19.9 42.6277 19.856 42.8551 19.768C43.0824 19.68 43.3281 19.5223 43.5921 19.295L44.2631 20.23C44.0651 20.4133 43.8377 20.571 43.5811 20.703C43.3317 20.8277 43.0677 20.9267 42.7891 21C42.5104 21.0733 42.2354 21.11 41.9641 21.11ZM46.2204 21V14.686C46.2204 14.334 46.2974 14.0223 46.4514 13.751C46.6054 13.4723 46.8181 13.256 47.0894 13.102C47.3607 12.9407 47.6761 12.86 48.0354 12.86C48.2847 12.86 48.5157 12.904 48.7284 12.992C48.9411 13.0727 49.1207 13.1863 49.2674 13.333L48.8494 14.356C48.7541 14.2753 48.6514 14.213 48.5414 14.169C48.4387 14.125 48.3397 14.103 48.2444 14.103C48.0977 14.103 47.9731 14.1287 47.8704 14.18C47.7751 14.224 47.7017 14.2937 47.6504 14.389C47.6064 14.4843 47.5844 14.598 47.5844 14.73V21H46.9024C46.7704 21 46.6457 21 46.5284 21C46.4184 21 46.3157 21 46.2204 21ZM45.3184 16.512V15.324H48.8934V16.512H45.3184ZM50.0108 21V15.203H51.3418L51.3638 17.051L51.1768 16.633C51.2574 16.3397 51.3968 16.0757 51.5948 15.841C51.7928 15.6063 52.0201 15.423 52.2768 15.291C52.5408 15.1517 52.8158 15.082 53.1018 15.082C53.2264 15.082 53.3438 15.093 53.4538 15.115C53.5711 15.137 53.6664 15.1627 53.7398 15.192L53.3768 16.677C53.2961 16.633 53.1971 16.5963 53.0798 16.567C52.9624 16.5377 52.8451 16.523 52.7278 16.523C52.5444 16.523 52.3684 16.5597 52.1998 16.633C52.0384 16.699 51.8954 16.7943 51.7708 16.919C51.6461 17.0437 51.5471 17.1903 51.4738 17.359C51.4078 17.5203 51.3748 17.7037 51.3748 17.909V21H50.0108ZM57.1106 21.11C56.5019 21.11 55.9629 20.9817 55.4936 20.725C55.0316 20.4683 54.6686 20.12 54.4046 19.68C54.1479 19.2327 54.0196 18.723 54.0196 18.151C54.0196 17.6963 54.0929 17.282 54.2396 16.908C54.3862 16.534 54.5879 16.2113 54.8446 15.94C55.1086 15.6613 55.4202 15.4487 55.7796 15.302C56.1462 15.148 56.5459 15.071 56.9786 15.071C57.3599 15.071 57.7156 15.1443 58.0456 15.291C58.3756 15.4377 58.6616 15.6393 58.9036 15.896C59.1456 16.1453 59.3289 16.446 59.4536 16.798C59.5856 17.1427 59.6479 17.5203 59.6406 17.931L59.6296 18.404H54.9326L54.6796 17.48H58.4966L58.3206 17.667V17.425C58.2986 17.1977 58.2252 16.9997 58.1006 16.831C57.9759 16.655 57.8146 16.5193 57.6166 16.424C57.4259 16.3213 57.2132 16.27 56.9786 16.27C56.6192 16.27 56.3149 16.3397 56.0656 16.479C55.8236 16.6183 55.6402 16.82 55.5156 17.084C55.3909 17.3407 55.3286 17.6633 55.3286 18.052C55.3286 18.426 55.4056 18.7523 55.5596 19.031C55.7209 19.3097 55.9446 19.526 56.2306 19.68C56.5239 19.8267 56.8649 19.9 57.2536 19.9C57.5249 19.9 57.7742 19.856 58.0016 19.768C58.2289 19.68 58.4746 19.5223 58.7386 19.295L59.4096 20.23C59.2116 20.4133 58.9842 20.571 58.7276 20.703C58.4782 20.8277 58.2142 20.9267 57.9356 21C57.6569 21.0733 57.3819 21.11 57.1106 21.11ZM62.7749 21.11C62.2615 21.11 61.7995 21.0257 61.3889 20.857C60.9855 20.681 60.6555 20.4317 60.3989 20.109L61.2789 19.35C61.4989 19.5993 61.7445 19.779 62.0159 19.889C62.2872 19.999 62.5732 20.054 62.8739 20.054C62.9985 20.054 63.1085 20.0393 63.2039 20.01C63.3065 19.9807 63.3945 19.9367 63.4679 19.878C63.5412 19.8193 63.5962 19.7533 63.6329 19.68C63.6769 19.5993 63.6989 19.5113 63.6989 19.416C63.6989 19.24 63.6329 19.1007 63.5009 18.998C63.4275 18.9467 63.3102 18.8917 63.1489 18.833C62.9949 18.7743 62.7932 18.7157 62.5439 18.657C62.1479 18.5543 61.8179 18.437 61.5539 18.305C61.2899 18.1657 61.0845 18.0117 60.9379 17.843C60.8132 17.7037 60.7179 17.546 60.6519 17.37C60.5932 17.194 60.5639 17.0033 60.5639 16.798C60.5639 16.5413 60.6189 16.3103 60.7289 16.105C60.8462 15.8923 61.0039 15.709 61.2019 15.555C61.3999 15.401 61.6309 15.2837 61.8949 15.203C62.1589 15.1223 62.4339 15.082 62.7199 15.082C63.0132 15.082 63.2955 15.1187 63.5669 15.192C63.8455 15.2653 64.1022 15.3717 64.3369 15.511C64.5789 15.643 64.7842 15.8043 64.9529 15.995L64.2049 16.82C64.0655 16.688 63.9115 16.5707 63.7429 16.468C63.5815 16.3653 63.4165 16.2847 63.2479 16.226C63.0792 16.16 62.9215 16.127 62.7749 16.127C62.6355 16.127 62.5109 16.1417 62.4009 16.171C62.2909 16.193 62.1992 16.2297 62.1259 16.281C62.0525 16.3323 61.9939 16.3983 61.9499 16.479C61.9132 16.5523 61.8949 16.6403 61.8949 16.743C61.9022 16.831 61.9242 16.9153 61.9609 16.996C62.0049 17.0693 62.0635 17.1317 62.1369 17.183C62.2175 17.2343 62.3385 17.293 62.4999 17.359C62.6612 17.425 62.8702 17.4873 63.1269 17.546C63.4862 17.6413 63.7869 17.7477 64.0289 17.865C64.2709 17.9823 64.4615 18.118 64.6009 18.272C64.7402 18.404 64.8392 18.558 64.8979 18.734C64.9565 18.91 64.9859 19.1043 64.9859 19.317C64.9859 19.6617 64.8869 19.9697 64.6889 20.241C64.4982 20.5123 64.2342 20.725 63.8969 20.879C63.5669 21.033 63.1929 21.11 62.7749 21.11ZM66.1777 21V12.86H67.5197V16.391L67.2667 16.523C67.3401 16.259 67.4831 16.0207 67.6957 15.808C67.9084 15.588 68.1614 15.412 68.4547 15.28C68.7481 15.148 69.0487 15.082 69.3567 15.082C69.7967 15.082 70.1634 15.17 70.4567 15.346C70.7574 15.522 70.9811 15.786 71.1277 16.138C71.2817 16.49 71.3587 16.93 71.3587 17.458V21H70.0057V17.557C70.0057 17.2637 69.9654 17.0217 69.8847 16.831C69.8041 16.633 69.6794 16.49 69.5107 16.402C69.3421 16.3067 69.1367 16.2627 68.8947 16.27C68.6967 16.27 68.5134 16.303 68.3447 16.369C68.1834 16.4277 68.0404 16.5157 67.9157 16.633C67.7984 16.743 67.7031 16.8713 67.6297 17.018C67.5637 17.1647 67.5307 17.326 67.5307 17.502V21H66.8597C66.7277 21 66.6031 21 66.4857 21C66.3757 21 66.2731 21 66.1777 21Z" fill="white"/>
                    </svg>
                </span>
            </div>
        </div>
        <div class="all-tables">
            <table>
                <tr>
                    <th>Name</th>
                    <th>UserName</th>
                    <th>Role</th>
                    <th>Phone Number</th>
                    <th> Created On </th>
                    <th>Email</th>
                    <th>Assigned Leads</th>
                    <th>Assigned Properties</th>
                    <th>Reporting To</th>
                    <th>Department</th>
                    <th>App Download</th>
                    <th>Action</th>
                </tr>
                @if(UserCollection != null)
                {
                    var UserCollectionCpy = UserCollection.DistinctBy(i => i.Name);
                    @foreach (var items in UserCollectionCpy.Select((value, i) => new { value, i }).ToList())
                    {
                        <tr>
                            <td>
                                @if (items.value?.Name != null)
                                {
                                    if (@items.value.IsActive)
                                    {
                                        <div>
                                            @items.value?.Name
                                            <button class="ActiveInactive-button" title="Active/InActive" @onclick="() => ToggleIsActive(items.value?.Id, false)">
                                                <b>&#10004;</b>
                                            </button>

                                        </div>
                                    }
                                    else
                                    {
                                        <div>
                                            @items.value?.Name
                                            <button class="ActiveInactive-button" title="Active/InActive" @onclick="() => ToggleIsActive(items.value?.Id, true)">
                                                <b>X</b>
                                            </button>
                                        </div>
                                    }
                                }
                            </td>
                            <td>@items.value?.UserName</td>
                            <td>@items.value?.Role?.Replace("{", "").Replace("}", "").Replace("\"", "")</td>
                            <td>@items.value?.PhoneNumber</td>
                            <td>@items.value?.CreatedOn?.ToString("dd/MM/yyyy")</td>
                            <td>@items.value?.Email</td>
                            <td>@items.value?.LeadsCount</td>
                            <td>@TenantModel?.PropertyCount</td>
                            <td>@items.value?.ReportingTo</td>
                            <td>@items.value?.Department</td>
                            <td>@items.value?.AppVersion</td>
                            <td>
                                <span @onclick=" () => { Id = items.value?.Id; ShowNotificationPopUpModalForUserModal(items?.value);}" class="options-rightside"><u>Send Notification</u></span>
                            </td>
                        </tr>
                    }
                }
            </table>
        </div>
        <div class="tenantmasterfooter">
            <div class="pagesize">
                <div class="pagesizebox">
                    
                    <span> @ShowingCount of @TotalCount</span>
                </div>
            </div>
            <div class="pagenumber">
                @if (UserCollection?.Count < PageSize)
                {
                    <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)">Back</span>
                }
                else
                {
                    @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                    {
                        if (i < PageNumber || i == 0)
                        {
                            <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)"> &lt;&lt; </span>
                        }
                        else if (i == PageNumber)
                        {
                            <span id="pagenumbercircle">@i</span>
                        }
                        else if (i > PageNumber)
                        {
                            <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber(CollectionType)">&gt;&gt;</span>
                        }
                    }
                }



            </div>
        </div>
    }
    else if(CollectionType == CollectionType.Integration)
    {
        <div class="options-header">
            <div class="options">
                <span class="option-option">
                    Staus : 
                    <InputSelect id="status" @bind-Value="Filter.Status" @onclick="GetIntegrationAsync">
                        <option value="true">false</option>
                        <option value="false">true</option>
                    </InputSelect>

                </span>
            </div>
            <div class="header-buttons">
                <span></span>
                @*Refresh*@
                <span class="options-rightside" @onclick="@(async (e) => {Filter = new(); Paginate(1); await GetIntegrationAsync();})">
                    <svg width="86" height="33" viewBox="0 0 86 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="86" height="33" rx="4" fill="#343739" />
                        <path d="M18.0092 20.719C18.0851 20.7552 18.1681 20.7739 18.2522 20.7737C18.3815 20.7737 18.507 20.7296 18.608 20.6488C18.7089 20.568 18.7794 20.4552 18.8077 20.329C18.836 20.2028 18.8205 20.0708 18.7637 19.9546C18.707 19.8383 18.6123 19.7449 18.4954 19.6897C17.7721 19.3485 17.1861 18.7718 16.8336 18.054C16.481 17.3361 16.3828 16.5198 16.555 15.7388C16.7271 14.9579 17.1595 14.2586 17.7813 13.7555C18.1464 13.4601 18.5641 13.2427 19.0088 13.1124L18.581 13.968C18.5377 14.0547 18.5172 14.1511 18.5216 14.2479C18.526 14.3448 18.555 14.4389 18.6059 14.5214C18.6569 14.6039 18.7281 14.6719 18.8127 14.7192C18.8973 14.7665 18.9927 14.7913 19.0896 14.7914C19.1955 14.7918 19.2993 14.7626 19.3894 14.7069C19.4795 14.6513 19.5521 14.5715 19.5991 14.4766L20.5097 12.6555C20.5772 12.5205 20.5882 12.3642 20.5404 12.221C20.4927 12.0779 20.39 11.9595 20.2549 11.8921C20.1715 11.8504 20.08 11.8303 19.9884 11.8322C19.9822 11.8321 19.976 11.832 19.9697 11.832C18.9121 11.839 17.8881 12.2051 17.0658 12.8703C16.2434 13.5355 15.6714 14.4604 15.4436 15.4932C15.2157 16.5261 15.3455 17.6058 15.8116 18.5552C16.2778 19.5047 17.0527 20.2676 18.0092 20.719Z" fill="white" />
                        <path d="M20.0157 21.1652C20.0198 21.1653 20.0239 21.1653 20.028 21.1653H20.0315C21.0892 21.1583 22.1132 20.7922 22.9355 20.127C23.7579 19.4618 24.3299 18.537 24.5577 17.5041C24.7856 16.4712 24.6558 15.3916 24.1897 14.4421C23.7235 13.4927 22.9487 12.7298 21.9921 12.2784C21.9163 12.2421 21.8333 12.2233 21.7493 12.2233C21.6414 12.2237 21.5357 12.2546 21.4446 12.3124C21.3534 12.3702 21.2804 12.4526 21.234 12.5501C21.2021 12.6176 21.1839 12.6908 21.1803 12.7655C21.1767 12.8401 21.1879 12.9147 21.2132 12.985C21.2384 13.0553 21.2773 13.12 21.3276 13.1753C21.3778 13.2306 21.4385 13.2754 21.5061 13.3072C22.2294 13.6485 22.8153 14.2253 23.1678 14.9432C23.5203 15.661 23.6185 16.4773 23.4463 17.2583C23.2741 18.0393 22.8417 18.7386 22.22 19.2417C21.8549 19.5371 21.4372 19.7546 20.9926 19.8848L21.4203 19.0295C21.4877 18.8944 21.4988 18.7382 21.451 18.595C21.4032 18.4518 21.3005 18.3335 21.1655 18.2661C21.0305 18.1986 20.8742 18.1876 20.7311 18.2353C20.5879 18.2831 20.4696 18.3858 20.4021 18.5208L19.4916 20.342C19.4482 20.4287 19.4278 20.525 19.4322 20.6219C19.4365 20.7187 19.4656 20.8129 19.5165 20.8953C19.5675 20.9778 19.6386 21.0459 19.7233 21.0932C19.8079 21.1404 19.9032 21.1653 20.0002 21.1654C20.0053 21.1654 20.0105 21.1654 20.0157 21.1652Z" fill="white" />
                        <path d="M32.034 20.5V12.8H35.378C35.84 12.8 36.2617 12.91 36.643 13.13C37.0243 13.3427 37.325 13.636 37.545 14.01C37.7723 14.3767 37.886 14.791 37.886 15.253C37.886 15.693 37.7723 16.1 37.545 16.474C37.325 16.8407 37.0243 17.134 36.643 17.354C36.269 17.5667 35.8473 17.673 35.378 17.673H33.431V20.5H32.034ZM36.5 20.5L34.542 17.024L36.016 16.749L38.194 20.511L36.5 20.5ZM33.431 16.43H35.389C35.6017 16.43 35.785 16.3823 35.939 16.287C36.1003 16.1843 36.225 16.045 36.313 15.869C36.401 15.693 36.445 15.4987 36.445 15.286C36.445 15.044 36.39 14.835 36.28 14.659C36.17 14.483 36.016 14.3437 35.818 14.241C35.62 14.1383 35.3927 14.087 35.136 14.087H33.431V16.43ZM41.9641 20.61C41.3554 20.61 40.8164 20.4817 40.3471 20.225C39.8851 19.9683 39.5221 19.62 39.2581 19.18C39.0014 18.7327 38.8731 18.223 38.8731 17.651C38.8731 17.1963 38.9464 16.782 39.0931 16.408C39.2397 16.034 39.4414 15.7113 39.6981 15.44C39.9621 15.1613 40.2737 14.9487 40.6331 14.802C40.9997 14.648 41.3994 14.571 41.8321 14.571C42.2134 14.571 42.5691 14.6443 42.8991 14.791C43.2291 14.9377 43.5151 15.1393 43.7571 15.396C43.9991 15.6453 44.1824 15.946 44.3071 16.298C44.4391 16.6427 44.5014 17.0203 44.4941 17.431L44.4831 17.904H39.7861L39.5331 16.98H43.3501L43.1741 17.167V16.925C43.1521 16.6977 43.0787 16.4997 42.9541 16.331C42.8294 16.155 42.6681 16.0193 42.4701 15.924C42.2794 15.8213 42.0667 15.77 41.8321 15.77C41.4727 15.77 41.1684 15.8397 40.9191 15.979C40.6771 16.1183 40.4937 16.32 40.3691 16.584C40.2444 16.8407 40.1821 17.1633 40.1821 17.552C40.1821 17.926 40.2591 18.2523 40.4131 18.531C40.5744 18.8097 40.7981 19.026 41.0841 19.18C41.3774 19.3267 41.7184 19.4 42.1071 19.4C42.3784 19.4 42.6277 19.356 42.8551 19.268C43.0824 19.18 43.3281 19.0223 43.5921 18.795L44.2631 19.73C44.0651 19.9133 43.8377 20.071 43.5811 20.203C43.3317 20.3277 43.0677 20.4267 42.7891 20.5C42.5104 20.5733 42.2354 20.61 41.9641 20.61ZM46.2204 20.5V14.186C46.2204 13.834 46.2974 13.5223 46.4514 13.251C46.6054 12.9723 46.8181 12.756 47.0894 12.602C47.3607 12.4407 47.6761 12.36 48.0354 12.36C48.2847 12.36 48.5157 12.404 48.7284 12.492C48.9411 12.5727 49.1207 12.6863 49.2674 12.833L48.8494 13.856C48.7541 13.7753 48.6514 13.713 48.5414 13.669C48.4387 13.625 48.3397 13.603 48.2444 13.603C48.0977 13.603 47.9731 13.6287 47.8704 13.68C47.7751 13.724 47.7017 13.7937 47.6504 13.889C47.6064 13.9843 47.5844 14.098 47.5844 14.23V20.5H46.9024C46.7704 20.5 46.6457 20.5 46.5284 20.5C46.4184 20.5 46.3157 20.5 46.2204 20.5ZM45.3184 16.012V14.824H48.8934V16.012H45.3184ZM50.0108 20.5V14.703H51.3418L51.3638 16.551L51.1768 16.133C51.2574 15.8397 51.3968 15.5757 51.5948 15.341C51.7928 15.1063 52.0201 14.923 52.2768 14.791C52.5408 14.6517 52.8158 14.582 53.1018 14.582C53.2264 14.582 53.3438 14.593 53.4538 14.615C53.5711 14.637 53.6664 14.6627 53.7398 14.692L53.3768 16.177C53.2961 16.133 53.1971 16.0963 53.0798 16.067C52.9624 16.0377 52.8451 16.023 52.7278 16.023C52.5444 16.023 52.3684 16.0597 52.1998 16.133C52.0384 16.199 51.8954 16.2943 51.7708 16.419C51.6461 16.5437 51.5471 16.6903 51.4738 16.859C51.4078 17.0203 51.3748 17.2037 51.3748 17.409V20.5H50.0108ZM57.1106 20.61C56.5019 20.61 55.9629 20.4817 55.4936 20.225C55.0316 19.9683 54.6686 19.62 54.4046 19.18C54.1479 18.7327 54.0196 18.223 54.0196 17.651C54.0196 17.1963 54.0929 16.782 54.2396 16.408C54.3862 16.034 54.5879 15.7113 54.8446 15.44C55.1086 15.1613 55.4202 14.9487 55.7796 14.802C56.1462 14.648 56.5459 14.571 56.9786 14.571C57.3599 14.571 57.7156 14.6443 58.0456 14.791C58.3756 14.9377 58.6616 15.1393 58.9036 15.396C59.1456 15.6453 59.3289 15.946 59.4536 16.298C59.5856 16.6427 59.6479 17.0203 59.6406 17.431L59.6296 17.904H54.9326L54.6796 16.98H58.4966L58.3206 17.167V16.925C58.2986 16.6977 58.2252 16.4997 58.1006 16.331C57.9759 16.155 57.8146 16.0193 57.6166 15.924C57.4259 15.8213 57.2132 15.77 56.9786 15.77C56.6192 15.77 56.3149 15.8397 56.0656 15.979C55.8236 16.1183 55.6402 16.32 55.5156 16.584C55.3909 16.8407 55.3286 17.1633 55.3286 17.552C55.3286 17.926 55.4056 18.2523 55.5596 18.531C55.7209 18.8097 55.9446 19.026 56.2306 19.18C56.5239 19.3267 56.8649 19.4 57.2536 19.4C57.5249 19.4 57.7742 19.356 58.0016 19.268C58.2289 19.18 58.4746 19.0223 58.7386 18.795L59.4096 19.73C59.2116 19.9133 58.9842 20.071 58.7276 20.203C58.4782 20.3277 58.2142 20.4267 57.9356 20.5C57.6569 20.5733 57.3819 20.61 57.1106 20.61ZM62.7749 20.61C62.2615 20.61 61.7995 20.5257 61.3889 20.357C60.9855 20.181 60.6555 19.9317 60.3989 19.609L61.2789 18.85C61.4989 19.0993 61.7445 19.279 62.0159 19.389C62.2872 19.499 62.5732 19.554 62.8739 19.554C62.9985 19.554 63.1085 19.5393 63.2039 19.51C63.3065 19.4807 63.3945 19.4367 63.4679 19.378C63.5412 19.3193 63.5962 19.2533 63.6329 19.18C63.6769 19.0993 63.6989 19.0113 63.6989 18.916C63.6989 18.74 63.6329 18.6007 63.5009 18.498C63.4275 18.4467 63.3102 18.3917 63.1489 18.333C62.9949 18.2743 62.7932 18.2157 62.5439 18.157C62.1479 18.0543 61.8179 17.937 61.5539 17.805C61.2899 17.6657 61.0845 17.5117 60.9379 17.343C60.8132 17.2037 60.7179 17.046 60.6519 16.87C60.5932 16.694 60.5639 16.5033 60.5639 16.298C60.5639 16.0413 60.6189 15.8103 60.7289 15.605C60.8462 15.3923 61.0039 15.209 61.2019 15.055C61.3999 14.901 61.6309 14.7837 61.8949 14.703C62.1589 14.6223 62.4339 14.582 62.7199 14.582C63.0132 14.582 63.2955 14.6187 63.5669 14.692C63.8455 14.7653 64.1022 14.8717 64.3369 15.011C64.5789 15.143 64.7842 15.3043 64.9529 15.495L64.2049 16.32C64.0655 16.188 63.9115 16.0707 63.7429 15.968C63.5815 15.8653 63.4165 15.7847 63.2479 15.726C63.0792 15.66 62.9215 15.627 62.7749 15.627C62.6355 15.627 62.5109 15.6417 62.4009 15.671C62.2909 15.693 62.1992 15.7297 62.1259 15.781C62.0525 15.8323 61.9939 15.8983 61.9499 15.979C61.9132 16.0523 61.8949 16.1403 61.8949 16.243C61.9022 16.331 61.9242 16.4153 61.9609 16.496C62.0049 16.5693 62.0635 16.6317 62.1369 16.683C62.2175 16.7343 62.3385 16.793 62.4999 16.859C62.6612 16.925 62.8702 16.9873 63.1269 17.046C63.4862 17.1413 63.7869 17.2477 64.0289 17.365C64.2709 17.4823 64.4615 17.618 64.6009 17.772C64.7402 17.904 64.8392 18.058 64.8979 18.234C64.9565 18.41 64.9859 18.6043 64.9859 18.817C64.9859 19.1617 64.8869 19.4697 64.6889 19.741C64.4982 20.0123 64.2342 20.225 63.8969 20.379C63.5669 20.533 63.1929 20.61 62.7749 20.61ZM66.1777 20.5V12.36H67.5197V15.891L67.2667 16.023C67.3401 15.759 67.4831 15.5207 67.6957 15.308C67.9084 15.088 68.1614 14.912 68.4547 14.78C68.7481 14.648 69.0487 14.582 69.3567 14.582C69.7967 14.582 70.1634 14.67 70.4567 14.846C70.7574 15.022 70.9811 15.286 71.1277 15.638C71.2817 15.99 71.3587 16.43 71.3587 16.958V20.5H70.0057V17.057C70.0057 16.7637 69.9654 16.5217 69.8847 16.331C69.8041 16.133 69.6794 15.99 69.5107 15.902C69.3421 15.8067 69.1367 15.7627 68.8947 15.77C68.6967 15.77 68.5134 15.803 68.3447 15.869C68.1834 15.9277 68.0404 16.0157 67.9157 16.133C67.7984 16.243 67.7031 16.3713 67.6297 16.518C67.5637 16.6647 67.5307 16.826 67.5307 17.002V20.5H66.8597C66.7277 20.5 66.6031 20.5 66.4857 20.5C66.3757 20.5 66.2731 20.5 66.1777 20.5Z" fill="white" />
                    </svg>

                </span>
            </div>
        </div>
        <div class="all-tables">
            <table>
                <tr>
                    <th>Sl No</th>
                    <th>Integration</th>
                    <th>Account Info</th>
                    <th>Status</th>
                    <th>No of Leads</th>
                    <th>Action</th>
                </tr>
                @foreach (var items in IntegrationCollection.Select((value, i) => new { value, i }))
                {
                    <tr>
                        <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                        <td>@items.value?.LeadSource</td>
                        <td>@items.value?.AccountName</td>
                        <td>@(!items.value?.IsDeleted ?? false)</td>
                        <td>@items.value?.LeadCount</td>
                        <td>
                            <span class="options-rightside" @onclick="() => DownloadExcel(items.value?.FileUrl)"><u>Download Excel</u></span>
                        </td>
                    </tr>
                }
            </table>
        </div>
        <div class="tenantmasterfooter">
            <div class="pagesize">
                <div class="pagesizebox">
                    <select id="pagesizeinput" @bind="PageSize" @bind:after="@GetIntegrationAsync">
                        @foreach (var dv in DropdownValues)
                        {
                            <option value="@dv">@dv</option>
                        }
                    </select>
                    <span> @ShowingCount of @TotalCount</span>
                </div>
            </div>
            <div class="pagenumber">
                @if (IntegrationCollection?.Count < PageSize)
                {
                    <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)">Back</span>
                }
                else
                {
                    @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                    {
                        if (i < PageNumber || i == 0)
                        {
                            <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)"> &lt;&lt; </span>
                        }
                        else if (i == PageNumber)
                        {
                            <span id="pagenumbercircle">@i</span>
                        }
                        else if (i > PageNumber)
                        {
                            <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber(CollectionType)">&gt;&gt;</span>
                        }
                    }
                }



            </div>
        </div>
    }
    else if (CollectionType == CollectionType.Notification)
    {
        <div class="options-header">
            <div class="options">
            </div>
            <div class="header-buttons">
                <span></span>
                @*Refresh*@
                <span class="options-rightside" @onclick="@(async (e) => {Filter = new(); Paginate(1); await GetNotificationAsync();})">
                    <svg width="86" height="33" viewBox="0 0 86 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="86" height="33" rx="4" fill="#343739" />
                        <path d="M18.0092 20.719C18.0851 20.7552 18.1681 20.7739 18.2522 20.7737C18.3815 20.7737 18.507 20.7296 18.608 20.6488C18.7089 20.568 18.7794 20.4552 18.8077 20.329C18.836 20.2028 18.8205 20.0708 18.7637 19.9546C18.707 19.8383 18.6123 19.7449 18.4954 19.6897C17.7721 19.3485 17.1861 18.7718 16.8336 18.054C16.481 17.3361 16.3828 16.5198 16.555 15.7388C16.7271 14.9579 17.1595 14.2586 17.7813 13.7555C18.1464 13.4601 18.5641 13.2427 19.0088 13.1124L18.581 13.968C18.5377 14.0547 18.5172 14.1511 18.5216 14.2479C18.526 14.3448 18.555 14.4389 18.6059 14.5214C18.6569 14.6039 18.7281 14.6719 18.8127 14.7192C18.8973 14.7665 18.9927 14.7913 19.0896 14.7914C19.1955 14.7918 19.2993 14.7626 19.3894 14.7069C19.4795 14.6513 19.5521 14.5715 19.5991 14.4766L20.5097 12.6555C20.5772 12.5205 20.5882 12.3642 20.5404 12.221C20.4927 12.0779 20.39 11.9595 20.2549 11.8921C20.1715 11.8504 20.08 11.8303 19.9884 11.8322C19.9822 11.8321 19.976 11.832 19.9697 11.832C18.9121 11.839 17.8881 12.2051 17.0658 12.8703C16.2434 13.5355 15.6714 14.4604 15.4436 15.4932C15.2157 16.5261 15.3455 17.6058 15.8116 18.5552C16.2778 19.5047 17.0527 20.2676 18.0092 20.719Z" fill="white" />
                        <path d="M20.0157 21.1652C20.0198 21.1653 20.0239 21.1653 20.028 21.1653H20.0315C21.0892 21.1583 22.1132 20.7922 22.9355 20.127C23.7579 19.4618 24.3299 18.537 24.5577 17.5041C24.7856 16.4712 24.6558 15.3916 24.1897 14.4421C23.7235 13.4927 22.9487 12.7298 21.9921 12.2784C21.9163 12.2421 21.8333 12.2233 21.7493 12.2233C21.6414 12.2237 21.5357 12.2546 21.4446 12.3124C21.3534 12.3702 21.2804 12.4526 21.234 12.5501C21.2021 12.6176 21.1839 12.6908 21.1803 12.7655C21.1767 12.8401 21.1879 12.9147 21.2132 12.985C21.2384 13.0553 21.2773 13.12 21.3276 13.1753C21.3778 13.2306 21.4385 13.2754 21.5061 13.3072C22.2294 13.6485 22.8153 14.2253 23.1678 14.9432C23.5203 15.661 23.6185 16.4773 23.4463 17.2583C23.2741 18.0393 22.8417 18.7386 22.22 19.2417C21.8549 19.5371 21.4372 19.7546 20.9926 19.8848L21.4203 19.0295C21.4877 18.8944 21.4988 18.7382 21.451 18.595C21.4032 18.4518 21.3005 18.3335 21.1655 18.2661C21.0305 18.1986 20.8742 18.1876 20.7311 18.2353C20.5879 18.2831 20.4696 18.3858 20.4021 18.5208L19.4916 20.342C19.4482 20.4287 19.4278 20.525 19.4322 20.6219C19.4365 20.7187 19.4656 20.8129 19.5165 20.8953C19.5675 20.9778 19.6386 21.0459 19.7233 21.0932C19.8079 21.1404 19.9032 21.1653 20.0002 21.1654C20.0053 21.1654 20.0105 21.1654 20.0157 21.1652Z" fill="white" />
                        <path d="M32.034 20.5V12.8H35.378C35.84 12.8 36.2617 12.91 36.643 13.13C37.0243 13.3427 37.325 13.636 37.545 14.01C37.7723 14.3767 37.886 14.791 37.886 15.253C37.886 15.693 37.7723 16.1 37.545 16.474C37.325 16.8407 37.0243 17.134 36.643 17.354C36.269 17.5667 35.8473 17.673 35.378 17.673H33.431V20.5H32.034ZM36.5 20.5L34.542 17.024L36.016 16.749L38.194 20.511L36.5 20.5ZM33.431 16.43H35.389C35.6017 16.43 35.785 16.3823 35.939 16.287C36.1003 16.1843 36.225 16.045 36.313 15.869C36.401 15.693 36.445 15.4987 36.445 15.286C36.445 15.044 36.39 14.835 36.28 14.659C36.17 14.483 36.016 14.3437 35.818 14.241C35.62 14.1383 35.3927 14.087 35.136 14.087H33.431V16.43ZM41.9641 20.61C41.3554 20.61 40.8164 20.4817 40.3471 20.225C39.8851 19.9683 39.5221 19.62 39.2581 19.18C39.0014 18.7327 38.8731 18.223 38.8731 17.651C38.8731 17.1963 38.9464 16.782 39.0931 16.408C39.2397 16.034 39.4414 15.7113 39.6981 15.44C39.9621 15.1613 40.2737 14.9487 40.6331 14.802C40.9997 14.648 41.3994 14.571 41.8321 14.571C42.2134 14.571 42.5691 14.6443 42.8991 14.791C43.2291 14.9377 43.5151 15.1393 43.7571 15.396C43.9991 15.6453 44.1824 15.946 44.3071 16.298C44.4391 16.6427 44.5014 17.0203 44.4941 17.431L44.4831 17.904H39.7861L39.5331 16.98H43.3501L43.1741 17.167V16.925C43.1521 16.6977 43.0787 16.4997 42.9541 16.331C42.8294 16.155 42.6681 16.0193 42.4701 15.924C42.2794 15.8213 42.0667 15.77 41.8321 15.77C41.4727 15.77 41.1684 15.8397 40.9191 15.979C40.6771 16.1183 40.4937 16.32 40.3691 16.584C40.2444 16.8407 40.1821 17.1633 40.1821 17.552C40.1821 17.926 40.2591 18.2523 40.4131 18.531C40.5744 18.8097 40.7981 19.026 41.0841 19.18C41.3774 19.3267 41.7184 19.4 42.1071 19.4C42.3784 19.4 42.6277 19.356 42.8551 19.268C43.0824 19.18 43.3281 19.0223 43.5921 18.795L44.2631 19.73C44.0651 19.9133 43.8377 20.071 43.5811 20.203C43.3317 20.3277 43.0677 20.4267 42.7891 20.5C42.5104 20.5733 42.2354 20.61 41.9641 20.61ZM46.2204 20.5V14.186C46.2204 13.834 46.2974 13.5223 46.4514 13.251C46.6054 12.9723 46.8181 12.756 47.0894 12.602C47.3607 12.4407 47.6761 12.36 48.0354 12.36C48.2847 12.36 48.5157 12.404 48.7284 12.492C48.9411 12.5727 49.1207 12.6863 49.2674 12.833L48.8494 13.856C48.7541 13.7753 48.6514 13.713 48.5414 13.669C48.4387 13.625 48.3397 13.603 48.2444 13.603C48.0977 13.603 47.9731 13.6287 47.8704 13.68C47.7751 13.724 47.7017 13.7937 47.6504 13.889C47.6064 13.9843 47.5844 14.098 47.5844 14.23V20.5H46.9024C46.7704 20.5 46.6457 20.5 46.5284 20.5C46.4184 20.5 46.3157 20.5 46.2204 20.5ZM45.3184 16.012V14.824H48.8934V16.012H45.3184ZM50.0108 20.5V14.703H51.3418L51.3638 16.551L51.1768 16.133C51.2574 15.8397 51.3968 15.5757 51.5948 15.341C51.7928 15.1063 52.0201 14.923 52.2768 14.791C52.5408 14.6517 52.8158 14.582 53.1018 14.582C53.2264 14.582 53.3438 14.593 53.4538 14.615C53.5711 14.637 53.6664 14.6627 53.7398 14.692L53.3768 16.177C53.2961 16.133 53.1971 16.0963 53.0798 16.067C52.9624 16.0377 52.8451 16.023 52.7278 16.023C52.5444 16.023 52.3684 16.0597 52.1998 16.133C52.0384 16.199 51.8954 16.2943 51.7708 16.419C51.6461 16.5437 51.5471 16.6903 51.4738 16.859C51.4078 17.0203 51.3748 17.2037 51.3748 17.409V20.5H50.0108ZM57.1106 20.61C56.5019 20.61 55.9629 20.4817 55.4936 20.225C55.0316 19.9683 54.6686 19.62 54.4046 19.18C54.1479 18.7327 54.0196 18.223 54.0196 17.651C54.0196 17.1963 54.0929 16.782 54.2396 16.408C54.3862 16.034 54.5879 15.7113 54.8446 15.44C55.1086 15.1613 55.4202 14.9487 55.7796 14.802C56.1462 14.648 56.5459 14.571 56.9786 14.571C57.3599 14.571 57.7156 14.6443 58.0456 14.791C58.3756 14.9377 58.6616 15.1393 58.9036 15.396C59.1456 15.6453 59.3289 15.946 59.4536 16.298C59.5856 16.6427 59.6479 17.0203 59.6406 17.431L59.6296 17.904H54.9326L54.6796 16.98H58.4966L58.3206 17.167V16.925C58.2986 16.6977 58.2252 16.4997 58.1006 16.331C57.9759 16.155 57.8146 16.0193 57.6166 15.924C57.4259 15.8213 57.2132 15.77 56.9786 15.77C56.6192 15.77 56.3149 15.8397 56.0656 15.979C55.8236 16.1183 55.6402 16.32 55.5156 16.584C55.3909 16.8407 55.3286 17.1633 55.3286 17.552C55.3286 17.926 55.4056 18.2523 55.5596 18.531C55.7209 18.8097 55.9446 19.026 56.2306 19.18C56.5239 19.3267 56.8649 19.4 57.2536 19.4C57.5249 19.4 57.7742 19.356 58.0016 19.268C58.2289 19.18 58.4746 19.0223 58.7386 18.795L59.4096 19.73C59.2116 19.9133 58.9842 20.071 58.7276 20.203C58.4782 20.3277 58.2142 20.4267 57.9356 20.5C57.6569 20.5733 57.3819 20.61 57.1106 20.61ZM62.7749 20.61C62.2615 20.61 61.7995 20.5257 61.3889 20.357C60.9855 20.181 60.6555 19.9317 60.3989 19.609L61.2789 18.85C61.4989 19.0993 61.7445 19.279 62.0159 19.389C62.2872 19.499 62.5732 19.554 62.8739 19.554C62.9985 19.554 63.1085 19.5393 63.2039 19.51C63.3065 19.4807 63.3945 19.4367 63.4679 19.378C63.5412 19.3193 63.5962 19.2533 63.6329 19.18C63.6769 19.0993 63.6989 19.0113 63.6989 18.916C63.6989 18.74 63.6329 18.6007 63.5009 18.498C63.4275 18.4467 63.3102 18.3917 63.1489 18.333C62.9949 18.2743 62.7932 18.2157 62.5439 18.157C62.1479 18.0543 61.8179 17.937 61.5539 17.805C61.2899 17.6657 61.0845 17.5117 60.9379 17.343C60.8132 17.2037 60.7179 17.046 60.6519 16.87C60.5932 16.694 60.5639 16.5033 60.5639 16.298C60.5639 16.0413 60.6189 15.8103 60.7289 15.605C60.8462 15.3923 61.0039 15.209 61.2019 15.055C61.3999 14.901 61.6309 14.7837 61.8949 14.703C62.1589 14.6223 62.4339 14.582 62.7199 14.582C63.0132 14.582 63.2955 14.6187 63.5669 14.692C63.8455 14.7653 64.1022 14.8717 64.3369 15.011C64.5789 15.143 64.7842 15.3043 64.9529 15.495L64.2049 16.32C64.0655 16.188 63.9115 16.0707 63.7429 15.968C63.5815 15.8653 63.4165 15.7847 63.2479 15.726C63.0792 15.66 62.9215 15.627 62.7749 15.627C62.6355 15.627 62.5109 15.6417 62.4009 15.671C62.2909 15.693 62.1992 15.7297 62.1259 15.781C62.0525 15.8323 61.9939 15.8983 61.9499 15.979C61.9132 16.0523 61.8949 16.1403 61.8949 16.243C61.9022 16.331 61.9242 16.4153 61.9609 16.496C62.0049 16.5693 62.0635 16.6317 62.1369 16.683C62.2175 16.7343 62.3385 16.793 62.4999 16.859C62.6612 16.925 62.8702 16.9873 63.1269 17.046C63.4862 17.1413 63.7869 17.2477 64.0289 17.365C64.2709 17.4823 64.4615 17.618 64.6009 17.772C64.7402 17.904 64.8392 18.058 64.8979 18.234C64.9565 18.41 64.9859 18.6043 64.9859 18.817C64.9859 19.1617 64.8869 19.4697 64.6889 19.741C64.4982 20.0123 64.2342 20.225 63.8969 20.379C63.5669 20.533 63.1929 20.61 62.7749 20.61ZM66.1777 20.5V12.36H67.5197V15.891L67.2667 16.023C67.3401 15.759 67.4831 15.5207 67.6957 15.308C67.9084 15.088 68.1614 14.912 68.4547 14.78C68.7481 14.648 69.0487 14.582 69.3567 14.582C69.7967 14.582 70.1634 14.67 70.4567 14.846C70.7574 15.022 70.9811 15.286 71.1277 15.638C71.2817 15.99 71.3587 16.43 71.3587 16.958V20.5H70.0057V17.057C70.0057 16.7637 69.9654 16.5217 69.8847 16.331C69.8041 16.133 69.6794 15.99 69.5107 15.902C69.3421 15.8067 69.1367 15.7627 68.8947 15.77C68.6967 15.77 68.5134 15.803 68.3447 15.869C68.1834 15.9277 68.0404 16.0157 67.9157 16.133C67.7984 16.243 67.7031 16.3713 67.6297 16.518C67.5637 16.6647 67.5307 16.826 67.5307 17.002V20.5H66.8597C66.7277 20.5 66.6031 20.5 66.4857 20.5C66.3757 20.5 66.2731 20.5 66.1777 20.5Z" fill="white" />
                    </svg>
                </span>
            </div>
        </div>
        <div class="all-tables">
            <table>
                <tr>
                    <th>Sl No</th>
                    <th>Date</th>
                    <th>UserName</th>
                    <th>Title</th>
                    <th>MessageBody</th>
                    <th>Status</th>
                </tr>
                @foreach (var items in NotificationCollection.Select((value, i) => new { value, i }))
                {
                    <tr>
                        <td>@(((PageNumber - 1) * PageSize) + @items.i + 1)</td>
                        <td>@items.value?.Date?.ToString("dd/MM/yyyy")</td>
                        <td>@items.value?.UserName</td>
                        <td>@items.value?.Title</td>
                        <td>@items.value?.MessageBody</td>
                        <td>@items.value?.IsSuccess</td>
                    </tr>
                }
            </table>
        </div>
        <div class="tenantmasterfooter">
            <div class="pagesize">
                <div class="pagesizebox">
                    <select id="pagesizeinput" @bind="PageSize" @bind:after="@GetNotificationAsync">
                        @foreach (var dv in DropdownValues)
                        {
                            <option value="@dv">@dv</option>
                        }
                    </select>
                    <span> @ShowingCount of @TotalCount</span>
                </div>
            </div>
            <div class="pagenumber">
                @if (NotificationCollection?.Count < PageSize)
                {
                    <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)">Back</span>
                }
                else
                {
                    @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                    {
                        if (i < PageNumber || i == 0)
                        {
                            <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber(CollectionType)"> &lt;&lt; </span>
                        }
                        else if (i == PageNumber)
                        {
                            <span id="pagenumbercircle">@i</span>
                        }
                        else if (i > PageNumber)
                        {
                            <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber(CollectionType)">&gt;&gt;</span>
                        }
                    }
                }



            </div>
        </div>
    }
    
</div>