﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Dtos
{
    public class AdminUserActivityDto
    {
        public Guid Id { get; set; }
        public Dictionary<DateTime, string>? Activity { get; set; } = new();
        public string? Token { get; set; }
        public DateTime LoginTime { get; set; }
        public DateTime LogoutTime { get; set; }
        public Dictionary<DateTime ,string>? UserActivity { get; set; } = new();
        public Dictionary<DateTime , string>? TenantMasterSearchActivity { get; set; } = new();
        public Guid AdminUserId { get; set; }
    }
}
