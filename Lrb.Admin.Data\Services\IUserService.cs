﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.User;
using Lrb.Admin.Data.Models.Wrappers;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;

namespace Lrb.Admin.Data.Services
{
    public interface IUserService
    {
        Task<bool> CreateUserAsync(CreateUserModel model);
        Task<Response<bool>> CreateBulkUserAsync(IBrowserFile file,string fileName);
        Task<bool> CheckIfEmailAlreadyExistsAsync(string email, string readConnectionString);
        Task<bool> CheckIfUserNameExistsAsync(string userName, string readConnectionString);
        Task<UserModelDto> GetAdminUserByIdAsync(Guid id);
        Task<string> GetDefaultCountryCodeAsync(string tenantId, string readConnectionString);
        Task<UserCountDto> GetUserCountsAsync(string tenantId, string readConnectionString);
    }
}
