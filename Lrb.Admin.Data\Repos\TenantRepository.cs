﻿using Dapper;
using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos.Factory;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using System.Text.RegularExpressions;

namespace Lrb.Admin.Data.Repos
{
    public class TenantRepository : ITenantRepository
    {
        private readonly IDBConnectionFactory _dbFactory;
        protected readonly string _intervalForIST = "5:30";
        protected readonly string Schema = "tenantmaster";
        protected readonly string Schema1 = "LeadratBlack";
        protected readonly string Schema2 = "Identity";
        protected int TotalCount;
        protected Query Query;
        public TenantRepository(IDBConnectionFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        public async Task<bool> CompareTenantId(string TenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var innerquery = $"SELECT \"Id\"\r\nFROM \"MultiTenancy\".\"Tenants\"\r\nWHERE \"Id\" ILIKE '{TenantId}'";
                var result = connection.Query<string>(innerquery);
                connection.Close();
                if (result.Count() > 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> CompareDifferentDatabseTenantId(string TenantId, string connectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync(connectionString);
                var innerquery = $"SELECT \"Id\"\r\nFROM \"MultiTenancy\".\"Tenants\"\r\nWHERE \"Id\" ILIKE '{TenantId}'";
                var result = connection.Query<string>(innerquery);
                connection.Close();
                if (result.Count() > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Address> GetAddressAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Query = db.Query($"{Schema}.TenantAddress").Where("Id", "=", tenantId)
                    .Select("Locality","SubLocality", "City", "State", "PostalCode");
                connection.Close();
                return await Query.FirstOrDefaultAsync<Address>();
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
        }

        public async Task<PagedResponse<GetTenantModel, string>> GetAllData(int pageNumber, int pageSize, GetAllTenantParameter filter)
        {
            try
            {
                using (var connection = await _dbFactory.CreateReadConnectionAsync())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("from_date", filter?.FromDate);
                    parameters.Add("to_date", filter?.ToDate);
                    parameters.Add("datetype", (int?)filter?.DateType);
                    parameters.Add("searchid", filter?.TenantId);
                    parameters.Add("page_number", pageNumber);
                    parameters.Add("page_size", pageSize);
                    parameters.Add("no_of_user", filter?.NoOfUsers);
                    //parameters.Add("city", filter?.City);
                    parameters.Add("billing_type", filter?.BillingType);
                    parameters.Add("is_paid", filter?.IsPaid);
                    parameters.Add("is_testing_account", filter?.IsTestingAccount);
                    parameters.Add("is_dormented", filter?.IsDormented);
                    //parameters.Add("license_moniter", filter?.LicenseMonitor);
                    var sql = "SELECT * FROM tenantmaster.tenant_master_details(@from_date, @to_date, @datetype, @searchid, @page_number, @page_size,@no_of_user, @billing_type, @is_paid, @is_testing_account, @is_dormented)";
                    var result = await connection.QueryAsync<GetTenantModel>(sql, parameters);
                    var querycount = "SELECT count(\"TenantId\") FROM tenantmaster.tenant_master_details(@from_date, @to_date, @datetype, @searchid, NULL, NULL,@no_of_user, @billing_type, @is_paid, @is_testing_account, @is_dormented)";
                    var countResult = await connection.QueryAsync<int>(querycount, parameters);
                    return new PagedResponse<GetTenantModel, string>(result, countResult.FirstOrDefault());
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }


        }

        public async Task<PagedResponse<GetTenantUserModel, string>> GetUserByTenantId(UserFilter filter,string? readConnectionString)
        {
            try
            {
                using (var connection = await _dbFactory.CreateReadConnectionAsync(readConnectionString))
                {
                    var sql = $"SELECT * FROM \"tenantmaster\".gettenantprofile('{filter?.TenantId}','{filter?.SearchId ?? null}','{filter?.PageNumber}','{filter?.PageSize}')";
                    var result = await connection.QueryAsync<GetTenantUserModel>(sql);
                    var querycount = $"SELECT count('Id') FROM \"tenantmaster\".gettenantprofile('{filter?.TenantId}','{filter?.SearchId ?? null}',null,null)";
                    var countResult = (await connection.QueryAsync<int>(querycount)).FirstOrDefault();
                    connection.Close();
                    return new PagedResponse<GetTenantUserModel, string>(result, countResult);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }


        }

        public async Task<Query> BuildQueryAsync(InvoiceFilter parameter, Query query)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                if (!string.IsNullOrEmpty(parameter.search))
                {
                    var subQuery = new Query()
                        .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                        .Select("PaymentId")
                        .Where(q =>
                            q.WhereLike("TenantId", $"%{parameter.search.ToLower().Replace(" ", "")}%")
                             .OrWhereLike("TenantName", $"%{parameter.search.ToLower().Replace(" ", "")}%")
                             .OrWhereLike("InvoiceNumber", $"%{parameter.search.ToLower().Replace(" ", "")}%")
                        );

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.OnboardingFromDate != null && parameter.OnboardingFromDate != default) || (parameter.OnboardingToDate != null && parameter.OnboardingToDate != default))
                {
                    var subQuery = new Query()
                        .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                        .Select("PaymentId")
                        .Where(q =>
                            q.Where("DateofOnboard", ">=", parameter?.OnboardingFromDate)
                             .Where("DateofOnboard", "<=", parameter?.OnboardingToDate)
                        );

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.SubscriptionFromDate != null && parameter.SubscriptionFromDate != default) || (parameter.SubscriptionToDate != null && parameter.SubscriptionToDate != default))
                {
                    var subQuery = new Query()
                       .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                       .Select("PaymentId")
                       .Where(q =>
                           q.Where("SubscriptionDate", ">=", parameter?.SubscriptionFromDate)
                            .Where("SubscriptionDate", "<=", parameter?.SubscriptionToDate)
                       );

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.PaymentFromDate != null && parameter.PaymentFromDate != default) || (parameter.PaymentToDate != null && parameter.PaymentToDate != default))
                {
                    var subQuery = new Query()
                     .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                     .Select("PaymentId")
                     .Where(q =>
                         q.Where("TransactionDate", ">=", parameter?.PaymentFromDate)
                          .Where("TransactionDate", "<=", parameter?.PaymentToDate)
                     );

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.LicenseValidityFromDate != null && parameter.LicenseValidityFromDate != default) || (parameter.LicenseValidityToDate != null && parameter.LicenseValidityToDate != default))
                {
                    var subQuery = new Query()
                     .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                     .Select("PaymentId")
                     .Where(q =>
                         q.Where("LicenseValidity", ">=", parameter?.LicenseValidityFromDate)
                          .Where("LicenseValidity", "<=", parameter?.LicenseValidityToDate)
                     );

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.TotalAmountPaidfrom != null) || (parameter.TotalAmountPaidto != null))
                {
                    var subQuery = new Query()
                     .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                     .Select("PaymentId")
                     .Where(q =>
                         q.Where("TotalAmountPaid", ">=", parameter?.TotalAmountPaidfrom)
                          .Where("TotalAmountPaid", "<=", parameter?.TotalAmountPaidto)
                     );
                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.TotalPackageAmountfrom != null) || (parameter.TotalPackageAmountto != null))
                {
                    var subQuery = new Query()
                     .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                     .Select("PaymentId")
                     .Where(q =>
                         q.Where("TotalPackageAmount", ">=", parameter?.TotalPackageAmountfrom)
                          .Where("TotalPackageAmount", "<=", parameter?.TotalPackageAmountto)
                     );
                    query = query.WhereIn("PaymentId", subQuery);
                }
                if ((parameter.NetDueAmountfrom != null) || (parameter.NetDueAmountto != null))
                {
                    var subQuery = new Query()
                     .FromRaw("\"tenantmaster\".GetInvoiceDetails()")
                     .Select("PaymentId")
                     .Where(q =>
                         q.Where("DueAmount", ">=", parameter?.NetDueAmountfrom)
                          .Where("DueAmount", "<=", parameter?.NetDueAmountto)
                     );
                    query = query.WhereIn("PaymentId", subQuery);
                }
                if (parameter.PaymentMode != default)
                {
                    var subQuery = new Query().FromRaw("\"tenantmaster\".GetInvoiceDetails()").Select("PaymentId");

                    if (parameter.PaymentMode == PaymentMode.UPI)
                    {
                        subQuery = subQuery.Where("PaymentMode", 0);
                    }
                    else if (parameter.PaymentMode == PaymentMode.Cashfree)
                    {
                        subQuery = subQuery.Where("PaymentMode", 1);
                    }
                    else if (parameter.PaymentMode == PaymentMode.IMPS)
                    {
                        subQuery = subQuery.Where("PaymentMode", 2);
                    }
                    else if (parameter.PaymentMode == PaymentMode.NEFT)
                    {
                        subQuery = subQuery.Where("PaymentMode", 3);
                    }
                    else if (parameter.PaymentMode == PaymentMode.RTGS)
                    {
                        subQuery = subQuery.Where("PaymentMode", 4);
                    }
                    else if (parameter.PaymentMode == PaymentMode.Cheque)
                    {
                        subQuery = subQuery.Where("PaymentMode", 5);
                    }
                    else if (parameter.PaymentMode == PaymentMode.Cash)
                    {
                        subQuery = subQuery.Where("PaymentMode", 6);
                    }
                    else if (parameter.PaymentMode == PaymentMode.PhonePe)
                    {
                        subQuery = subQuery.Where("PaymentMode", 7);
                    }
                    else if (parameter.PaymentMode == PaymentMode.International)
                    {
                        subQuery = subQuery.Where("PaymentMode", 8);
                    }

                    query = query.WhereIn("PaymentId", subQuery);
                }
                if (parameter.BillingType != default)
                {
                    var subQuery = new Query().FromRaw("\"tenantmaster\".GetInvoiceDetails()").Select("PaymentId");

                    if (parameter.BillingType == BillingType.Quarterly)
                    {
                        subQuery = subQuery.Where("BillingType", 1);
                    }
                    else if (parameter.BillingType == BillingType.Halfyearly)
                    {
                        subQuery = subQuery.Where("BillingType", 2);
                    }
                    else if (parameter.BillingType == BillingType.Yearly)
                    {
                        subQuery = subQuery.Where("BillingType", 3);
                    }
                    query = query.WhereIn("PaymentId", subQuery);
                }

                return query;
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<GetTenantLeadsModel> GetLeadByIdAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Query = db.Query($"{Schema}.TenantLeads").Where("TenantId", "=", tenantId).
                    Select("Date", "Name", "PhoneNumber", "Status", "Source", "EnquiredFor", "NoOfBHK", "BHKType");
                connection.Close();
                return await Query.FirstOrDefaultAsync<GetTenantLeadsModel>();
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
        }
        public async Task<GetTenantModel> GetTenantByIdAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                //Query = db.Query($"{Schema}.TenantMasterFilters_1").Where("TenantId", "=", tenantId).
                //        Select("TenantId", "TenantName",
                //        "DateOfOnBoard", "LicenseValidity", "City", "Vendors", "NoOfUsers", "ActiveUsers", "InactiveUser",
                //        "PropertyCount", "AssignedLeads", "UnassignedLeads", "SoldLicenses", "GSTNumber", "NetAmount", "GSTAmount",
                //        "TotalAmount", "PaidAmount", "DueAmount", "BillingType");
                Query = db.Query().FromRaw($"{Schema}.tenant_master_details()").Where("TenantId", "=", tenantId);
                connection.Close();
                return await Query.FirstOrDefaultAsync<GetTenantModel>();
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
        }

        public async Task<PagedResponse<GetTenantIntegrationModel, string>> GetTenantIntegrationInfo(string tenantId, int pageNumber, int pagerSize, GetAllTenantParameter filter,string? readConnectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync(readConnectionString);
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await TotalIntegrationCount(tenantId, filter);
                if (filter != default)
                {
                    Query = db.Query($"{Schema1}.IntegrationAccountInfo").WhereFalse("IsDeleted")
                    .Where("TenantId", "=", tenantId).
                    Select("LeadSource", "AccountName", "IsDeleted", "LeadCount", "FileUrl");
                }
                if (filter.Status != null)
                {
                    Query = db.Query($"{Schema1}.IntegrationAccountInfo").Where("TenantId", "=", tenantId)
                    .Where("IsDeleted", "=", filter.Status).
                    Select("LeadSource", "AccountName", "IsDeleted", "LeadCount", "FileUrl");
                }
                Query = Query.Skip((pageNumber - 1) * pagerSize).Take(pagerSize);
                IEnumerable<GetTenantIntegrationModel> tenantIntegration = await Query.GetAsync<GetTenantIntegrationModel>();
                connection.Close();
                return new PagedResponse<GetTenantIntegrationModel, string>(tenantIntegration, count);
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
        }
        public async Task<PagedResponse<GetTenantLeadsModel, string>> GetTenantLeadAsync(string tenantId, int pageNumber, int pagerSize, GetAllTenantParameter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await TotalLeadsCount(filter);
                var query = db.Query($"{Schema}.TenantLeads").Where($"TenantId", "=", tenantId).
                    Select("Date", "Name", "PhoneNumber", "Status", "Source", "EnquiredFor", "NoOfBHK", "BHKType");
                query = query.Skip((pageNumber - 1) * pagerSize).Take(pagerSize);
                IEnumerable<GetTenantLeadsModel> tenantsLeads = await query.GetAsync<GetTenantLeadsModel>();
                connection.Close();
                return new PagedResponse<GetTenantLeadsModel, string>(tenantsLeads, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<GetTenantUserModel> GetUserByIdAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var sql = $"SELECT * FROM \"tenantmaster\".gettenantprofile('{tenantId}',null,null,null)";
                var result = await connection.QueryAsync<GetTenantUserModel>(sql);
                connection.Close();
                return await Query.FirstOrDefaultAsync<GetTenantUserModel>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<int> TotalIntegrationCount(string tenantId, GetAllTenantParameter filter)
        {
            using var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema1}.IntegrationAccountInfo").Where("TenantId", "=", tenantId).Select("count(\"TenantId\")");
            connection.Close();
            return await query.CountAsync<int>();
        }
        public async Task<int> TotalLeadsCount(GetAllTenantParameter filter)
        {
            using var connection = await _dbFactory.CreateReadConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = db.Query($"{Schema}.TenantLeads").Select("count(\"TenantId\")");
            connection.Close();
            return await query.CountAsync<int>();
        }

        public async Task<int> TotalTenantCount(InvoiceFilter filter)
        {
            try
            {
                using var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query().FromRaw($"\"tenantmaster\".GetInvoiceDetails()").Select("count(\"PaymentId\")");
                query = await BuildQueryAsync(filter, query);
                connection.Close();
                return await query.CountAsync<int>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }

        public async Task<Response<bool>> UpdateTenantStatusAsync(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var result = db.Query($"{Schema}.Vendor").Where("TenantId", "=", tenantId)
                    .Select("GharOfficeTenant");
                bool gharofficetenantStatus = await result.FirstOrDefaultAsync<bool>();
                await result.UpdateAsync(new { GharOfficeTenant = !gharofficetenantStatus });
                return new Response<bool>(!gharofficetenantStatus);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<TenantProfile> GetTenantWebsite(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Query = db.Query($"{Schema1}.Profiles").Where("TenantId", "=", tenantId).WhereNotNull("Website").Select("Website");
                connection.Close();
                return await Query.FirstOrDefaultAsync<TenantProfile>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }
        public async Task<List<string>> GetTenantId()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Query = db.Query($"{Schema}.TenantMasterFilters_1").WhereNotNull("TenantId").Select("TenantId").OrderBy();
                List<string> record = (List<string>)await Query.GetAsync<string>();
                connection.Close();
                return record;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<int> CountAdminAsync(string tenantId,string readConnectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync(readConnectionString);
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = $"select count(u.\"Id\")from \"Identity\".\"Users\" u\r\n" +
                    $"left join \"Identity\".\"UserRoles\" ur on ur.\"UserId\" = u.\"Id\"\r\n" +
                    $"left join \"Identity\".\"Roles\" r on r.\"Id\" = ur.\"RoleId\"\r\n" +
                    $"where u.\"TenantId\" = '{tenantId}' and r.\"Name\" = 'Admin' and u.\"UserName\" not ilike '%.admin%'";
                int count = (await connection.QueryAsync<int>(query)).FirstOrDefault();
                connection.Close();
                return count;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<IEnumerable<string>> GetAllUserId(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                    var query = $"SELECT \"Id\" FROM \"tenantmaster\".gettenantprofile('{tenantId}',null,null,null)";
                    IEnumerable<string> ListOfIds = (await connection.QueryAsync<string>(query));
                    connection.Close();
                return ListOfIds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<Response<bool>> UpdateTenantValidityAsync(string tenantId, DateTime? validityDate)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = await db.Query($"MultiTenancy.Tenants").Where("Id", "=", tenantId).UpdateAsync(new { ValidUpto = validityDate });
                var subscriptionData = await db.Query("LeadratBlack.Subscriptions")
                .Where("TenantId", "=", tenantId)
                .Where("LicenseValidity", ">", DateTime.UtcNow)
                .FirstOrDefaultAsync<LRBSubscription>();
                if (subscriptionData != null)
                {
                    var query1 = await db.Query($"LeadratBlack.Subscriptions").Where("Id", "=", subscriptionData.Id)
                        .UpdateAsync(new { LicenseValidity = validityDate });
                }

                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<TenantViewModelExport>> GetTenantExport(GetAllTenantParameter filter)
        {
            try
            {
                using (var connection = await _dbFactory.CreateReadConnectionAsync())
                {
                    var parameters = new DynamicParameters();
                    var count = await GetAllTenantCountAsync();
                    parameters.Add("from_date", filter?.FromDate);
                    parameters.Add("to_date", filter?.ToDate);
                    parameters.Add("datetype", (int?)filter?.DateType);
                    parameters.Add("searchid", filter?.TenantId);
                    parameters.Add("page_number", null);
                    parameters.Add("page_size", null);
                    parameters.Add("no_of_user", filter?.NoOfUsers);
                    parameters.Add("billing_type", filter?.BillingType);
                    parameters.Add("is_paid", filter?.IsPaid);
                    parameters.Add("is_testing_account", filter?.IsTestingAccount);
                    parameters.Add("is_dormented", filter?.IsDormented);
                    var sql = "SELECT * FROM tenantmaster.tenant_master_details(@from_date, @to_date, @datetype, @searchid, @page_number, @page_size, @no_of_user, @billing_type, @is_paid, @is_testing_account, @is_dormented)";
                    var result = await connection.QueryAsync<TenantViewModelExport>(sql, parameters);
                    return result.ToList();
                }
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }


        public async Task<List<TenantCompleteDetailsDto>> GetCompleteTenantData()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var newQuery = $"Select t.\"TenantId\", t.\"TenantName\", t.\"DateOfOnboard\" ," +
                    $" t.\"LicenseExpiry\" , t.\"City\", t.\"NoOfUsers\"," +
                    $" t.\"ActiveUsers\" , t.\"InActiveUsers\", t.\"SoldLicenses\",t.\"NetAmount\",t.\"GSTAmount\",t.\"TotalAmount\", t.\"PaidAmount\", t.\"DueAmount\",t.\"BillingType\", " +
                    $" t.\"MagicBricks\",t.\"NinetyNineAcres\",t.\"Housing\", t.\"QuikrHomes\", t.\"Website\", t.\"EstateDekho\",  t.\"OLX\" ,t.\"RealEstateIndia\",t.\"CommonFloor\"," +
                    $"t.\"Gmail\",t.\"JustLead\",t.\"Facebook\",t.\"GoogleAds\"From \"{Schema}\".\"TenantCompleteData_1\" t";
                var list = (await connection.QueryAsync<TenantCompleteDetailsDto>(newQuery)).ToList();
                connection.Close();
                return list;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<GetTenantUserModel> GetUserFromIdAsync(string id)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                Guid userId = Guid.Parse(id.ToString());
                var query = db.Query($"{Schema2}.Users").Where($"Id", "=", id).Select();
                connection.Close();
                return query.FirstOrDefault<GetTenantUserModel>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<List<string>> GetAllTenantAsync()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"MultiTenancy.Tenants").Select("Id");
                IEnumerable<string> tenantIds = await query.GetAsync<string>();
                connection.Close();
                return tenantIds.ToList();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<int> GetAllTenantCountAsync()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"MultiTenancy.Tenants").Select("count(\"Id\")");
                var count = await query.CountAsync<int>();
                connection.Close();
                return count;

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> ToggleIsActive(string userId,bool value)
        {
            var connection = await _dbFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var query = await db.Query($"{Schema2}.Users").Where("Id", "=", userId).UpdateAsync(new {IsActive = value});
            if(query > 0)
            {
                return true;
            }
            return false;
        }

        public async Task<bool> ToggleSubscriptionIsActive(Guid SubscriptionId, bool value)
        {
            var connection = await _dbFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var updateData = new
            {
                IsActive = value,
                IsExpired = !value,
                ExpiredOn = value ? (DateTime?)null : (DateTime?)DateTime.UtcNow
            };
            var query = await db.Query($"{Schema1}.Subscriptions").Where("Id", "=", SubscriptionId).UpdateAsync(updateData);
            
            
            if(query > 0)
            {
                return true;
            }
            return false;
        }

        public async Task<bool> ToggleSubscriptionAddonIsActive(Guid SubscriptionAddonId, bool value)
        {
            var connection = await _dbFactory.CreateConnectionAsync();
            var db = new QueryFactory(connection, new PostgresCompiler());
            var updateData = new
            {
                IsActive = value,
                IsExpired = !value,
                ExpiredOn = value ? (DateTime?)null : (DateTime?)DateTime.UtcNow
            };
            var query = await db.Query($"{Schema1}.SubscriptionAddOns").Where("Id", "=", SubscriptionAddonId).UpdateAsync(updateData);

            if (query > 0)
            {
                return true;
            }
            return false;
        }
        public async Task UpdateIsDormented()
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"UPDATE \"tenantmaster\".\"TenantsInfo\" AS ti\r\nSET \"IsDormented\" = \r\nCASE " +
                    $"\r\nWHEN not EXISTS (\r\nSELECT 1\r\nFROM \"LeadratBlack\".\"Leads\" AS l\r\n " +
                    $"WHERE l.\"TenantId\" = ti.\"Id\"\r\nAND l.\"CreatedOn\" >= current_date - interval '7 days'\r\n)" +
                    $" THEN true\r\nELSE false\r\nEND;";
                var details = await connection.QueryAsync(query);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTestingAccount(string tenantId, bool toggleValue)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"update \"tenantmaster\".\"TenantsInfo\" set \"IsTestingAccount\" = {toggleValue} where \"TenantsInfo\".\"Id\" = '{tenantId}'\r\n";
                var details = await connection.QueryAsync(query);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> GetAccountStatus(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var query = $"select \"IsTestingAccount\" from tenantmaster.\"TenantsInfo\" where \"Id\"='{tenantId}'";
                var details = await connection.QueryAsync<bool>(query);
                return details.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<TotalSubscriptionDetails> GetTotalSubscriptionDetails(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"select * from tenantmaster.getsubscriptiondetails('{tenantId}')";
                var details = await connection.QueryAsync<TotalSubscriptionDetails>(query);
                return details.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);

            }
        }

        public async Task<TenantsDto> GetTenantInfo(string tenantId)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"SELECT * FROM \"MultiTenancy\".\"Tenants\" where \"Id\" = '{tenantId}' ";
                var details = await connection.QueryAsync<TenantsDto>(query);
                return details.FirstOrDefault();  
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<Response<bool>> CreateDifferentDatabseTenant(string tenantId, string connectionString)
        {
            bool IsCreated = false;
            try
            {
                bool isTenantAlreadyExcist = await CompareDifferentDatabseTenantId(tenantId, connectionString);
                if (isTenantAlreadyExcist)
                {
                    IsCreated = true;
                }
                else
                {
                    TenantsDto tenantInfo = await GetTenantInfo(tenantId);
                    if(tenantInfo != null)
                    {
                        var connection = await _dbFactory.CreateConnectionAsync(connectionString);
                        var db = new QueryFactory(connection, new PostgresCompiler());
                        var query = await db.Query("MultiTenancy.Tenants").InsertAsync(tenantInfo);
                        connection.Close();
                        return new Response<bool>(query > 0);
                    }
                }
                return new(IsCreated);
            }
            catch(Exception ex)
            {
                return new(IsCreated);
            }
        }

        public async Task<Response<bool>> CreateTenantInfo(TenantsInfo tenantsInfo, bool isTestIngAccount)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                tenantsInfo.CreatedOn = DateTime.UtcNow;
                tenantsInfo.IsDormented = false;
                tenantsInfo.IsTestingAccount = isTestIngAccount;
                var query = await db.Query("tenantmaster.TenantsInfo").InsertAsync(tenantsInfo);
                connection.Close();
                return new Response<bool>(query > 0);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }



        public async Task<PagedResponse<InvoiceDto, string>> GetInvoiceDetail(int pageNumber, int pageSize, InvoiceFilter filter)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var count = await TotalTenantCount(filter);

                if (filter != default)
                {
                    Query = db.Query().FromRaw($"\"tenantmaster\".GetInvoiceDetails()").Select("PaymentId", "TenantId", "TenantName", "InvoiceStatus", "DateofOnboard",
                        "SubscriptionDate", "TransactionDate", "NoOfLicenses", "LicenseValidity", "VendorNames", "InvoiceNumber", "GSTNumber",
                        "TotalPackageAmount", "NetAmountPaid", "GSTAmount", "TotalAmountPaid", "DueAmount", "DueDate", "TDSDeductions","Address", "BillingType",
                        "PaymentMode", "SubscriptionType").OrderByDesc("TransactionDate");
                }
                Query = Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
                Query = await BuildQueryAsync(filter, Query);
                IEnumerable<InvoiceDto> InvoiceData = await Query.GetAsync<InvoiceDto>();
                connection.Close();
                return new PagedResponse<InvoiceDto, string>(InvoiceData, count);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateInvoiceNo(string OldInvoiceNo, string NewInvoiceNo, Guid PaymentID)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"update tenantmaster.\"Invoice\" Set \"InvoiceNo\" = '{OldInvoiceNo}' where \"InvoiceNo\" = '{NewInvoiceNo}'";
                var details = await connection.QueryAsync(query);
                var query2 = $"update tenantmaster.\"Invoice\" set \"InvoiceNo\" = '{NewInvoiceNo}' where \"PaymentId\" = '{PaymentID}'";
                var details2 = await connection.QueryAsync(query2);
                connection.Close();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTDSPercentage(InvoiceDto InvoiceTDS)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"UPDATE tenantmaster.\"Invoice\"\r\nSET \"TDSPercent\" = '{InvoiceTDS.TDSDeductions}',\r\n    \"LastModifiedOn\" = NOW()\r\nWHERE \"PaymentId\" = '{InvoiceTDS.PaymentId}'";
                var details = await connection.QueryAsync(query);
                connection.Close();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> UpdateTransactionDate(InvoiceDto InvoiceUpdate)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync();
                var query = $"UPDATE tenantmaster.\"Invoice\"\r\n\tSET \"CreatedOn\" = '{InvoiceUpdate.TransactionDate?.ToUniversalTime().ToString("yyyy/MM/dd HH:mm:ss")}', \"LastModifiedOn\"= NOW()\r\n\tWHERE \"PaymentId\" = '{InvoiceUpdate.PaymentId}'";
                var details = await connection.QueryAsync(query);
                var query2 = $"UPDATE \"LeadratBlack\".\"Payments\" SET \"CreatedOn\" = '{InvoiceUpdate.TransactionDate?.ToUniversalTime().ToString("yyyy/MM/dd HH:mm:ss")}', \"LastModifiedOn\"= NOW()  WHERE \"Id\" = '{InvoiceUpdate.PaymentId}'";
                var details2 = await connection.QueryAsync(query2);
                connection.Close();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UpdateInvoiceDto> GetInvoiceById(Guid? id)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var innerquery = $"SELECT * FROM tenantmaster.\"Invoice\" where \"PaymentId\" = '{id}'";
                var result = await connection.QueryAsync<UpdateInvoiceDto>(innerquery);
                connection.Close();
                return result.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<GetCustomListingSourcesDto>> GetCustomListingSource()
        {
            var connection = await _dbFactory.CreateReadConnectionAsync();
            var query = $"SELECT * FROM \"{Schema1}\".\"CustomListingSources\" where \"TenantId\" = 'root'";
            var details = await connection.QueryAsync<GetCustomListingSourcesDto>(query);
            return details.ToList();
        }

        public async Task<bool> PostCustomListingSources(string tenantId,string connectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync(connectionString);
                var db = new QueryFactory(connection, new PostgresCompiler());
                var getRootData = await GetCustomListingSource();
                foreach(var newData in getRootData)
                {
                    newData.Id = Guid.NewGuid();
                    newData.TenantId = tenantId;
                    newData.CreatedOn = DateTime.UtcNow;
                    newData.CreatedBy = Guid.Empty;
                    newData.LastModifiedOn = DateTime.UtcNow;
                    newData.LastModifiedBy = Guid.Empty;
                    var query = await db.Query($"{Schema1}.CustomListingSources").InsertAsync(newData);
                }
                connection.Close();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error Getting Invoice: {ex.Message}");
            }
        }

        public async Task<IEnumerable<string>> GetInvoiceNo()
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync();
                var db = new QueryFactory(connection, new PostgresCompiler());
                var query = db.Query($"tenantmaster.Invoice").Select("InvoiceNo");
                IEnumerable<string> InvoiceNo = await query.GetAsync<string>();
                connection.Close();
                return InvoiceNo.ToList();

            }
            catch (Exception ex)
            {
                throw new Exception($"Error Getting Invoice: {ex.Message}");
            }
        }

        public async Task<ProfileDto> GetProfileDetails(string tenantId,string readConnectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateReadConnectionAsync(readConnectionString);
                var innerquery = $"select p.\"Id\",p.\"TenantId\",p.\"GSTNumber\",p.\"AddressId\",\r\na.\"PlaceId\",a.\"SubLocality\",\r\na.\"Locality\", a.\"District\", a.\"City\"," +
                    $"a.\"State\",a.\"Country\",a.\"PostalCode\" \r\nfrom \"LeadratBlack\".\"Profiles\" p\r\nleft join (\r\nselect \r\n\"Addresses\".\"Id\"," +
                    $"\r\n\"Addresses\".\"PlaceId\",\r\n\"Addresses\".\"SubLocality\",\r\n\"Addresses\".\"Locality\",\r\n\"Addresses\".\"District\",\r\n\"Addresses\".\"City\"," +
                    $"\r\n\"Addresses\".\"State\",\r\n\"Addresses\".\"Country\",\r\n\"Addresses\".\"PostalCode\"\r\nfrom \"LeadratBlack\".\"Addresses\"\r\nwhere " +
                    $"\"Addresses\".\"IsDeleted\" = false\r\n) a on \"a\".\"Id\" = \"p\".\"AddressId\"\r\nwhere \"p\".\"IsDeleted\" = false and p.\"TenantId\" = '{tenantId}'";
                var result = await connection.QueryAsync<ProfileDto>(innerquery);
                connection.Close();
                return result.FirstOrDefault();
            }
            catch(Exception ex)
            {
                throw new Exception($"Error Getting Invoice: {ex.Message}");
            }
        }

        public async Task<bool> UpdateTenantProfile(ProfileDto profileData, string connectionString)
        {
            try
            {
                var connection = await _dbFactory.CreateConnectionAsync(connectionString);
                var db = new QueryFactory(connection, new PostgresCompiler());
                if (profileData.AddressId != null)
                {
                    var profileUpdateResponse = db.Query($"{Schema1}.Profiles").Where("Id", "=", profileData.Id).Update(new { GSTNumber = profileData.GSTNumber });
                    var AddressUpdateResponse = db.Query($"{Schema1}.Addresses").Where("Id", "=", profileData.AddressId).Update(new
                    {
                        PlaceId = profileData.PlaceId,
                        SubLocality = profileData.SubLocality,
                        Locality = profileData.Locality,
                        District = profileData.District,
                        City = profileData.City,
                        State = profileData.State,
                        Country = profileData.Country,
                        PostalCode = profileData.PostalCode
                    });
                    connection.Close();
                    if (AddressUpdateResponse >= 1 && profileUpdateResponse >= 1)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    Guid AddressId = Guid.NewGuid();
                    var AddressUpdateResponse = db.Query($"{Schema1}.Addresses").Insert(new
                    {
                        Id = AddressId,
                        PlaceId = profileData.PlaceId,
                        SubLocality = profileData.SubLocality,
                        Locality = profileData.Locality,
                        District = profileData.District,
                        City = profileData.City,
                        State = profileData.State,
                        Country = profileData.Country,
                        PostalCode = profileData.PostalCode,
                        IsGoogleMapLocation = false,
                        IsDeleted = false
                    });
                    var profileUpdateResponse = db.Query($"{Schema1}.Profiles").Where("Id", "=", profileData.Id).Update(new { GSTNumber = profileData.GSTNumber, AddressId = AddressId });
                    connection.Close();
                    if (AddressUpdateResponse >= 1 && profileUpdateResponse >= 1)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}