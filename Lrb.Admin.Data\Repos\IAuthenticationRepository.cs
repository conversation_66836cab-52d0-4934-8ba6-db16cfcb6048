﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface IAuthenticationRepository
    {
        Task<Response<string>> ValidateUser(UserLoginDto model);
        Task<UserModelDto> FindUserName(string userName);
        Task<bool> RegisterUser(UserModelDto model);
        Task<UserModelDto> FindUserPassword(string password);
        Task<Response<bool>> UpdateUserAsync(Guid id, UserModelDto model);
        Task<UserModelDto> GetUserByIdAsync(Guid id);
    }
}
