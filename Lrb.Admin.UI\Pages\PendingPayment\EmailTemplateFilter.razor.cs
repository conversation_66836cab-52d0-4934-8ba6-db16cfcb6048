﻿using Lrb.Admin.Data.Filters;
using Microsoft.AspNetCore.Components;

namespace Lrb.Admin.UI.Pages.PendingPayment
{
    public partial class EmailTemplateFilter
    {
        [Parameter]
        public int PageNumber { get; set; }
        [Parameter]
        public int PageSize { get; set; }
        [Parameter]
        public List<int>? Count { get; set; }
        [Parameter] 
        public List<string>? LicenseMonitor { get; set; }
        [Parameter]
        public PendingPaymentFilterParameter? MailTemaplate { get; set; } = new();
        [Parameter]
        public Func<Task>? GetEmailTemplateAsync { get; set; }

        private async Task SendDueAmountReminderEmailTemplate()
        {
            await GetEmailTemplateAsync.Invoke();
            StateHasChanged();
        }

        private async Task ResetTemplate()
        {
            MailTemaplate.VendorEmail = default!;
            MailTemaplate.AdminEmail = default!;
            MailTemaplate.Sender = default!;
            MailTemaplate.Subject = null;
            MailTemaplate.AttachedFiles = null;
            MailTemaplate.ToRecipients = default!;
            MailTemaplate.CcRecipients = default!;
            MailTemaplate.BccRecipients = default!;
        }
    }
}