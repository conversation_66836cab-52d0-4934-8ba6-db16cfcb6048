﻿using Blazored.LocalStorage;
using iText.Forms;
using iText.Forms.Fields;
using iText.IO.Font.Constants;
using iText.Kernel.Font;
using iText.Kernel.Pdf;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Models.Common;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Roles;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using PhoneNumbers;
using Radzen;
using Lrb.Admin.Data.Models.Invoice;
using System.Text.RegularExpressions;
using Lrb.Admin.Data.Utility;

namespace Lrb.Admin.UI.Pages.Tenant
{
    [Authorize(Roles = "Admin,Manager")]
    public partial class AddTenant
    {
        public const string DefaultPassword = "123Pa$$word!";
        [Inject]
        private ITenantService TenantService { get; set; }
        [Inject]
        private IRoleService RoleService { get; set; }
        [Inject]
        private IUserService UserService { get; set; }
        [Inject]
        public IJSRuntime _jsRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IPaymentService PaymentService { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public DialogService dailogService { get; set; }
        [Inject]
        public IConfiguration configuration { get; set; }
        public CreateTenantWithAdminUserModel Model { get; set; } = new CreateTenantWithAdminUserModel();
        public AddTenantSubscriptionDto SubscriptionsModel { get; set; } = new AddTenantSubscriptionDto();
        public LRBSubscription Subscription { get; set; } = new();
        public bool PartPayment { get; set; } = false;
        public string? Token { get; set; }
        public List<int> Parts { get; set; } = new() { 1, 2, 3, 4, 5, 6 };
        public ElementReference DynamicPayemntPart { get; set; }
        public AuthenticationStateProvider Auth { get; set; }
        public NavigationManager Navigation { get; set; }
        public TenantsInfo? tenantsInfo { get; set; } = new();
        public List<string> Vendors { get; set; } = new();
        public List<string> VendorNames { get; set; } = new();
        public string? GSTNumber { get; set; }
        private string IsTDSApplicable { get; set; }
        private float? TDSNumber { get; set; }
        private TenantCountryType countryType { get; set; }
        private string? ConnectionString { get; set; }
        private string? ReadConnectionString { get;set; }
        private string? DubaiConnectionString { get; set; }
        private string? DubaiReadConnectionString { get; set; }
        private bool IsTenantOriginAvailable { get; set; } = false;
        List<string> fileData = new List<string>();
        public int progress;
        public bool showProgress;
        public bool showComplete;
        public string? completionMessage;
        public bool cancelUpload = false;
        private decimal? TDSAmount;
        private IBrowserFile? selectedFile;

        protected override async Task OnInitializedAsync()
        {
            ConnectionString = configuration.GetConnectionString("DefaultConnection");
            ReadConnectionString = configuration.GetConnectionString("ReadReplicaConnectionString");
            DubaiConnectionString = configuration.GetConnectionString("DubaiConnectionString") ?? string.Empty;
            DubaiReadConnectionString = configuration.GetConnectionString("DubaiReadReplicaConnectionString") ?? string.Empty;
            VendorNames = await VendorService.GetVendorsAllNames();
            //Model.IsDubaiTenant = false;
        }

        private async Task HandleTDSFileUpload(InputFileChangeEventArgs e)
        {
            selectedFile = e.File;
        }
        private void OnCountryChanged(ChangeEventArgs e)
        {
            countryType = Enum.Parse<Lrb.Admin.Data.Enums.TenantCountryType>(e.Value.ToString());
            if(countryType == Lrb.Admin.Data.Enums.TenantCountryType.Select)
            {
                IsTenantOriginAvailable = false;
            }
            else
            {
                IsTenantOriginAvailable = true;
            }


            if (countryType == Lrb.Admin.Data.Enums.TenantCountryType.Dubai)
            {
                Model.OrganizationInfo.Address.City = "Dubai";
                Model.OrganizationInfo.Address.State = "Dubai";
                Model.OrganizationInfo.Address.Country = "UAE";
                GSTNumber = "NA";
                SubscriptionsModel.GstInclude = false;
                Model.ConnectionString = DubaiConnectionString;
                Model.ReadReplicaConnectionString = DubaiReadConnectionString;
                //Model.IsDubaiTenant = true;
            }

            else
            {
                Model.OrganizationInfo.Address.City = default;
                Model.OrganizationInfo.Address.State = default;
                Model.OrganizationInfo.Address.Country = default;
                GSTNumber = default;
                SubscriptionsModel.GstInclude = true;
            }
        }
        public async Task AddTenantAsync()
        {
            var isCreated = false;
            try
            {
                var isId = TenantService.CompareTenantIdAsync(Model.Id.Trim()).Result;
                if (Model.AdminUser.PhoneNumber != null)
                {
                    Model.AdminUser.PhoneNumber = Model.AdminUser.PhoneNumber.Replace(" ", "");
                }
                if (Model.OrganizationInfo.PhoneNumber != null)
                {
                    Model.OrganizationInfo.PhoneNumber = Model.OrganizationInfo.PhoneNumber.Replace(" ", "");
                }
                if (!isId)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", "TenantId Already Exist");
                    return;
                }
                if (!Model.AdminUser.PhoneNumber.StartsWith("+"))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", "Please include the country code with the phone number (e.g., +91 for India).");
                    return;
                }
                var response = ValidateContactNumbers(Model.AdminUser.PhoneNumber);
                if (response != Model.AdminUser.PhoneNumber)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Add Correct Admin contect no.");
                    return;
                }
                if (Model.AdminUser.UserName.Length < 6)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"UserName should contain atleast 6 letters");
                    return;
                }
                if (await UserService.CheckIfEmailAlreadyExistsAsync(Model?.AdminEmail ?? string.Empty, Model?.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Company Email already exists");
                    return;
                }
                if (await UserService.CheckIfEmailAlreadyExistsAsync(Model?.AdminUser.Email ?? string.Empty,Model?.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Admin Email already exists");
                    return;
                }
                if (await UserService.CheckIfUserNameExistsAsync(Model?.AdminUser.UserName ?? string.Empty, Model?.ReadReplicaConnectionString ?? string.Empty))
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Admin Username already exists");
                    return;
                }
                if (GSTNumber == null)
                {
                    await _jsRuntime.InvokeVoidAsync("alert", $"Please Add GST Number If not available add NA");
                    return;
                }
                GSTNumber = GSTNumber.Trim();
            }
            catch (NullReferenceException ex)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"Please fill all the fields");
                return;
            }
            catch (Exception ex)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"{ex}");
                return;
            }
            Model.Id = Model.Id.Trim().ToLower();
            Model.AdminUser.UserName = Model.AdminUser.UserName.Trim().ToLower();
            Model.OrganizationInfo.GSTNumber = GSTNumber;
            if(countryType == TenantCountryType.India)
            {
                Model.OrganizationInfo.ShouldHideSubscription = false;
            }
            else
            {
                Model.OrganizationInfo.ShouldHideSubscription = true;
            }
            
            if (Model.AdminEmail == Model.AdminUser.Email)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"Company email & AdminEmail cannot be same");
                return;
            }
            if (await TenantService.GetByIdAsync(Model.Id) == null)
            {
                try
                {
                     if (Lrb.Admin.Data.Enums.BillingType.Quarterly == SubscriptionsModel.BillingType)
                    {
                        Model.ValidUpto = DateTime.UtcNow.AddMonths(3).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Halfyearly == SubscriptionsModel.BillingType)
                    {
                        Model.ValidUpto = DateTime.UtcNow.AddMonths(6).AddDays(-1);
                    }
                    else if (Lrb.Admin.Data.Enums.BillingType.Yearly == SubscriptionsModel.BillingType)
                    {
                        Model.ValidUpto = DateTime.UtcNow.AddYears(1).AddDays(-1);
                    }
                    else
                    {
                        Model.ValidUpto = DateTime.UtcNow.AddMonths(1);
                    }

                    try
                    {
                        isCreated = await TenantService.CreateTenantAsync(Model);
                    }

                    catch (Exception ex)
                    {
                        await _jsRuntime.InvokeVoidAsync("console.log", "DNS or Tenant Not Created");
                    }
                    if(!string.IsNullOrWhiteSpace(Model.ConnectionString) && countryType != TenantCountryType.India)
                    {
                        try
                        {
                            bool IsTenantInfoCreated = await TenantService.CompareTenantIdAsync(Model.Id.Trim());
                            if (!IsTenantInfoCreated)
                            {
                                var isDubaiTenantCreated = TenantService.CreateDifferentDatabseTenantAsync(Model.Id, Model.ConnectionString ?? string.Empty);
                            }
                        }
                        catch (Exception ex)
                        {
                            await _jsRuntime.InvokeVoidAsync("console.log", "Dubai TenantInfo not created");
                        }
                    }
                    

                    if (isCreated == true)
                    {
                        await _jsRuntime.InvokeVoidAsync("console.log", "DNS & Tenant Created");
                    }
                    await _jsRuntime.InvokeVoidAsync("console.log", $"Tenant {Model.Name} Successfully Created");

                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    if (userActivityHistory == null)
                    {
                        Token = await LocalStorage.GetItemAsync<string>("token");
                        userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                        AdminUserActivity activityDto = new()
                        {
                            ActivityTime = DateTime.UtcNow,
                            Activity = ActivityConst.Logout,
                            AdminUserId = userActivityHistory.AdminUserId,
                            TenantId = Model.Id,
                            Token = Token
                        };
                        await ActivityService.UserActivityAsync(activityDto);
                        await LocalStorage.RemoveItemAsync("token");
                        Navigation.NavigateTo("");
                        await Auth.GetAuthenticationStateAsync();
                    }
                    AdminUserActivity userActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.AddTenant + " " + Model.Name,
                        TenantId = Model.Id,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(userActivity);

                    #region TenantsInfo
                    var userDetails = await UserService.GetAdminUserByIdAsync(userActivityHistory.Id);
                    LincenseHistory lincenseHistory = new()
                    {
                        EditSource = "Onboarding Tenant",
                        LincenseData = Subscription.SoldLicenses,
                        LastModifiedBy = $"{userDetails.FirstName} + {userDetails.LastName}",
                        LastModifiedOn = DateTime.UtcNow
                    };
                    tenantsInfo.CreatedOn = DateTime.UtcNow;
                    tenantsInfo.IsDormented = false;
                    tenantsInfo.Id = Model.Id;
                    tenantsInfo.GSTNumber = GSTNumber;
                    tenantsInfo.RegisteredName = Model.Name;
                    tenantsInfo.RegisteredAddress = (
                        Model?.OrganizationInfo?.Address?.SubLocality +
                        Model?.OrganizationInfo?.Address?.City +
                        Model?.OrganizationInfo?.Address?.State +
                        Model?.OrganizationInfo?.Address?.Country +
                        Model?.OrganizationInfo?.Address?.PostalCode);
                    tenantsInfo.LicenseHistory?.Add(lincenseHistory);
                    if (isCreated == true)
                    {
                        tenantsInfo.IsDomainEnabled = true;
                    }
                    else
                    {
                        tenantsInfo.IsDomainEnabled = false;
                    }


                    if (Vendors.Contains("Testing Account"))
                    {
                        await TenantService.CreateTenantInfoAsync(tenantsInfo, true);
                    }
                    else
                    {
                        await TenantService.CreateTenantInfoAsync(tenantsInfo, false);
                    }
                    #endregion

                    var role = await RoleService.GetAdminRole(Model.Id, Model.ConnectionString);
                    if (role != null)
                    {
                        Subscription.Id = Guid.NewGuid();
                        Subscription.Name = Model?.Name;
                        Subscription.TenantName = Model?.Name;
                        Subscription.CreatedOn = DateTime.UtcNow;
                        Subscription.TenantId = Model.Id;
                        Subscription.NetAmount = SubscriptionsModel.SoldPrice;
                        Subscription.SoldLicenses = SubscriptionsModel.SoldLicences;
                        Subscription.IsActive = true;
                        if (SubscriptionsModel.Type == PaymentType.Full)
                        {
                            Subscription.PaidAmount = SubscriptionsModel.TotalAmount;
                            Subscription.DueAmount = 0;
                            Subscription.DueDate = null;
                        }
                        else
                        {
                            Subscription.PaidAmount = SubscriptionsModel.PaidTotalAmount;
                            Subscription.DueAmount = SubscriptionsModel.PendingAmount;
                            Subscription.DueDate = SubscriptionsModel.NextDueDate;
                        }
                        Subscription.TotalAmount = SubscriptionsModel.TotalAmount;
                        Subscription.PaymentDate = SubscriptionsModel.PaymentDate;
                        Subscription.GSTAmount = SubscriptionsModel.GstAmount;
                        Subscription.BillingType = SubscriptionsModel.BillingType;
                        Subscription.GSTNumber = GSTNumber?.ToUpper();
                        Subscription.LicenseValidity = (DateTime)Model.ValidUpto;

                        var response = await SubscriptionService.CreateNewSubscriptionAsync(Subscription);
                        if (response != null)
                        {
                            LRBPayments payments = new LRBPayments();
                            await _jsRuntime.InvokeVoidAsync("console.log", $"Successfully created Subscription for {Model.Name}");

                            if (SubscriptionsModel.Type == PaymentType.Full)
                            {
                                payments = new()
                                {
                                    Id = Guid.NewGuid(),
                                    TenantId = Model.Id,
                                    Type = SubscriptionsModel.Type,
                                    //PartPaid = 1,
                                    //PartNumber = SubscriptionsModel.NoOfParts,
                                    NetAmount = SubscriptionsModel.SoldPrice,
                                    GSTAmount = SubscriptionsModel.GstAmount,
                                    TotalAmount = SubscriptionsModel.TotalAmount,
                                    //PaidAmount = SubscriptionsModel.TotalAmount,
                                    NextDueDate = null,
                                    PendingAmount = 0,
                                    CreatedOn = SubscriptionsModel.PaymentDate,
                                    SubscriptionId = Subscription.Id,
                                    Mode = SubscriptionsModel.PaymentMode,
                                    Description = SubscriptionsModel.Description,
                                    IsDeleted = false,
                                    InvoiceNumber = await GenerateInvoiceNumber(),
                                    PaymentMode = SubscriptionsModel.PaymentMode.ToString()
                                };
                            }
                            else
                            {
                                payments = new()
                                {
                                    Id = Guid.NewGuid(),
                                    TenantId = Model.Id,
                                    Type = SubscriptionsModel.Type,
                                    //PartPaid = 1,
                                    //PartNumber = SubscriptionsModel.NoOfParts,
                                    NetAmount = SubscriptionsModel.PaidNetAmount,
                                    GSTAmount = SubscriptionsModel.PaidGSTAmount,
                                    TotalAmount = SubscriptionsModel.PaidTotalAmount,
                                    NextDueDate = SubscriptionsModel.NextDueDate,
                                    PendingAmount = SubscriptionsModel.PendingAmount,
                                    //PaidAmount = SubscriptionsModel.PaidTotalAmount,
                                    CreatedOn = SubscriptionsModel.PaymentDate,
                                    SubscriptionId = Subscription.Id,
                                    Mode = SubscriptionsModel.PaymentMode,
                                    Description = SubscriptionsModel.Description,
                                    IsDeleted = false,
                                    InvoiceNumber = await GenerateInvoiceNumber(),
                                    PaymentMode = SubscriptionsModel.PaymentMode.ToString()
                                };
                            }

                            var added = await PaymentService.CreatePaymentAsync(payments);

                            if (added != null)
                            {
                                string? TDSUrl = null;
                                if (IsTDSApplicable == "Applicable" && fileData.Count > 0)
                                {
                                    string fileName = $"{Model.Id}_{SubscriptionsModel.PaymentDate.ToString("ddMMyyHHmm")}";
                                    var tdsresponse = await TenantService.UploadTDSCertificate(fileData, fileName);
                                    TDSUrl = tdsresponse;
                                }
                                CreateInvoice createInvoice = new()
                                {
                                    Id = new Guid(),
                                    TenantId = payments.TenantId,
                                    CreatedOn = SubscriptionsModel.PaymentDate,
                                    PaymentId = payments.Id,
                                    InvoiceNo = payments.InvoiceNumber,
                                    TDSPercent = TDSNumber ?? null,
                                    //SubscriptionType = "Subscription",
                                    TDSCertificateURL = TDSUrl

                                };
                                await PaymentService.CreateInvoiceAsync(createInvoice);
                                await _jsRuntime.InvokeVoidAsync("console.log", $"Successfully Created Payment Details for {Model.Name}");
                                AdminUserActivity subsActivity = new()
                                {
                                    AdminUserId = userActivityHistory.AdminUserId,
                                    TenantId = Model.Id,
                                    ActivityTime = DateTime.UtcNow,
                                    Activity = ActivityConst.AddedSubscription + " " + Model.Name,
                                    Token = Token
                                };
                                await ActivityService.UserActivityAsync(subsActivity);


                            }
                        }
                        else
                        {
                            await _jsRuntime.InvokeVoidAsync("alert", $"Subscription not created ... Something went wrong");
                        }
                        var adminUser = Model.AdminUser;
                        adminUser.Password = DefaultPassword;
                        adminUser.ConfirmPassword = DefaultPassword;
                        adminUser.TenantId = Model.Id;
                        adminUser.UserRoles = new List<UserRoleModel>()
                        {
                            new()
                            {
                                Description = role.Description,
                                RoleId = role.Id,
                                RoleName = role.Name,
                                Enabled = true,
                            }
                        };
                        var isUserCreated = await UserService.CreateUserAsync(adminUser);
                        await _jsRuntime.InvokeVoidAsync("alert", $" Tenant Onboarded SuccessFully \n || UserName : {Model.AdminUser.UserName} / Password : {DefaultPassword} ||");
                    }
                }
                catch (Exception ex)
                {
                    await _jsRuntime.InvokeVoidAsync("console.log", $"{ex.Message}");
                }
                if (Vendors.Count() != 0)
                {
                    if (Vendors.Contains("Testing Account"))
                    {
                        await TenantService.UpdateTestingAccountAsync(Model.Id, true);
                        Vendors.Remove("Testing Account");
                    }
                    var count = await VendorService.UpdateVendorListAsync(Vendors, Model.Id);
                    if (count > 0)
                    {
                        await _jsRuntime.InvokeVoidAsync("console.log", "Vendors Updated");

                    }
                    else
                    {
                        await _jsRuntime.InvokeVoidAsync("console.log", "Vendors update failed");
                    }
                    await UpdateAndDownloadPdf(Model.OrganizationInfo.Address, Model.Name);
                }
                if(countryType == Lrb.Admin.Data.Enums.TenantCountryType.Dubai)
                {
                    try 
                    {
                        var result = TenantService.PostCustomListingSourcesasync(Model.Id, Model.ConnectionString);
                    }
                    catch(Exception ex)
                    {
                        Console.WriteLine("Custom Listing Source Exception :"+ex);
                    }
                }
                try
                {
                    var result = await TenantService.UpdateTenantCache(Model.Id);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Updating Connect.leadrat.com Cheching :" + ex.Message);
                }
            }
            else
            {
                await _jsRuntime.InvokeVoidAsync("alert", "Tenant Id already exists.. Choose new one");
            }
            await NavigateToComponent(Model.Id);
        }
        private async Task NavigateToComponent(string tenantId)
        {
            await _jsRuntime.InvokeVoidAsync("open", $"/tenant/{tenantId}", "_blank");
        }

        public async Task<string> GenerateInvoiceNumber()
        {
            if (DateTime.UtcNow.Month >= 4)
            {
                return "DSL/" + DateTime.UtcNow.Year % 100 + "-" + ((DateTime.UtcNow.Year % 100) + 1) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
            else
            {
                return "DSL/" + ((DateTime.UtcNow.Year % 100) - 1) + "-" + (DateTime.UtcNow.Year % 100) + "/"
                    + (await SubscriptionService.GetLatestInvoiceNumberAsync() + 1).ToString("D3");
            }
        }

        public async Task Reset()
        {
            //var isConfirmed = await _jsRuntime.InvokeAsync<bool>("confirm", $"Are you sure?");
            var isConfirmed = await dailogService.Confirm("Are you sure?", "Reset Data Confirmation", new ConfirmOptions() { OkButtonText = "Yes", CancelButtonText = "No", }) ?? false;
            if (isConfirmed)
            {
                Model = new();
                SubscriptionsModel = new();
            }
        }
        public async Task CheckPartPayment()
        {
            PartPayment = true;
        }


        public void AddMultipleVendors(ChangeEventArgs evnt)
        {
            if (Vendors.Contains("Select"))
            {
                Vendors.Remove("Select");
            }
            if (evnt.Value.ToString() != "Select")
            {
                if (!Vendors.Contains(evnt.Value))
                {
                    var data = evnt.Value.ToString();
                    Vendors.Add(data);

                }
            }
        }

        public object RemoveVendor(string vendor)
        {
            Vendors.Remove(vendor);
            return "done";
        }

        public async Task UpdateAndDownloadPdf(AddressModel addressData, string companyName = "default")
        {
            try
            {
                string address = "default";
                address = $"{addressData.SubLocality} " + $"{addressData.City} " + $"{addressData.State} " + $"- {addressData.PostalCode}";
                string onboardingDate = DateTime.Today.ToString("dd/MM/yyyy");
                string inputFile = string.Equals(addressData.City, "Dubai", StringComparison.OrdinalIgnoreCase)
                                    ? "https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/LeadratDubaiMou.pdf"
                                    : "https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/LeadratGenericMou.pdf";

                string outputFile = "updated_document.pdf";

                using (PdfReader reader = new PdfReader(inputFile))
                {
                    using (PdfWriter writer = new PdfWriter(outputFile))
                    {
                        using (PdfDocument pdfDoc = new PdfDocument(reader, writer))
                        {
                            PdfAcroForm form = PdfAcroForm.GetAcroForm(pdfDoc, true);
                            PdfFont boldFont = PdfFontFactory.CreateFont(StandardFonts.HELVETICA_BOLD);

                            PdfFormField companyNameField = form.GetField("CompanyName");
                            companyNameField.SetValue(companyName).SetReadOnly(true);
                            companyNameField.SetFont(boldFont);


                            PdfFormField onboardingDateField = form.GetField("OnboardingDate");
                            onboardingDateField.SetValue(onboardingDate).SetReadOnly(true);
                            onboardingDateField.SetFont(boldFont);


                            PdfFormField addressField = form.GetField("Address").SetReadOnly(true);
                            addressField.SetValue(address);
                            addressField.SetFont(boldFont);

                        }
                    }
                }
                await DownloadUpdatedPdfAsync(outputFile);
            }
            catch (Exception ex)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        private async Task DownloadUpdatedPdfAsync(string updatedPdfPath)
        {
            try
            {
                byte[] data = await File.ReadAllBytesAsync(updatedPdfPath);
                string fileName = $"{Model.Name.ToUpper().Replace(" ", "")}_Leadrat.pdf";
                string contentType = "application/pdf";
                await _jsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            catch (Exception ex)
            {
                await _jsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        private static string? ValidateContactNumbers(string? conactnum)
        {

            string defaultRegion = null;
            //string countrycode = globalsettings?.Countries?.FirstOrDefault()?.DefaultCallingCode;

            string mobileNumber = Regex.Replace(conactnum, "[^0-9]", "");
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            if (conactnum.StartsWith("0") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                mobileNumber = "+91" + mobileNumber.Substring(1);
                return mobileNumber;
            }
            if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                PhoneNumber number = phoneUtil.Parse("+" + mobileNumber, null);
                defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
            }
            //if (string.IsNullOrWhiteSpace(defaultRegion))
            //{
            //    List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countrycodes, new List<string>());
            //    defaultRegion = regionCodes.FirstOrDefault();
            //    if (string.IsNullOrWhiteSpace(defaultRegion))
            //    {
            //        defaultRegion = globalsettings.Countries?.FirstOrDefault()?.Code;
            //        countrycode = globalsettings.Countries?.FirstOrDefault()?.DefaultCallingCode;
            //    }
            //}

            PhoneNumber phoneNumber = phoneUtil.Parse(mobileNumber, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
            if (defaultRegion == "AE")
            {
                if (contactWithCountryCode.Length == 12)
                {
                    return contactWithCountryCode;
                }
            }

            bool isValid;
            if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
            {
                return contactWithCountryCode;
            }
            else
            {
                return "false";
            }
            //else if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            //{
            //    return "+" + mobileNumber;
            //}
            //else if (string.IsNullOrWhiteSpace(defaultRegion) && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            //{
            //    return mobileNumber;
            //}
            //else if (mobileNumber.Length > 6 && mobileNumber.Length < 20)
            //{
            //    return defaultRegion + mobileNumber;
            //}
            //else
            //{
            //    return string.Empty;
            //}
        }


        #region FileUpload

        private async Task TDSFileUpload(InputFileChangeEventArgs files)
        {
            foreach (var file in files.GetMultipleFiles())
            {
                var data = await PdfUploadHelper.PdfToByteArray(file);
                if (data != null)
                {
                    var base64Data = Convert.ToBase64String(data);
                    fileData.Add(base64Data);
                }
            }
        }
        #endregion
    }

}
