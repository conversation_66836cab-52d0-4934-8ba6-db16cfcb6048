﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;

namespace Lrb.Admin.UI.Pages.Vendor
{
    public partial class EditVendor
    {
        [Inject]
        public IVendorService VendorService { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        public VendorModel Model { get; set; } = new();
        [Parameter]
        public Guid Id { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        public string? Token { get; set; }
        public async Task EditVendorAsync()
        {
            var response = await VendorService.UpdateVendorAsync(Id, Model);
            if(response != null) 
            {
                await JsRuntime.InvokeVoidAsync("alert", "Updated Successfully");
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity adminUser = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId= userActivityHistory.TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.UpdateVendor + Model.FirstName + Model.LastName,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(adminUser);
            }
            else
            {
                await JsRuntime.InvokeVoidAsync("alert", "Something went Wrong");
            }
        }
    }
}
