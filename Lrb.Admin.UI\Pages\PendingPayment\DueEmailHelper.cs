﻿namespace Lrb.Admin.UI.Pages.PendingPayment
{
    public class Due<PERSON>mailHelper
    {
        public static string ReplaceVariables(string text, Dictionary<string, string> variableSet)
        {
            if (variableSet == null || !variableSet.Any() || string.IsNullOrWhiteSpace(text))
            {
                return text;
            }
            foreach (var variable in variableSet)
            {
                text = text.Replace(variable.Key, String.Format(variable.Value).Replace(" ", "%20") ?? string.Empty);
            }
            return text;
        }
    }

    public static class LeadratEmails
    {
        public const string NoReplyEmail = "<EMAIL>";
        public const string SupportEmail = "<EMAIL>";
        public const string StudioEmail = "<EMAIL>";
        public const string CSTEmail = "<EMAIL>";
    }
}