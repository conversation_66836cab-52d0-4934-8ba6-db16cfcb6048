﻿using Lrb.Admin.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Models.Tenant
{
    public class TenantViewModel
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public DateTime DateOfOnBoard { get; set; }
        public DateTime LicenseValidity { get; set; }
        public string? City { get; set; }
        public string? Vendors { get; set; }
        public int NoOfUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InActiveUser { get; set; }
        public int UserLimit { get; set; }
        public int PropertyCount { get; set; }
        public int AssignedLeads { get; set; }
        public int UnassignedLeads { get; set; }
        public int SoldLicenses { get; set; }
        public string? GSTNumber { get; set; }
        public double NetAmount { get; set; }
        public double GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public BillingType BillingType { get; set; }
    }

    public class TenantViewModelExport 
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? DateOfOnBoard { get; set; }
        public string? LicenseValidity { get; set; }
        public string? Vendors { get; set; }
        public int SoldLicenses { get; set; }
        public int AddonSoldLicenses { get; set; }
        public string? GSTNumber { get; set; }
        public double NetAmount { get; set; }
        public double GSTAmount { get; set; }
        public double TotalAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public double AddonTotalAmount { get; set; }
        public string? AddonDueAmount { get; set; }
        public string? AddOnDueDate { get; set; }
        public BillingType BillingType { get; set; }
    }
}
