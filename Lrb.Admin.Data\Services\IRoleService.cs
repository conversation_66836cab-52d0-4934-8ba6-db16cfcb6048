﻿using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Models.Roles;

namespace Lrb.Admin.Data.Services
{
    public interface IRoleService
    {
        Task<ApplicationRole> GetAdminRole(string tenantId,string? ConnectionString=null);
        Task<ApplicationRole> GetBasicRoleAsync(string tenantId, string? ConnectionString = null);
        Task<ApplicationRole> GetManagerRoleAsync(string tenantId, string? ConnectionString = null);
        Task<ApplicationRole> GetHRRoleAsync(string tenantId, string? ConnectionString = null);
        Task<ApplicationRole> GetSalesExecutiveRoleAsync(string tenantId, string? ConnectionString = null);


    }
}
