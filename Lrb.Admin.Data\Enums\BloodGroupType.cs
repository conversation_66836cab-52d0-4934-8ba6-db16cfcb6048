﻿using System.ComponentModel;

namespace Lrb.Admin.Data.Enums
{
    public enum BloodGroupType
    {
        [Description("None")]
        None = 0,
        [Description("A+")]
        APositive,
        [Description("A-")]
        ANegative,
        [Description("B+")]
        BPositive,
        [Description("B-")]
        BNegative,
        [Description("O+")]
        OPositive,
        [Description("O-")]
        ONegative,
        [Description("AB+")]
        ABPositive,
        [Description("AB-")]
        ABNegative,
    }
}
