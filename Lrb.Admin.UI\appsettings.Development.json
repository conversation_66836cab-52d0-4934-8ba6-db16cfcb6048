{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;", "ReadReplicaConnectionString": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;", "DubaiConnectionString": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;", "DubaiReadReplicaConnectionString": "Host=dev-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=************`H9hTJK5ojM)C=$C6w,0;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;"}, "LrbApiEndPoints": {"IdentityApi": "https://lrb-dev-identity.leadratd.com/", "WebApi": "https://lrb-dev-web.leadratd.com/", "MobileApi": "https://lrb-dev-mobile.leadratd.com/"}, "LrbDomains": {"DomainName": "leadratd.com", "DomainValue": "d390pujtbxatxx.cloudfront.net.", "Type": "CNAME", "Ttl": 3600}, "GoDaddySettings": {"ApiKey": "fYWDoVBV87hz_G7dYJXpr3CnA8Au1MUs1Mh", "Secret": "WiAjFjCnbJjGwbgXAXcdQT", "BaseUrl": "https://api.godaddy.com/v1/"}, "LrbS3Bucket": {"S3BucketUrl": "https://dleadrat-black.s3.ap-south-1.amazonaws.com/"}, "PHP_PaymentApiKey": {"API-Key": "TGVhZHJhdCBCbGFjayDgpLjgpYcg4KSt4KWB4KSX4KSk4KS+4KSoIOCkteCkv+CkteCksOCkoyDgpKrgpY3gpLDgpL7gpKrgpY3gpKQg4KSV4KSw4KWHIA=="}}