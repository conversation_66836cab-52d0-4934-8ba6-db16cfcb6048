﻿/*@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
@import url('c:\users\<USER>\source\repos\leadrat-black-admin\lrb.admin.ui\shared\mainlayout.razor.css');
@import url('analytics.css');*/


/*     UI For Mobile Devices     */
/*@media (min-width:320px) and (max-width: 476px)
{
    * {
        font-family: Arial, Helvetica, sans-serif;
    }
    .analytics {
        border: 2px solid black;
        width:100%;
    }

    .analytics-filter {
        padding: 1% 1%;
        margin-bottom: 1%;
        width:100%;
        display:flex;
        flex-wrap:wrap;
    }
    .analytics-filter-left{
        width:60%;
    }

    .analytics-count {
        padding-left: 10%;
        padding-top: 30%;
        width:30%
    }

    .analytics-filters-right {
        display: inline-grid;
        padding: 1% 0;
        display:flex;
    }

    .analytics-button {
        display:flex;
        padding:1% 1%;
        flex-wrap:wrap;
    }


    .analytics-properties {
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .pages-filter {
    }

    .table-paging {
        border: 2px solid black;
        margin-right: 2%;
        margin-top: 1%;
    }

}*/

/*----------------------------------------------------------------------------------------
     UI For IPAD  Devices */    

/*@media (min-width:477px) and (max-width:768px)
{
    * {
        font-family: Arial, Helvetica, sans-serif;
    }

    .analytics {
        border: 2px solid black;
        padding: 1% 1%;
        margin-bottom: 1%
    }

    .analytics-filter {
        display: flex;
    }
    .analytics-filter-left{
        width:50%;
    }

    .analytics-count {
        padding-top: 4%;
        width: 30%
    }

    .analytics-filters-right {
        padding: 1% 0;
    }

    .analytics-button {
        padding-top: 1%;
    }


    .analytics-properties {
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .pages-filter {
    }

    .table-paging {
        border: 2px solid black;
        margin-right: 2%;
        margin-top: 1%;
    }
}*/


/*----------------------------------------------------------------------------------------*/
/*     UI For Laptop     */

/*@media (min-width:769px)
{
    * {
        font-family: Arial, Helvetica, sans-serif;
    }

    .analytics {
        border: 2px solid black;
        padding: 1% 1%;
        margin-bottom: 1%
    }
    .analytics-filter {
        display: flex;
    }

    .analytics-count {
        padding-left: 5%;
        padding-top: 4%;
        width: 30%
    }

    .analytics-filters-right {
        display: inline-grid;
        padding: 1% 0;
    }

    .analytics-button {
        padding-top: 1%;
    }


    .analytics-properties {
        overflow: scroll;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        font-size: medium;
    }

    .table .table-bordered {
        border-collapse: collapse;
        width: 100%;
        position: relative;
    }

    td {
        padding: 10px;
    }

    tr:nth-of-type(odd) {
        background: #eee;
    }

    .table-bordered > :not(caption) > * {
        border-width: 2px 0;
        border: 2px solid gray;
    }

    .pages-filter {
    }

    .table-paging {
        border: 2px solid black;
        margin-right: 2%;
        margin-top: 1%;
    }
}*/
