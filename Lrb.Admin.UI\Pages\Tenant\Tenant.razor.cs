﻿using Blazored.LocalStorage;
using Blazored.Modal;
using Blazored.Modal.Services;
using DocumentFormat.OpenXml.Spreadsheet;
using iText.Forms;
using iText.Forms.Fields;
using iText.IO.Font.Constants;
using iText.Kernel.Font;
using iText.Kernel.Pdf;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Entities;
using Lrb.Admin.Data.Enums;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Models.Notification;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Vendor;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Lrb.Admin.Data.Settings;
using Lrb.Admin.UI.Modal;
using Lrb.Admin.UI.Modal.Notification;
using Lrb.Admin.UI.Modal.Subscription;
using Lrb.Admin.UI.Modal.SubStatus;
using Lrb.Admin.UI.Modal.TenantProfile;
using Lrb.Admin.UI.Modal.User;
using Lrb.Admin.UI.Pages.SubscriptionPage;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Options;
using Microsoft.JSInterop;
using SqlKata.Execution;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.Tenant
{
    [Authorize(Roles = "Admin, Manager")]
    public partial class Tenant
    {
        [Inject]
        public ITenantService TenantServices { get; set; }
        [Inject]
        public IAuthService AuthService { get; set; }
        [Inject]
        public NavigationManager Navigation { get; set; }
        [Inject]
        public IOptions<LrbS3Bucket> BucketUrl { get; set; }
        [Inject]
        public IJSRuntime JsRuntime { get; set; }
        [Inject]
        public IVendorService VendorService { get; set; }
        [Parameter]
        public string? TenantId { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Inject]
        public IActivityService ActivityService { get; set; }
        [Inject]
        public IGlobalService GlobalService { get; set; }
        [Inject]
        public INotificationService Notification { get; set; }
        [Inject]
        public IModalService Modal { get; set; }
        [Inject]
        public ICustomSubStatusService CustomSubStatus { get; set; }
        [Parameter]
        public string? Id { get; set; }
        [Parameter]
        public string? UserName { get; set; }
        [Inject]
        public SpinnerService Spinner { get; set; }
        [Inject]
        public ISubscriptionService SubscriptionService { get; set; }
        [Inject]
        public IUserService UserService { get; set; }
        public string? Token { get; set; }
        public bool EditAccountManager { get; set; } = false;
        public bool EditUserLimit { get; set; } = false;
        public bool EditExpiryDate { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalCount { get; set; }
        public int AdminCount { get; set; }
        public double MaxPageNumber { get; set; }
        public int ShowingCount { get; set; }
        public int UserLimit { get; set; }
        public CollectionType CollectionType { get; set; }
        public GetTenantModel TenantModel { get; set; } = new();
        public List<VendorModel> VendorModel { get; set; } = new();
        public UserDetailsDto User { get; set; } = new();
        public GetTenantLeadsModel LeadsModel { get; set; } = new();
        public GetTenantIntegrationModel IntegrationModel { get; set; } = new();
        public GetAllTenantParameter Filter { get; set; } = new();
        public Address TenantAddress { get; set; } = new();
        public TenantProfile ProfileModel { get; set; } = new();
        public List<string> Vendors { get; set; } = new();
        public NotificationModelDto NotificationModel { get; set; } = new();
        public GlobalSettingViewModel GlobalSettingView { get; set; } = new();
        public ObservableCollection<GetTenantUserModel> UserCollection { get; set; } = new();
        public ObservableCollection<GetTenantLeadsModel> LeadCollection { get; set; } = new();
        public ObservableCollection<GetTenantIntegrationModel> IntegrationCollection { get; set; } = new();
        public ObservableCollection<LRBSubscription> SubscriptionCollection { get; set; } = new();
        public ObservableCollection<NotificationViewModel> NotificationCollection { get; set; } = new();
        public NotificationFilter NotificationFilter { get; set; } = new();
        public UpdateSubscriptionDto UpdateSubscription { get; set; } = new();
        string? Buttonclass { get; set; }
        public List<CustomSubStatusDto> SubStatuses { get; set; } = new();
        public List<CustomMainStatusesDTO> StatusDtos { get; set; } = new();
        public Dictionary<string, List<CustomSubStatusDto>> StatusData { get; set; } = new();
        public double SubStatusDependentLeadsCount { get; set; }
        public List<string?>? AllStatusDisplayNames { get; set; } = new();
        public List<CustomSubStatusDto> ItemList { get; set; } = new List<CustomSubStatusDto>();
        public List<LRBPayments> AllPayments { get; set; } = new();
        public bool IsSubscriptionAvailable { get; set; }
        public string? TenantWeblink { get; set; }
        public List<string> VendorNames { get; set; } = new();
        public List<string> VendorDeleteNames { get; set; } = new();
        public List<string> UserNames { get; set; } = new();
        public Dictionary<string, Dictionary<string, List<string>>> keyValuePairs { get; set; } = new();
        public bool TestingAccount { get; set; }
        public TotalSubscriptionDetails SubscriptionDetails { get; set; } = new();
        public int TotalPending { get; set; }
        public List<int> DropdownValues { get; set; } = new List<int> { 10, 25, 50, 100 };
        public UserModelDto LoginAdminUserInfo { get; set; } = new();
        public AdminUserActivity AdminUser { get; set; } = new();
        public string? UserLoginToken { get; set; }
        public ProfileDto? ProfileData { get; set; }
        public int? ExtendedDays { get; set; }
        public bool? IsExpiryExtended { get; set; }
        public string SubscriptionFilterType { get; set; } = "Active";
        public double? monthlyCostPerUser { get; set; }
        public UserFilter? userFilter { get; set; } = new();
        public UserCountDto? userCounts { get; set; } = new();
        public int leftDays = 0;
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await GetSubscriptionAsync();
                leftDays = Daysleft();
                TenantModel = await TenantServices.GetTenantByIdAsync(TenantId);
                TenantAddress = await TenantServices.GetTenantAddressAsync(TenantId);
                ProfileData = await TenantServices.GetProfileDetailsAsync(TenantId, TenantModel?.ReadReplicaConnectionString ?? string.Empty);
                ProfileModel = await TenantServices.GetTenantWebsiteAsync(TenantId);
                AdminCount = await TenantServices.GetAdminCountAsync(TenantId,TenantModel?.ReadReplicaConnectionString ?? string.Empty);
                UserLimit = await SubscriptionService.GetNoOfLicenseAsync(TenantId);
                UpdateSubscription.SoldLicenses = UserLimit;
                VendorModel = await VendorService.GetAccountManagerAsync(TenantId);
                var AnySubcriptions = await SubscriptionService.GetSubscriptionDetailsAsync(TenantId);
                IsSubscriptionAvailable = AnySubcriptions.TotalCount > 0;
                TenantWeblink = $"https://{TenantId}.leadrat.com";
                Vendors = await VendorService.GetVendorsAllNames();
                TestingAccount = await TenantServices.GetAccountStatusAsync(TenantId);
                SubscriptionDetails = await TenantServices.GetTotalSubscriptionDetailsAsync(TenantId);
                Token = await LocalStorage.GetItemAsync<string>("token");
                AdminUser = await ActivityService.GetUserByTokenAsync(Token);
                LoginAdminUserInfo = await AuthService.GetUser(AdminUser.AdminUserId);
                userCounts = await UserService.GetUserCountsAsync(TenantId, TenantModel?.ReadReplicaConnectionString ?? string.Empty);
                await ExpireDateExtended();
                await CalculatingMonthlyCost();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            MaxPageNumber = (double)TotalCount / (double)PageSize;
        }

        //private async Task NavigateToComponent()
        //{
        //    try
        //    {
        //        JsRuntime.InvokeVoidAsync("open", $"https://{TenantId}.leadrat.com", "_blank");
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine(ex);
        //    }
            
        //}
        protected void GetTrueFilter()
        {
            var tenants = Filter.GetType().GetProperties();
            foreach (var tenant in tenants)
            {
                if (tenant.GetValue(Filter) != null && tenant.GetValue(Filter).ToString() == "Select")
                {
                    tenant.SetValue(Filter, true);
                }
                Filter.PageSize = PageSize;
                Filter.PageNumber = PageNumber;
            }
        }
        public async Task GetDataAsync(CollectionType collectionType)
        {
            PageNumber = 1;
            PageSize = 10;
            switch (collectionType)
            {
                case CollectionType.User:
                    Buttonclass = "btn btn.secondary";
                    StateHasChanged();
                    await GetUserAsync();
                    break;
                case CollectionType.Integration:
                    Buttonclass = "btn btn.secondary";
                    StateHasChanged();
                    await GetIntegrationAsync();
                    break;
                case CollectionType.Subscription:
                    Buttonclass = "btn btn.secondary";
                    StateHasChanged();
                    await GetSubscriptionAsync();
                    break;
                case CollectionType.Notification:
                    Buttonclass = "btn btn.secondary";
                    StateHasChanged();
                    await GetNotificationAsync();
                    break;
                case CollectionType.Status:
                    Buttonclass = "btn btn.secondary";
                    await GetStatusAsync();
                    break;
                default:
                    break;
            }
        }

        private void ShowAllSubscriptions()
        {
            SubscriptionFilterType = "All";
        }

        private void ShowActiveSubscriptions()
        {
            SubscriptionFilterType = "Active";
        }

        private void ShowUpcomingSubscription()
        {
            SubscriptionFilterType = "Upcoming";
        }
        private void ShowExpiredSubscription()
        {
            SubscriptionFilterType = "Expired";
        }
        private void ShowDeletedSubscription()
        {
            SubscriptionFilterType = "Deleted";
        }

        private IEnumerable<LRBSubscription> GetFilteredSubscriptions()
        {
            if (SubscriptionFilterType == "Active")
            {
                ShowingCount = SubscriptionCollection.Where(s => s.IsActive && !s.IsDeleted).Count();
                return SubscriptionCollection.Where(s => s.IsActive && !s.IsDeleted);
            }
            else if (SubscriptionFilterType == "Upcoming")
            {
                ShowingCount = SubscriptionCollection.Where(s => !s.IsActive && !s.IsExpired && !s.IsDeleted).Count();
                return SubscriptionCollection.Where(s => !s.IsActive && !s.IsExpired && !s.IsDeleted);
            }
            else if(SubscriptionFilterType == "Expired")
            {
                ShowingCount = SubscriptionCollection.Where(s => !s.IsActive && s.IsExpired && !s.IsDeleted).Count();
                return SubscriptionCollection.Where(s => !s.IsActive && s.IsExpired && !s.IsDeleted);
            }
            else if(SubscriptionFilterType == "Deleted")
            {
                ShowingCount = SubscriptionCollection.Where(s => s.IsDeleted).Count();
                return SubscriptionCollection.Where(s => s.IsDeleted);
            }
            else
            {
                ShowingCount = SubscriptionCollection.Where(s => !s.IsDeleted).Count();
                return SubscriptionCollection.Where(s => !s.IsDeleted);
            }
        }
        private async Task HandleKeyDownUser(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                await GetUserAsync();
            }
        }

        private async Task HandleKeyDownNotification(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                await GetNotificationAsync();
            }
        }

        public async Task GetUserAsync()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetTenantUserModel, string> response = null;
                var fetchTask = InvokeAsync(async () =>
                {
                    userFilter.TenantId = TenantId;
                    userFilter.PageNumber = PageNumber;
                    userFilter.PageSize = PageSize;
                    response = await TenantServices.GetUserByTenantIdAsync(userFilter, TenantModel?.ReadReplicaConnectionString ?? string.Empty);
                });
                while (!fetchTask.IsCompleted)
                {
                    Spinner.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    Spinner.Hide();
                }
                if (response!= null && response.Items != null)
                {
                    UserCollection = new ObservableCollection<GetTenantUserModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    ShowingCount = UserCollection.Count;
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    MaxPageNumber = (double)TotalCount / (double)PageSize;
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity adminUserActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.TenantProfile,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(adminUserActivity);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"Something Went Wrong");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        public async Task GetLeadAsync()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetTenantLeadsModel, string> response = null;
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await TenantServices.GetTenantLeadsAsync(TenantId, Filter);
                });
                while (!fetchTask.IsCompleted)
                {
                    Spinner.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    Spinner.Hide();
                }
                if (response != null && response.Items != null)
                {
                    LeadCollection = new ObservableCollection<GetTenantLeadsModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    MaxPageNumber = (double)TotalCount / (double)PageSize;
                    ShowingCount = PageNumber * PageSize - (PageSize - LeadCollection.Count);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        public async Task GetIntegrationAsync()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<GetTenantIntegrationModel, string> response = null;
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await TenantServices.GetTenantIntegrationAsync(TenantId, Filter, TenantModel?.ReadReplicaConnectionString ?? string.Empty);
                });
                while (!fetchTask.IsCompleted)
                {
                    Spinner.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    Spinner.Hide();
                }
                if (response != null && response.Items != null)
                {
                    IntegrationCollection = new ObservableCollection<GetTenantIntegrationModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    MaxPageNumber = (double)TotalCount / (double)PageSize;
                    ShowingCount = PageNumber * PageSize - (PageSize - IntegrationCollection.Count);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }
        public async Task GetSubscriptionAsync()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<LRBSubscription, string> response = null;
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await SubscriptionService.GetSubscriptionDetailsAsync(TenantId);
                });
                while (!fetchTask.IsCompleted)
                {
                    Spinner.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    Spinner.Hide();
                }
                if (response != null && response.Items != null)
                {
                    SubscriptionCollection = new ObservableCollection<LRBSubscription>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    MaxPageNumber = (double)TotalCount / (double)PageSize;
                    ShowingCount = PageNumber * PageSize - (PageSize - SubscriptionCollection.Count);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        public void Paginate(int page)
        {
            PageNumber = page;
        }

        private async Task IncreasePageNumber(CollectionType collectionType)
        {
            if (PageNumber < (Math.Ceiling(MaxPageNumber)))
            {
                PageNumber++;
            }
            switch (collectionType)
            {
                case CollectionType.User:
                    {
                        UserCollection.Clear();
                        await GetUserAsync();
                    }
                    break;
                case CollectionType.Integration:
                    {
                        IntegrationCollection.Clear();
                        await GetIntegrationAsync();
                    }
                    break;
                default:
                    break;
            }
        }
        private async Task DecreasePageNumber(CollectionType collectionType)
        {
            if (PageNumber > 1)
            {
                PageNumber--;
            }
            switch (collectionType)
            {
                case CollectionType.User:
                    {
                        UserCollection.Clear();
                        await GetUserAsync();
                    }
                    break;
                case CollectionType.Integration:
                    {
                        IntegrationCollection.Clear();
                        await GetIntegrationAsync();
                    }
                    break;
                default:
                    break;
            }
        }
        private async Task EditProfileAsync()
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
            if (confirmed)
            {
                foreach (var v in VendorModel)
                {
                    VendorNames.Add(v.FirstName + ' ' + v.LastName);
                }
                EditAccountManager = true;
            }


        }
        private async Task EditTenantAccountManager()
        {
            if (await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?"))
            {
                var response = await VendorService.DeleteTenantIdFromVendorAsync(VendorDeleteNames, TenantId);
                var data = await VendorService.UpdateVendorListAsync(VendorNames, TenantId);
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity userActivity = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.UpdateTenantProfile + " " + TenantId,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(userActivity);
                VendorNames = new();
                EditAccountManager = false;
            }
        }
        private async Task EditLimitAsync()
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
            if (confirmed)
            {
                EditUserLimit = true;
            }
        }
        private async Task EditDate()
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are you Sure");
            {
                if (confirmed)
                {
                    EditExpiryDate = true;
                }
            }
            return;
        }
        private async Task EditExpiryDateAsync(GetTenantModel model)
        {
            UpdateTenantValidityDto updateTenantValidityDto = new UpdateTenantValidityDto()
            {
                TenantId = TenantId ?? string.Empty,
                ExtendedExpiryDate = model.LicenseValidity?.ToUniversalTime()
            };
            var response = await TenantServices.UpdateTenantValidity(TenantId ?? string.Empty, updateTenantValidityDto);
            if (response)
            {
                Token = await LocalStorage.GetItemAsync<string>("token");
                var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                AdminUserActivity userActivity = new()
                {
                    AdminUserId = userActivityHistory.AdminUserId,
                    TenantId = TenantId,
                    ActivityTime = DateTime.UtcNow,
                    Activity = ActivityConst.UpdateTenantProfile + " " + TenantId,
                    Token = Token
                };
                await ActivityService.UserActivityAsync(userActivity);
                await JsRuntime.InvokeVoidAsync("alert", "Updated Successfully");
            }
            EditExpiryDate = false;
        }

        private async Task EditTenantStatus(GetTenantModel model)
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
            if (confirmed)
            {
                var response = await TenantServices.UpdateTenantStatusAsync(model.TenantId);
                if (response != null)
                {
                    TenantModel.GharofficeTenant = response.Data;
                }
            }
        }
        private async Task DownloadExcel(string fileUrl)
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
            if (confirmed)
            {
                Navigation.NavigateTo(BucketUrl.Value.S3BucketUrl + fileUrl);
            }
        }
        public async Task ShowNotificationPopUpModal()
        {
            var parameter = new ModalParameters();
            //parameter.Add(nameof(TenantId), TenantId ?? string.Empty);
            //parameter.Add(nameof(Id), user?.Id ?? string.Empty);
            var options = new ModalOptions
            {
                Size = ModalSize.Small
            };
            var modal = Modal.Show<NotificationPopUpModal>("Notification", parameter, options);
        }

        public async Task ShowNotificationPopUpModalForUserModal(GetTenantUserModel? user)
        {
            var parameter = new ModalParameters();
            parameter.Add(nameof(TenantId), TenantId ?? string.Empty);
            parameter.Add(nameof(Id), user?.Id ?? string.Empty);
            parameter.Add(nameof(UserName), user?.UserName ?? string.Empty);
            var options = new ModalOptions
            {
                Size = ModalSize.Small
            };
            var modal = Modal.Show<NotifySingleUserModal>("Notification", parameter, options);
        }

        public async Task ShowBulkUserPopUpModal()
        {
            var modal = Modal.Show<AddBulkUser>("Add Bulk User");
        }
        public async Task ShowGlobolSettingPopUpModal()
        {
            var parameter = new ModalParameters();
            parameter.Add(nameof(GlobalSettingPopUpModal.TenantId), TenantId);
            parameter.Add(nameof(GlobalSettingPopUpModal.ConnectionString), TenantModel.ConnectionString ?? string.Empty);
            parameter.Add(nameof(GlobalSettingPopUpModal.ReadConnectionString), TenantModel.ReadReplicaConnectionString ?? string.Empty);
            var option = new ModalOptions
            {
                Size = ModalSize.Medium
            };
            var modal = Modal.Show<GlobalSettingPopUpModal>("Global Setting", parameter, option);
        }
        public async Task NavigateToSubscriptionPage()
        {
            await JsRuntime.InvokeVoidAsync("open", $"/subscription/{TenantId}", "_blank");
        }

        public async Task UpdateProfileData()
        {
            var parameter = new ModalParameters();
            parameter.Add(nameof(UpdateProfile.TenantId), TenantId);
            parameter.Add(nameof(UpdateProfile.ProfileData), ProfileData);
            parameter.Add(nameof(UpdateProfile.IsTestingAccount), TestingAccount);
            parameter.Add(nameof(UpdateProfile.Vendors), Vendors);
            parameter.Add(nameof(UpdateProfile.VendorModel), VendorModel);
            parameter.Add(nameof(UpdateProfile.ConnectionString), TenantModel.ConnectionString ?? string.Empty);
            parameter.Add(nameof(UpdateProfile.ReadConnectionString), TenantModel.ReadReplicaConnectionString ?? string.Empty);
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<UpdateProfile>("Update Tenant Profile", parameter, options);

        }
        public async Task UpdateSubscriptionDataById(Guid? subscriptionId)
        {
            var model = await SubscriptionService.GetTenantSubscriptionById(subscriptionId, false);
            var parameter = new ModalParameters();
            parameter.Add(nameof(UpdateTenantSubscription.TenantId), TenantId);
            parameter.Add(nameof(UpdateTenantSubscription.SubscriptionId), subscriptionId);
            parameter.Add(nameof(UpdateTenantSubscription.SubscriptionModel), model);
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<UpdateTenantSubscription>("Update Subscription", parameter, options);
        }

        public async Task DeleteSubscription(Guid? subscriptionId)
        {
            bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
            if (confirmed)
            {
                var response = await SubscriptionService.DeleteSubscriptionAsync(subscriptionId);
                if (response)
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"Subscription Deleted Successfully");
                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity userActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = ActivityConst.DeleteSubscription + " " + TenantId,
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(userActivity);
                    await JsRuntime.InvokeVoidAsync("location.reload");
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", "Something Went Wrong {Tenant.CS : 518}");
                }
            }
        }

        public async Task GetNotificationAsync()
        {
            try
            {
                GetTrueFilter();
                PagedResponse<NotificationViewModel, string> response = null;
                var fetchTask = InvokeAsync(async () =>
                {
                    response = await Notification.GetNotificationDetailsAsync(TenantId, NotificationFilter);
                });
                while (!fetchTask.IsCompleted)
                {
                    Spinner.Show();
                    await fetchTask;
                    fetchTask.Dispose();
                    Spinner.Hide();
                }
                if (response != null && response.Items != null)
                {
                    NotificationCollection = new ObservableCollection<NotificationViewModel>(response.Items);
                    TotalCount = (int)response.TotalCount;
                    MaxPageNumber = (double)TotalCount / (double)PageSize;
                    ShowingCount = PageNumber * PageSize - (PageSize - NotificationCollection.Count);
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"{response.Message}");
                }
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }



        private async Task AddSubscriptionAsync()
        {
            var latestSubscription = await SubscriptionService.GetAllLicencesValidityAsync(TenantId);
            //Navigation.NavigateTo($"/add-subscription/{TenantId}");
            if (latestSubscription == null)
            {
                TenantsDto tempData = new();
                latestSubscription = tempData;
                latestSubscription.ValidUpto = null;
            }
            var parameter = new ModalParameters
            {
                { nameof(AddTenantSubscription.TenantId), TenantId},
                { nameof(AddTenantSubscription.LicencesValidity), latestSubscription.ValidUpto}
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Automatic
            };
            var modal = Modal.Show<AddTenantSubscription>("Add Subscription", parameter, options);
        }

        public bool IsPaid()
        {
            if (TenantModel.PaidAmount > 0)
            {
                return true;
            }
            return false;
        }

        public async Task GetStatusAsync()
        {
            try
            {
                var statusData = await CustomSubStatus.GetAllMainStatuses(TenantId);
                StatusDtos = statusData.ToList();
                var subStatusData = await CustomSubStatus.GetCustomMasterLeadSubStatuses(TenantId);
                SubStatuses = subStatusData.ToList();
                if (StatusData.Count == 0)
                {
                    StatusDtos.ForEach(i =>
                    {
                        var allSubStatusList = new List<CustomSubStatusDto>();
                        SubStatuses.ForEach(j =>
                        {
                            if (i.Id == j.BaseId)
                            {
                                allSubStatusList.Add(j);
                            }
                        });
                        StatusData.Add(key: i.DisplayName, value: allSubStatusList);
                        AllStatusDisplayNames.Add(i.DisplayName);

                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        public async Task<double> GetSubStatusLeadCount(Guid? subStatusId)
        {
            long count = 0;
            if (subStatusId != null)
            {
                var SubStatusDependentLeadsCount = await CustomSubStatus.GetLeadCountRequest(subStatusId, TenantId);
                count = SubStatusDependentLeadsCount;
                return count;
            }
            return count;
        }

        public async Task DeleteSubStatus(Guid? subStatusId, string? mainStatusName)
        {
            SubStatusDependentLeadsCount = await CustomSubStatus.GetLeadCountRequest(subStatusId, TenantId);
            if (SubStatusDependentLeadsCount == 0)
            {
                bool confirmed = await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?");
                if (confirmed)
                {
                    if (await CustomSubStatus.DeleteMasterLeadSubStatusesById(subStatusId, null))
                    {
                        await JsRuntime.InvokeVoidAsync("alert", $"Successfully Deleted");
                        await JsRuntime.InvokeVoidAsync("location.reload");
                    }
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", $"Deletion cancelled");
                }
            }
            else
            {
                await JsRuntime.InvokeVoidAsync("alert", $"Kindly tranfer the dependent Leads");
                //await JsRuntime.InvokeVoidAsync("location.reload");

                ////code to Transfer leads of one status to other 
                //List<CustomSubStatusDto> allStatusData = new List<CustomSubStatusDto>();
                //foreach(var statusdata in StatusData)
                //{
                //    if(statusdata.Key == mainStatusName)
                //    {
                //        ItemList = statusdata.Value;
                //    }
                //}
                //ShowStringSelectionPopup(subStatusId);
            }
        }

        ///code to Transfer leads of one status to other
        public async Task ShowStringSelectionPopup(Guid? subStatusToDelete)
        {
            var parameter = new ModalParameters
            {
                { nameof(SubStatusDeletion.SubStatusToDelete), subStatusToDelete },
                { nameof(SubStatusDeletion.ItemList), ItemList }
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Medium
            };
            var modal = Modal.Show<SubStatusDeletion>("Delete Sub-Status", parameter, options);
        }

        public async Task CreateNewSubStatus(string? mainStatusDisplayName)
        {
            Guid? baseId = await CustomSubStatus.GetStatusId(TenantId, mainStatusDisplayName);

            Guid? masterSubStatusId = await CustomSubStatus.GetMasterStatusId(baseId);
            CustomSubStatusDto custom = new();
            custom.Id = Guid.NewGuid();
            custom.BaseId = baseId;
            custom.Level = 1;
            custom.OrderRank = 0;
            custom.MasterLeadStatusId = Guid.Empty;
            custom.TenantId = TenantId;
            custom.IsDeleted = false;
            custom.CreatedBy = Guid.Empty;
            custom.CreatedOn = DateTime.UtcNow;
            custom.LastModifiedBy = Guid.Empty;
            custom.LastModifiedOn = DateTime.UtcNow;
            custom.DeletedBy = Guid.Empty;
            custom.DeletedOn = null;
            custom.MasterLeadStatusBaseId = masterSubStatusId;

            var parameter = new ModalParameters
            {
                { nameof(CreateSubStatus.TenantId), TenantId},
                { nameof(SubStatusDeletion.CustomSubStatus), custom}
            };
            var options = new ModalOptions
            {
                Size = ModalSize.Small
            };
            var modal = Modal.Show<CreateSubStatus>("Add Sub-Status", parameter, options);
        }

        public bool GetUserActivity(string? tenantId, string? name)
        {
            return CustomSubStatus.GetUserAvailablity(tenantId, name).Result;
        }

        public async Task GetPaymentsOnSubscription(Guid? subscriptionId)
        {
            var allPayments = SubscriptionService.GetTenantAllPaymentAsync(subscriptionId).Result.ToList();
            allPayments = allPayments.OrderBy(i => i.CreatedBy).ToList();
            if (allPayments != null)
            {
                var parameter = new ModalParameters
                {
                    { nameof(PaymentDetails.TenantId), TenantId},
                    { nameof(PaymentDetails.IsAddOn), false},
                    { nameof(PaymentDetails.Payments), allPayments},
                    { nameof(PaymentDetails.SubscriptionId), subscriptionId }
                };
                var options = new ModalOptions
                {
                    Size = ModalSize.ExtraLarge
                };
                var model = Modal.Show<PaymentDetails>("Payment Details", parameter, options);
            }
        }

        public async Task GetAddOnSubscriptions(Guid? subscriptionId)
        {
            var allAddOSubs = SubscriptionService.GetAllAddOnSubscriptionsAsync(subscriptionId).Result.ToList();

            if (allAddOSubs != null)
            {
                var parameter = new ModalParameters
                {
                    { nameof(ViewAddOnDetails.TenantId), TenantId},
                    { nameof(ViewAddOnDetails.AddOnSubs), allAddOSubs},
                    { nameof(ViewAddOnDetails.SubscriptionId), subscriptionId }
                };
                var options = new ModalOptions
                {
                    Size = ModalSize.ExtraLarge
                };
                var model = Modal.Show<ViewAddOnDetails>("AddOn Subscription Details", parameter, options);
            }
        }

        public async Task ToggleIsActive(string userId, bool value)
        {

            if (await JsRuntime.InvokeAsync<bool>("confirm", "Are You Sure?"))
            {
                bool result = await TenantServices.ToggleActivivity(userId, value);
                if (result)
                {
                    TenantModel = await TenantServices.GetTenantByIdAsync(TenantId);
                    await GetUserAsync();
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("alert", "Could not Update activity");
                }
            }
            else
            {
                await GetUserAsync();
            }
        }

        public async Task ToggleSubscriptionIsActive(Guid SubscriptionId,DateTime SubscriptionValidity ,bool value)
        {
            int isActiveCount = SubscriptionCollection.Where(i => i.IsActive == true && i.IsDeleted == false).Count();
            if(isActiveCount > 0 && value)
            {
                await JsRuntime.InvokeVoidAsync("alert", "Tenant Already has a Active Subscription");
            }
            else
            {
                if (await JsRuntime.InvokeAsync<bool>("confirm", "This Change will Affect Subscription, Are you Sure"))
                {

                    Token = await LocalStorage.GetItemAsync<string>("token");
                    var userActivityHistory = await ActivityService.GetUserByTokenAsync(Token);
                    AdminUserActivity adminUserActivity = new()
                    {
                        AdminUserId = userActivityHistory.AdminUserId,
                        TenantId = TenantId,
                        ActivityTime = DateTime.UtcNow,
                        Activity = $"{TenantId} Subscription changed to {value}",
                        Token = Token
                    };
                    await ActivityService.UserActivityAsync(adminUserActivity);

                    bool result = await TenantServices.ToggleSubscriptionIsActiveAsync(SubscriptionId, value);
                    if (value && result)
                    {
                        UpdateTenantValidityDto updateTenantValidityDto = new UpdateTenantValidityDto()
                        {
                            TenantId = TenantId ?? string.Empty,
                            ExtendedExpiryDate = SubscriptionValidity.ToUniversalTime()
                        };
                        var response = await TenantServices.UpdateTenantValidity(TenantId ?? string.Empty, updateTenantValidityDto);
                    }
                    if (result)
                    {
                        await OnInitializedAsync();
                    }
                    else
                    {
                        await JsRuntime.InvokeVoidAsync("alert", "Could not Update Subscription");
                    }
                }
                else
                {
                    await OnInitializedAsync();
                }
            }
           
        }

        public void AddMultipleVendors(ChangeEventArgs evnt)
        {
            if (VendorNames.Contains("Select"))
            {
                VendorNames.Remove("Select");
            }
            if (evnt.Value.ToString() != "Select")
            {
                if (!VendorNames.Contains(evnt.Value))
                {
                    var data = evnt.Value.ToString();
                    VendorNames.Add(data);
                }
            }
        }

        public async void RemoveVendor(string VendorName)
        {
            VendorNames.Remove(VendorName);
            VendorDeleteNames.Add(VendorName);
        }

        public void FormatDeviceDetails()
        {

            foreach (var item in UserCollection)
            {
                List<string> AndroidList = new();
                List<string> IosList = new();
                if (UserCollection.Where(i => i.Name.Equals(item.Name)).Count() > 1)
                {
                    var multipleRecords = UserCollection.Where(i => i.Id.Equals(item.Id)).ToList();
                    foreach (var record in multipleRecords)
                    {
                        if (record.Platform == Platform.Android)
                        {
                            AndroidList.Add(record.AppVersion);
                        }
                        else
                        {
                            IosList.Add(record.AppVersion);
                        }
                    }
                }
                var temp = new Dictionary<string, List<string>>();
                temp.Add("Android", AndroidList);
                temp.Add("iOS", IosList);
                if (!keyValuePairs.ContainsKey(item.Name))
                {
                    keyValuePairs.Add(item.Name, temp);
                }
            }
        }

        public async Task SortingOnCreating()
        {
            if (Filter.CreatedOnSorting == false)
            {
                Filter.CreatedOnSorting = true;
            }
            else
            {
                Filter.CreatedOnSorting = false;
            }
            await OnInitializedAsync();
        }
        public async Task ToggleAccountToTestingAccount(ChangeEventArgs e)
        {
            if (TenantId != null)
            {
                bool newTestingAccount = Convert.ToBoolean(e.Value);

                var result = await JsRuntime.InvokeAsync<bool>(
                    "confirm",
                    $"Are you sure? You want to make \"{TenantId}\" Testing Account as {newTestingAccount}"
                );

                if (result)
                {
                    await TenantServices.UpdateTestingAccountAsync(TenantId, newTestingAccount);
                    TestingAccount = newTestingAccount;
                    await JsRuntime.InvokeVoidAsync("alert", "Successfully Done");
                }
                else
                {
                    await JsRuntime.InvokeVoidAsync("location.reload");
                }
            }
        }


        public async Task UpdateAndDownloadPdf(Address addressData, string companyName = "default")
        {
            try
            {
                string address = "default";
                address = $"{addressData.SubLocality} " + $"{addressData.City} " + $"{addressData.State} " + $"- {addressData.PostalCode}";
                string onboardingDate = DateTime.Today.ToString("dd/MM/yyyy");
                string inputFile = string.Equals(addressData.City, "Dubai", StringComparison.OrdinalIgnoreCase)
                                    ? "https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/LeadratDubaiMou.pdf"
                                    : "https://leadrat-black.s3.ap-south-1.amazonaws.com/Admin/LeadratGenericMou.pdf";
                string outputFile = "updated_document.pdf";

                using (PdfReader reader = new PdfReader(inputFile))
                {
                    using (PdfWriter writer = new PdfWriter(outputFile))
                    {
                        using (PdfDocument pdfDoc = new PdfDocument(reader, writer))
                        {
                            PdfAcroForm form = PdfAcroForm.GetAcroForm(pdfDoc, true);
                            PdfFont boldFont = PdfFontFactory.CreateFont(StandardFonts.HELVETICA_BOLD);

                            PdfFormField companyNameField = form.GetField("CompanyName");
                            companyNameField.SetValue(companyName);
                            companyNameField.SetFont(boldFont);


                            PdfFormField onboardingDateField = form.GetField("OnboardingDate");
                            onboardingDateField.SetValue(onboardingDate);
                            onboardingDateField.SetFont(boldFont);


                            PdfFormField addressField = form.GetField("Address");
                            addressField.SetValue(address);
                            addressField.SetFont(boldFont);

                        }
                    }
                }
                await DownloadUpdatedPdfAsync(outputFile);
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        private async Task DownloadUpdatedPdfAsync(string updatedPdfPath)
        {
            try
            {
                byte[] data = await File.ReadAllBytesAsync(updatedPdfPath);
                string fileName = $"{TenantModel.TenantName.ToUpper().Replace(" ", "")}_Leadrat.pdf";
                string contentType = "application/pdf";
                await JsRuntime.InvokeVoidAsync("saveAsFile", fileName, data, contentType);
            }
            catch (Exception ex)
            {
                await JsRuntime.InvokeVoidAsync("alert", $"{ex.Message}");
            }
        }

        public async Task CalculatingMonthlyCost()
        {
            if(SubscriptionCollection?.FirstOrDefault(i => i.IsActive == true && i.IsDeleted == false) != null)
            {
                var TotalAmount = SubscriptionCollection?.FirstOrDefault(i => i.IsActive == true && i.IsDeleted == false)?.TotalAmount;
                var SubscriptionLicense = SubscriptionCollection?.FirstOrDefault(i => i.IsActive == true && i.IsDeleted == false)?.SoldLicenses;
                var BillingType = SubscriptionCollection?.FirstOrDefault(i => i.IsActive == true && i.IsDeleted == false)?.BillingType;
                var PerLicenseAmount = TotalAmount / SubscriptionLicense;

                if ((int)BillingType == 1)
                {
                    monthlyCostPerUser = Math.Round((double)(PerLicenseAmount / 3), 2);
                }
                else if ((int)BillingType == 2)
                {
                    monthlyCostPerUser = Math.Round((double)(PerLicenseAmount / 6), 2);
                }
                else if ((int)BillingType == 3)
                {
                    monthlyCostPerUser = Math.Round((double)(PerLicenseAmount / 12), 2);
                }
            }
            
        }
        public int Daysleft()
        {
            int DaysLeft = 0;
            var today = DateTime.UtcNow;
            var ActiveSubscription = SubscriptionCollection?.Where(i => i.IsActive == true)?.FirstOrDefault();
            switch (ActiveSubscription?.BillingType)
            {
                case BillingType.Yearly:
                    var daysLeftInYear = (ActiveSubscription.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInYear;
                    break;
                case BillingType.Halfyearly:
                    var daysLeftInHalfYear = (ActiveSubscription.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInHalfYear;
                    break;
                case BillingType.Quarterly:
                    var daysLeftInQuarterly = (ActiveSubscription.LicenseValidity - today).Days + 1;
                    DaysLeft = daysLeftInQuarterly;
                    break;
            }
            return DaysLeft;
        }

        public async Task ExpireDateExtended()
        {
            if(SubscriptionCollection?.Where(i => i.IsActive == true)?.Count() > 0)
            {
                if (TenantModel.LicenseValidity?.ToString("dd/MM/yyyy") == SubscriptionCollection?.Where(i => i.IsActive == true)?.FirstOrDefault()?.LicenseValidity.ToString("dd/MM/yyyy"))
                {
                    IsExpiryExtended = false;
                }
                else
                {
                    var tenantLicenseValidity = TenantModel.LicenseValidity;
                    var subscriptionLicenseValidity = SubscriptionCollection?
                        .Where(i => i.IsActive == true)?
                        .FirstOrDefault()?
                        .LicenseValidity;
                    ExtendedDays = (tenantLicenseValidity.Value.Date - subscriptionLicenseValidity.Value.Date).Days;
                    if (ExtendedDays > 0)
                    {
                        IsExpiryExtended = true;
                    }
                }
            }
        } 
    }

    public enum CollectionType
    {
        Subscription,
        User,
        Integration,
        IVR,
        Notification,
        Status
    }


}