﻿using Npgsql;
using System.Data;

namespace Lrb.Admin.Data.Repos.Factory
{
    public interface IDBConnectionFactory
    {
        Task<IDbConnection> CreateConnectionAsync(string connectionString = null);
        Task CloseConnectionAsync(NpgsqlConnection dbConnection);
        Task OpenConnectionAsync(NpgsqlConnection dbConnection);
        Task<IDbConnection> CreateReadConnectionAsync(string connectionString = null);
    }
}
