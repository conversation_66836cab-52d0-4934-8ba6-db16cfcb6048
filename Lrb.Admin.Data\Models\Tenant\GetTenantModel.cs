﻿using Lrb.Admin.Data.Enums;
 
namespace Lrb.Admin.Data.Models.Tenant
{
    public class GetTenantModel
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? ConnectionString { get; set; }
        public string? ReadReplicaConnectionString { get; set; }
        public DateTime? DateOfOnBoard { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public DateTime? PaymentDate { get; set; }
        public DateTime? DueDate { get; set; }
        public string? AddOnDueDate { get; set; }
        public string? City { get; set; }
        public string? FullAddress { get; set; }
        public int UserLimit { get; set; }
        public int NoOfUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int PropertyCount { get; set; }
        public int AssignedLeads { get; set; }
        public int UnassignedLeads { get; set; }
        public double NetAmount { get; set; }
        public double PaidAmount { get; set; }
        public double DueAmount { get; set; }
        public string? AddonDueAmount { get; set; }
        public int SoldLicenses { get; set; }
        public int AddonSoldLicenses { get; set; }
        public double TotalAmount { get; set; }
        public double AddonTotalAmount { get; set; }
        public double GSTAmount { get; set; }
        public BillingType BillingType { get; set; }
        public bool GharofficeTenant { get; set; }
        public bool IntegrationEnabled { get; set; }
        public bool IVREnabled { get; set; }
        public string? ProfileImageUrl { get; set; }
        public int InActiveUser { get; set; }
        public string? Vendors { get; set; }
        public string? GSTNumber { get; set; }
    }
}