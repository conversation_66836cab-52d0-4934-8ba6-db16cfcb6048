﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public class ActivityService : IActivityService
    {
        private readonly IActivityRepository _activityRepository;
        public ActivityService(IActivityRepository activityRepository)
        {
            _activityRepository = activityRepository;
        }

        public async Task<Response<bool>> UserActivityAsync(AdminUserActivity userActivity)
        {
            var affectedRow = await _activityRepository.AddAdminUserActity(userActivity);
            return affectedRow;
        }

        public async Task<AdminUserActivity> GetUserByTokenAsync(string token)
        {
            var response  = await _activityRepository.GetByIdTokenAsync(token);
            return response;
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetAdminUserActivityAsync(ActivityFilter filter)
        {
            var response = await _activityRepository.GetUserActivity(filter.PageNumber, filter.PageSize, filter);
            return response;
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetTodayActivityAsync(ActivityFilter filter)
        {
            filter.FromDate = DateTime.Today;
            filter.ToDate = DateTime.Today.AddDays(1).AddTicks(-1);
            var resources = await _activityRepository.GetUserActivity(filter.PageNumber, filter.PageSize, filter);
            return resources;
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetYesterdayActivityAsync(ActivityFilter filter)
        {
            filter.FromDate = DateTime.Today.AddDays(-1); // Yesterday at 00:00:00
            filter.ToDate = DateTime.Today.AddTicks(-1);  // Yesterday at 23:59:59.9999999
            var resources = await _activityRepository.GetUserActivity(filter.PageNumber, filter.PageSize, filter);
            return resources;   
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetLastWeekActivityAsync(ActivityFilter filter)
        {
            var currentDayOfWeek = (int)DateTime.Today.DayOfWeek;
            var daysToSubtract = currentDayOfWeek == 0 ? 7 : currentDayOfWeek; // If today is Sunday, go back 7 days
            var lastWeekStart = DateTime.Today.AddDays(-daysToSubtract - 6);
            var lastWeekEnd = DateTime.Today.AddDays(-daysToSubtract).AddTicks(-1);

            filter.FromDate = lastWeekStart; // Last week's Monday at 00:00:00
            filter.ToDate = lastWeekEnd;     // Last week's Sunday at 23:59:59.9999999

            var resources = await _activityRepository.GetUserActivity(filter.PageNumber, filter.PageSize, filter);
            return resources;
        }

        public async Task<PagedResponse<GetActivityDto, string>> GetLastMontActivityAsync(ActivityFilter filter)
        {
            var firstDayOfThisMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            var lastMonthStart = firstDayOfThisMonth.AddMonths(-1);
            var lastMonthEnd = firstDayOfThisMonth.AddTicks(-1);

            filter.FromDate = lastMonthStart; // 1st day of last month at 00:00:00
            filter.ToDate = lastMonthEnd;     // Last day of last month at 23:59:59.9999999

            var resources = await _activityRepository.GetUserActivity(filter.PageNumber, filter.PageSize, filter);
            return resources;
        }
    }
}
