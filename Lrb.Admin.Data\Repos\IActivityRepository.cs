﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Wrappers;

namespace Lrb.Admin.Data.Repos
{
    public interface IActivityRepository
    {
        Task<Response<bool>> AddAdminUserActity(AdminUserActivity activity);
        Task<Response<bool>> UpdateAdminActivity(AdminUserActivity activity);
        Task<AdminUserActivity> GetByIdTokenAsync(string idToken);
        Task<PagedResponse<GetActivityDto, string>> GetUserActivity(int pageNumber, int pageSize, ActivityFilter filter);
        Task<int> GetActivityCount();
    }
}
