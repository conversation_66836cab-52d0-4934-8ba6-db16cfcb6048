window.DeleteSubStatus = (listItems) =>  {
    // Create a container for the buttons
    var container = document.createElement("div");
    container.style.display = "flex";
    container.style.flexDirection = "column";
    container.style.alignItems = "center";

    // Create buttons for each list item
    listItems.forEach(function (item) {
        var button = document.createElement("button");
        button.textContent = item.DisplayName; // Assuming the DTO has a 'displayName' property
        button.style.margin = "5px";
        button.addEventListener("click", function () {
            // Close the pop-up and return the selected item
            closePopup(item);
        });
        container.appendChild(button);
    });

    // Create a submit button
    var submitButton = document.createElement("button");
    submitButton.textContent = "Submit";
    submitButton.style.margin = "10px";
    submitButton.addEventListener("click", function () {
        // Close the pop-up without selecting an item
        closePopup(null);
    });
    container.appendChild(submitButton);

    // Create the pop-up window
    var popup = window.open("", "_blank", "width=400,height=300,scrollbars=yes,resizable=yes");
    popup.document.body.appendChild(container);

    // Function to close the pop-up and return the selected item
    function closePopup(selectedItem) {
        popup.close();
        // Invoke the JS function with the selected item
        DotNet.invokeMethodAsync("YourAssemblyName", "OnItemSelected", selectedItem);
    }
    
    window.showListPopup = function (listItems)
    {
            DotNet.invokeMethodAsync("YourAssemblyName", "OnListPopup", listItems);
    };
 }


