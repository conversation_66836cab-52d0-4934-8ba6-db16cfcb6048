﻿
@media (min-width:794px)
{
    input {
        background-color: transparent;
        border: 0px solid;
        height: 20px;
        width: 160px;
        color: #CCC;
    }

    .invoice-design {
        display: flex;
        border: 1px solid black;
        margin: auto;
        width: 100%;
    }
    .billing-details {
        display: inline-grid;
       border-right: 1px solid black;
        width:61%;
    }
    .billing-details-right{
        width:39%;
        margin-bottom:10%;
        display:inline-grid;
    }
    .billing-from {
        border-bottom: 1px solid black;
        display: inline-grid;
        padding-left: 1%;
    }
    .billing-to {
        display: inline-grid;
        padding-left: 1%;
    }
    .gst-in {
        display: flex;
    }
    .invoice-logo {
        border-bottom: 0px solid black;
        display: flex;
        justify-content: center;
        background-color: #50bea7;
        padding:20px;
        width:100%;
    }
    .invoice-no {
        border-bottom: 1px solid black;
        padding: 2px;
    }
    .invoice-date {
        border-bottom: 1px solid black;
        padding:2px
    }
    .amount-due {
        border-bottom: 1px solid black;
        padding: 2px
    }
    .due-date {
        border-bottom: 1px solid black;
        padding: 2px
    }
    .license-date {
        padding: 2px;
        border-bottom: 1px solid black;
    }
    .tenant-subs {
        display: flex;
        border-left: 1px solid black;
        border-right: 1px solid black;
        width: 100%;
    }
    .subs-details{
        display:flex;
        width:100%;
        height:140px;
    }
    .description {
        display:inline-grid;
        border-right:1px solid black;
        align-self:center;
        height:100%;
        width:22%;
    }
    .payment-details {
        display: inline-grid;
        border-right: 1px solid black;
        width: 100%;
        align-self: center;
        height: 140px;
    }
    .hsn-no {
        display: inline-grid;
        border-right: 1px solid black;
        width: 15%;
    }
    .license-details {
        display: inline-grid;
        border-right: 1px solid black;
        width: 13%;
        align-self: center;
        height: 140px;
    }
    .license-validity {
        display: inline-grid;
        border-right: 1px solid black;
        width: 13%;
        align-self: center;
        height: 140px;
    }
    .subs-price {
        display: inline-grid;
        width: 13%;
        align-self: center;
        height: 140px;
    }
    .payment-date {
        display: inline-grid;
        border-right: 1px solid black;
        width: 13%;
        align-self: center;
        height: 140px;
        
    }
    .payment-details {
        display: flex;
        border: 1px solid black;
        width: 100%;
        height:144px;
    }
    .banking-details {
        display: inline-grid;
        padding-left: 8px;
        width: 60.9%;
        height:144px;
    }
    .payment-history {
        border-left: 1px solid black;
        width: 39.1%;
        display: flex;
    }
    .payment-type {
        display: inline-grid;
        border-right: 1px solid black;
        width: 66.66%;
    }
    .payment {
        width: 33.33%;
        display: inline-grid;
    }
    .amount-word {
        display: flex;
        border-right: 1px solid black;
        border-left: 1px solid black;
        width: 100%;
        padding-left: 8px;
    }
    .payment-mode {
        display: flex;
        border-right: 1px solid black;
        border-left: 1px solid black;
        border-top: 1px solid black;
        width: 100%;
        padding-left: 8px;
    }
    .payment-status {
        display: flex;
        border-right: 1px solid black;
        border-left: 1px solid black;
        border-top: 1px solid black;
        width: 100%;
        padding-left: 8px;
    }
    .note {
        display: flex;
        border: 1px solid black;
        width: 100%;
        padding-left: 8px;    
    }
    
    .terms-condotion {
        display: flex;
        border-left: 1px solid black;
        border-right: 1px solid black;
        border-bottom: 1px solid black;
        width: 100%;
        padding-left: 8px;
    }
    .download-pdf{
        display:flex;
        align-items:center;
        justify-content:center;
        padding-top:3%;
    }
    .bank-name{
        display:flex;
    }
    .account-name{
        display:flex;
    }
    .acc-no{
        display:flex;
    }
    .ifcs-code{
        display:flex;
    }
    .acc-type{
        display:flex;
    }
}
