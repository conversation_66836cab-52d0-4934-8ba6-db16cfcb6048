﻿@page "/activity"
<head>
    <link href="https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@100;200;300;400;500&display=swap" rel="stylesheet">
</head>

<div class="tenantmasterheader">
    <div class="headerText">
        <span class="headername">
            <svg width="164" height="25" viewBox="0 0 164 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.23589 12.2657L12.634 17.2137C12.8784 17.4886 13.333 17.3157 13.333 16.9479L13.333 7.05208C13.333 6.68427 12.8784 6.51143 12.634 6.78633L8.23589 11.7343C8.10118 11.8858 8.10118 12.1142 8.23589 12.2657Z" fill="#292A2B" />
                <path d="M30.32 20L35.78 6H38.22L43.64 20H40.94L37.94 12.04C37.8733 11.88 37.78 11.6267 37.66 11.28C37.5533 10.9333 37.4333 10.56 37.3 10.16C37.1667 9.74667 37.0467 9.36667 36.94 9.02C36.8333 8.66 36.7533 8.4 36.7 8.24L37.2 8.22C37.12 8.48667 37.0267 8.78667 36.92 9.12C36.8133 9.45333 36.7 9.8 36.58 10.16C36.46 10.52 36.34 10.8667 36.22 11.2C36.1133 11.5333 36.0133 11.8333 35.92 12.1L32.92 20H30.32ZM32.8 16.8L33.7 14.54H40.08L41.04 16.8H32.8ZM49.5342 20.2C48.5476 20.2 47.6609 19.96 46.8742 19.48C46.0876 19 45.4676 18.3467 45.0142 17.52C44.5609 16.6933 44.3342 15.76 44.3342 14.72C44.3342 13.68 44.5609 12.7467 45.0142 11.92C45.4676 11.0933 46.0876 10.44 46.8742 9.96C47.6609 9.48 48.5476 9.24 49.5342 9.24C50.4809 9.24 51.3409 9.42 52.1142 9.78C52.8876 10.14 53.4876 10.64 53.9142 11.28L52.5542 12.92C52.3542 12.64 52.1009 12.3867 51.7942 12.16C51.4876 11.9333 51.1609 11.7533 50.8142 11.62C50.4676 11.4867 50.1209 11.42 49.7742 11.42C49.1876 11.42 48.6609 11.5667 48.1942 11.86C47.7409 12.14 47.3809 12.5333 47.1142 13.04C46.8476 13.5333 46.7142 14.0933 46.7142 14.72C46.7142 15.3467 46.8476 15.9067 47.1142 16.4C47.3942 16.8933 47.7676 17.2867 48.2342 17.58C48.7009 17.8733 49.2209 18.02 49.7942 18.02C50.1409 18.02 50.4742 17.9667 50.7942 17.86C51.1276 17.74 51.4409 17.5733 51.7342 17.36C52.0276 17.1467 52.3009 16.88 52.5542 16.56L53.9142 18.22C53.4609 18.8067 52.8342 19.2867 52.0342 19.66C51.2476 20.02 50.4142 20.2 49.5342 20.2ZM56.9355 20V6.78H59.3955V20H56.9355ZM54.8555 11.74V9.46H61.6955V11.74H54.8555ZM63.8489 20V9.46H66.3089V20H63.8489ZM65.0489 7.22C64.5556 7.22 64.1689 7.1 63.8889 6.86C63.6222 6.60667 63.4889 6.25333 63.4889 5.8C63.4889 5.37333 63.6289 5.02667 63.9089 4.76C64.1889 4.49333 64.5689 4.36 65.0489 4.36C65.5556 4.36 65.9422 4.48667 66.2089 4.74C66.4889 4.98 66.6289 5.33333 66.6289 5.8C66.6289 6.21333 66.4889 6.55333 66.2089 6.82C65.9289 7.08667 65.5422 7.22 65.0489 7.22ZM72.8083 20L68.3683 9.46H71.0483L74.0483 17.34L73.5283 17.48L76.6883 9.46H79.3083L74.6483 20H72.8083ZM81.388 20V9.46H83.848V20H81.388ZM82.588 7.22C82.0946 7.22 81.708 7.1 81.428 6.86C81.1613 6.60667 81.028 6.25333 81.028 5.8C81.028 5.37333 81.168 5.02667 81.448 4.76C81.728 4.49333 82.108 4.36 82.588 4.36C83.0946 4.36 83.4813 4.48667 83.748 4.74C84.028 4.98 84.168 5.33333 84.168 5.8C84.168 6.21333 84.028 6.55333 83.748 6.82C83.468 7.08667 83.0813 7.22 82.588 7.22ZM88.1073 20V6.78H90.5673V20H88.1073ZM86.0273 11.74V9.46H92.8673V11.74H86.0273ZM96.3408 24.6L98.5808 19.42L98.6008 21.06L93.4008 9.46H96.2408L99.1408 16.34C99.2341 16.5133 99.3274 16.7467 99.4208 17.04C99.5141 17.32 99.5941 17.6 99.6608 17.88L99.1608 18.04C99.2541 17.7867 99.3541 17.52 99.4608 17.24C99.5674 16.9467 99.6674 16.6467 99.7608 16.34L102.241 9.46H105.101L100.701 20L98.9008 24.6H96.3408ZM115.754 20V8.4H111.754V6H122.474V8.4H118.354V20H115.754ZM126.572 20.2C125.706 20.2 124.919 19.96 124.212 19.48C123.506 19 122.939 18.3467 122.512 17.52C122.086 16.6933 121.872 15.7533 121.872 14.7C121.872 13.6467 122.086 12.7067 122.512 11.88C122.939 11.0533 123.519 10.4067 124.252 9.94C124.986 9.47333 125.812 9.24 126.732 9.24C127.266 9.24 127.752 9.32 128.192 9.48C128.632 9.62667 129.019 9.84 129.352 10.12C129.686 10.4 129.959 10.72 130.172 11.08C130.399 11.44 130.552 11.8267 130.632 12.24L130.092 12.1V9.46H132.572V20H130.072V17.48L130.652 17.38C130.559 17.74 130.386 18.0933 130.132 18.44C129.892 18.7733 129.586 19.0733 129.212 19.34C128.852 19.5933 128.446 19.8 127.992 19.96C127.552 20.12 127.079 20.2 126.572 20.2ZM127.252 18.02C127.826 18.02 128.332 17.88 128.772 17.6C129.212 17.32 129.552 16.9333 129.792 16.44C130.046 15.9333 130.172 15.3533 130.172 14.7C130.172 14.06 130.046 13.4933 129.792 13C129.552 12.5067 129.212 12.12 128.772 11.84C128.332 11.56 127.826 11.42 127.252 11.42C126.679 11.42 126.172 11.56 125.732 11.84C125.306 12.12 124.972 12.5067 124.732 13C124.492 13.4933 124.372 14.06 124.372 14.7C124.372 15.3533 124.492 15.9333 124.732 16.44C124.972 16.9333 125.306 17.32 125.732 17.6C126.172 17.88 126.679 18.02 127.252 18.02ZM141.244 20.2C140.778 20.2 140.318 20.1333 139.864 20C139.424 19.8533 139.024 19.66 138.664 19.42C138.304 19.18 138.004 18.9133 137.764 18.62C137.524 18.3133 137.364 18.0067 137.284 17.7L137.864 17.44L137.804 19.96H135.424V5.2H137.884V11.9L137.444 11.7C137.511 11.38 137.658 11.08 137.884 10.8C138.124 10.5067 138.418 10.2467 138.764 10.02C139.111 9.78 139.491 9.59333 139.904 9.46C140.318 9.31333 140.738 9.24 141.164 9.24C142.111 9.24 142.944 9.47333 143.664 9.94C144.398 10.4067 144.971 11.0533 145.384 11.88C145.811 12.7067 146.024 13.6467 146.024 14.7C146.024 15.7667 145.818 16.7133 145.404 17.54C144.991 18.3667 144.418 19.02 143.684 19.5C142.964 19.9667 142.151 20.2 141.244 20.2ZM140.724 18.04C141.284 18.04 141.784 17.9 142.224 17.62C142.664 17.3267 143.011 16.9333 143.264 16.44C143.518 15.9333 143.644 15.3533 143.644 14.7C143.644 14.06 143.518 13.4933 143.264 13C143.024 12.5067 142.684 12.12 142.244 11.84C141.804 11.56 141.298 11.42 140.724 11.42C140.151 11.42 139.644 11.56 139.204 11.84C138.764 12.12 138.418 12.5067 138.164 13C137.911 13.4933 137.784 14.06 137.784 14.7C137.784 15.3533 137.911 15.9333 138.164 16.44C138.418 16.9333 138.764 17.3267 139.204 17.62C139.644 17.9 140.151 18.04 140.724 18.04ZM148.237 20V5.2H150.717V20H148.237ZM158.353 20.2C157.246 20.2 156.266 19.9667 155.413 19.5C154.573 19.0333 153.913 18.4 153.433 17.6C152.966 16.7867 152.733 15.86 152.733 14.82C152.733 13.9933 152.866 13.24 153.133 12.56C153.399 11.88 153.766 11.2933 154.233 10.8C154.713 10.2933 155.279 9.90667 155.933 9.64C156.599 9.36 157.326 9.22 158.113 9.22C158.806 9.22 159.453 9.35333 160.053 9.62C160.653 9.88667 161.173 10.2533 161.613 10.72C162.053 11.1733 162.386 11.72 162.613 12.36C162.853 12.9867 162.966 13.6733 162.953 14.42L162.933 15.28H154.393L153.933 13.6H160.873L160.553 13.94V13.5C160.513 13.0867 160.379 12.7267 160.153 12.42C159.926 12.1 159.633 11.8533 159.273 11.68C158.926 11.4933 158.539 11.4 158.113 11.4C157.459 11.4 156.906 11.5267 156.453 11.78C156.013 12.0333 155.679 12.4 155.453 12.88C155.226 13.3467 155.113 13.9333 155.113 14.64C155.113 15.32 155.253 15.9133 155.533 16.42C155.826 16.9267 156.233 17.32 156.753 17.6C157.286 17.8667 157.906 18 158.613 18C159.106 18 159.559 17.92 159.973 17.76C160.386 17.6 160.833 17.3133 161.313 16.9L162.533 18.6C162.173 18.9333 161.759 19.22 161.293 19.46C160.839 19.6867 160.359 19.8667 159.853 20C159.346 20.1333 158.846 20.2 158.353 20.2Z" fill="#292A2B" />
            </svg>

        </span>
    </div>
    <div class="headerbuttons">
        <span @onclick="async () => { Filter = new(); Paginate(1); await GetUserActivity(); }">
            <svg width="89" height="38" viewBox="0 0 89 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="89" height="38" rx="4" fill="#292A2B" />
                <path d="M16.4396 24.4261C16.5371 24.4727 16.6439 24.4967 16.752 24.4964C16.9183 24.4964 17.0796 24.4398 17.2094 24.3359C17.3392 24.232 17.4298 24.0869 17.4662 23.9247C17.5026 23.7625 17.4827 23.5926 17.4097 23.4432C17.3367 23.2938 17.215 23.1737 17.0647 23.1027C16.1347 22.6641 15.3813 21.9225 14.928 20.9996C14.4747 20.0767 14.3484 19.0272 14.5698 18.023C14.7912 17.0189 15.3471 16.1198 16.1465 15.4731C16.616 15.0932 17.1531 14.8137 17.7247 14.6462L17.1747 15.7462C17.119 15.8577 17.0927 15.9816 17.0984 16.1061C17.104 16.2307 17.1413 16.3517 17.2068 16.4577C17.2723 16.5638 17.3638 16.6513 17.4726 16.7121C17.5815 16.7728 17.704 16.8048 17.8287 16.8049C17.9648 16.8055 18.0983 16.7678 18.2141 16.6963C18.3299 16.6247 18.4233 16.5222 18.4838 16.4002L19.6545 14.0587C19.7412 13.8851 19.7554 13.6842 19.694 13.5001C19.6326 13.3161 19.5005 13.1639 19.3269 13.0772C19.2197 13.0236 19.102 12.9977 18.9843 13.0003C18.9763 13 18.9683 13 18.9603 13C17.6004 13.009 16.2839 13.4797 15.2266 14.335C14.1693 15.1902 13.4338 16.3793 13.1409 17.7073C12.8479 19.0353 13.0148 20.4234 13.6141 21.6441C14.2135 22.8648 15.2097 23.8457 16.4396 24.4261Z" fill="white" />
                <path d="M19.0193 24.9998C19.0246 24.9999 19.0299 25 19.0352 25H19.0397C20.3996 24.991 21.7161 24.5203 22.7734 23.665C23.8307 22.8098 24.5662 21.6207 24.8591 20.2927C25.1521 18.9647 24.9852 17.5766 24.3859 16.3559C23.7865 15.1351 22.7903 14.1542 21.5604 13.5739C21.463 13.5272 21.3563 13.503 21.2483 13.503C21.1095 13.5036 20.9737 13.5433 20.8565 13.6176C20.7393 13.692 20.6454 13.7979 20.5858 13.9232C20.5448 14.0101 20.5213 14.1042 20.5167 14.2001C20.5121 14.2961 20.5265 14.392 20.5589 14.4824C20.5914 14.5728 20.6414 14.656 20.706 14.727C20.7707 14.7981 20.8487 14.8558 20.9356 14.8967C21.8655 15.3354 22.6188 16.0771 23.0721 17C23.5253 17.923 23.6516 18.9725 23.4302 19.9767C23.2088 20.9808 22.6528 21.8799 21.8535 22.5267C21.384 22.9066 20.847 23.1861 20.2753 23.3536L20.8252 22.2538C20.912 22.0802 20.9262 21.8793 20.8647 21.6952C20.8033 21.5112 20.6713 21.359 20.4977 21.2723C20.3241 21.1856 20.1232 21.1714 19.9391 21.2328C19.755 21.2943 19.6029 21.4263 19.5162 21.5999L18.3454 23.9413C18.2898 24.0528 18.2635 24.1767 18.2691 24.3013C18.2747 24.4258 18.312 24.5468 18.3775 24.6528C18.443 24.7589 18.5346 24.8464 18.6434 24.9072C18.7522 24.968 18.8748 24.9999 18.9994 25C19.006 25 19.0127 25 19.0193 24.9998Z" fill="white" />
                <path d="M35.128 24V15.6H38.776C39.28 15.6 39.74 15.72 40.156 15.96C40.572 16.192 40.9 16.512 41.14 16.92C41.388 17.32 41.512 17.772 41.512 18.276C41.512 18.756 41.388 19.2 41.14 19.608C40.9 20.008 40.572 20.328 40.156 20.568C39.748 20.8 39.288 20.916 38.776 20.916H36.652V24H35.128ZM40 24L37.864 20.208L39.472 19.908L41.848 24.012L40 24ZM36.652 19.56H38.788C39.02 19.56 39.22 19.508 39.388 19.404C39.564 19.292 39.7 19.14 39.796 18.948C39.892 18.756 39.94 18.544 39.94 18.312C39.94 18.048 39.88 17.82 39.76 17.628C39.64 17.436 39.472 17.284 39.256 17.172C39.04 17.06 38.792 17.004 38.512 17.004H36.652V19.56ZM45.9608 24.12C45.2968 24.12 44.7088 23.98 44.1968 23.7C43.6928 23.42 43.2968 23.04 43.0088 22.56C42.7288 22.072 42.5888 21.516 42.5888 20.892C42.5888 20.396 42.6688 19.944 42.8288 19.536C42.9888 19.128 43.2088 18.776 43.4888 18.48C43.7768 18.176 44.1168 17.944 44.5088 17.784C44.9088 17.616 45.3448 17.532 45.8168 17.532C46.2328 17.532 46.6208 17.612 46.9808 17.772C47.3408 17.932 47.6528 18.152 47.9168 18.432C48.1808 18.704 48.3808 19.032 48.5168 19.416C48.6608 19.792 48.7288 20.204 48.7208 20.652L48.7088 21.168H43.5848L43.3088 20.16H47.4728L47.2808 20.364V20.1C47.2568 19.852 47.1768 19.636 47.0408 19.452C46.9048 19.26 46.7288 19.112 46.5128 19.008C46.3048 18.896 46.0728 18.84 45.8168 18.84C45.4248 18.84 45.0928 18.916 44.8208 19.068C44.5568 19.22 44.3568 19.44 44.2208 19.728C44.0848 20.008 44.0168 20.36 44.0168 20.784C44.0168 21.192 44.1008 21.548 44.2688 21.852C44.4448 22.156 44.6888 22.392 45.0008 22.56C45.3208 22.72 45.6928 22.8 46.1168 22.8C46.4128 22.8 46.6848 22.752 46.9328 22.656C47.1808 22.56 47.4488 22.388 47.7368 22.14L48.4688 23.16C48.2528 23.36 48.0048 23.532 47.7248 23.676C47.4528 23.812 47.1648 23.92 46.8608 24C46.5568 24.08 46.2568 24.12 45.9608 24.12ZM50.6041 24V17.112C50.6041 16.728 50.6881 16.388 50.8561 16.092C51.0241 15.788 51.2561 15.552 51.5521 15.384C51.8481 15.208 52.1921 15.12 52.5841 15.12C52.8561 15.12 53.1081 15.168 53.3401 15.264C53.5721 15.352 53.7681 15.476 53.9281 15.636L53.4721 16.752C53.3681 16.664 53.2561 16.596 53.1361 16.548C53.0241 16.5 52.9161 16.476 52.8121 16.476C52.6521 16.476 52.5161 16.504 52.4041 16.56C52.3001 16.608 52.2201 16.684 52.1641 16.788C52.1161 16.892 52.0921 17.016 52.0921 17.16V24H51.3481C51.2041 24 51.0681 24 50.9401 24C50.8201 24 50.7081 24 50.6041 24ZM49.6201 19.104V17.808H53.5201V19.104H49.6201ZM54.739 24V17.676H56.191L56.215 19.692L56.011 19.236C56.099 18.916 56.251 18.628 56.467 18.372C56.683 18.116 56.931 17.916 57.211 17.772C57.499 17.62 57.799 17.544 58.111 17.544C58.247 17.544 58.375 17.556 58.495 17.58C58.623 17.604 58.727 17.632 58.807 17.664L58.411 19.284C58.323 19.236 58.215 19.196 58.087 19.164C57.959 19.132 57.831 19.116 57.703 19.116C57.503 19.116 57.311 19.156 57.127 19.236C56.951 19.308 56.795 19.412 56.659 19.548C56.523 19.684 56.415 19.844 56.335 20.028C56.263 20.204 56.227 20.404 56.227 20.628V24H54.739ZM62.4843 24.12C61.8203 24.12 61.2323 23.98 60.7203 23.7C60.2163 23.42 59.8203 23.04 59.5323 22.56C59.2523 22.072 59.1123 21.516 59.1123 20.892C59.1123 20.396 59.1923 19.944 59.3523 19.536C59.5123 19.128 59.7323 18.776 60.0123 18.48C60.3003 18.176 60.6403 17.944 61.0323 17.784C61.4323 17.616 61.8683 17.532 62.3403 17.532C62.7563 17.532 63.1443 17.612 63.5043 17.772C63.8643 17.932 64.1763 18.152 64.4403 18.432C64.7043 18.704 64.9043 19.032 65.0403 19.416C65.1843 19.792 65.2523 20.204 65.2443 20.652L65.2323 21.168H60.1083L59.8323 20.16H63.9963L63.8043 20.364V20.1C63.7803 19.852 63.7003 19.636 63.5643 19.452C63.4283 19.26 63.2523 19.112 63.0363 19.008C62.8283 18.896 62.5963 18.84 62.3403 18.84C61.9483 18.84 61.6163 18.916 61.3443 19.068C61.0803 19.22 60.8803 19.44 60.7443 19.728C60.6083 20.008 60.5403 20.36 60.5403 20.784C60.5403 21.192 60.6243 21.548 60.7923 21.852C60.9683 22.156 61.2123 22.392 61.5243 22.56C61.8443 22.72 62.2163 22.8 62.6403 22.8C62.9363 22.8 63.2083 22.752 63.4563 22.656C63.7043 22.56 63.9723 22.388 64.2603 22.14L64.9922 23.16C64.7763 23.36 64.5283 23.532 64.2483 23.676C63.9763 23.812 63.6883 23.92 63.3843 24C63.0803 24.08 62.7803 24.12 62.4843 24.12ZM68.6635 24.12C68.1035 24.12 67.5995 24.028 67.1515 23.844C66.7115 23.652 66.3515 23.38 66.0715 23.028L67.0315 22.2C67.2715 22.472 67.5395 22.668 67.8355 22.788C68.1315 22.908 68.4435 22.968 68.7715 22.968C68.9075 22.968 69.0275 22.952 69.1315 22.92C69.2435 22.888 69.3395 22.84 69.4195 22.776C69.4995 22.712 69.5595 22.64 69.5995 22.56C69.6475 22.472 69.6715 22.376 69.6715 22.272C69.6715 22.08 69.5995 21.928 69.4555 21.816C69.3755 21.76 69.2475 21.7 69.0715 21.636C68.9035 21.572 68.6835 21.508 68.4115 21.444C67.9795 21.332 67.6195 21.204 67.3315 21.06C67.0435 20.908 66.8195 20.74 66.6595 20.556C66.5235 20.404 66.4195 20.232 66.3475 20.04C66.2835 19.848 66.2515 19.64 66.2515 19.416C66.2515 19.136 66.3115 18.884 66.4315 18.66C66.5595 18.428 66.7315 18.228 66.9475 18.06C67.1635 17.892 67.4155 17.764 67.7035 17.676C67.9915 17.588 68.2915 17.544 68.6035 17.544C68.9235 17.544 69.2315 17.584 69.5275 17.664C69.8315 17.744 70.1115 17.86 70.3675 18.012C70.6315 18.156 70.8555 18.332 71.0395 18.54L70.2235 19.44C70.0715 19.296 69.9035 19.168 69.7195 19.056C69.5435 18.944 69.3635 18.856 69.1795 18.792C68.9955 18.72 68.8235 18.684 68.6635 18.684C68.5115 18.684 68.3755 18.7 68.2555 18.732C68.1355 18.756 68.0355 18.796 67.9555 18.852C67.8755 18.908 67.8115 18.98 67.7635 19.068C67.7235 19.148 67.7035 19.244 67.7035 19.356C67.7115 19.452 67.7355 19.544 67.7755 19.632C67.8235 19.712 67.8875 19.78 67.9675 19.836C68.0555 19.892 68.1875 19.956 68.3635 20.028C68.5395 20.1 68.7675 20.168 69.0475 20.232C69.4395 20.336 69.7675 20.452 70.0315 20.58C70.2955 20.708 70.5035 20.856 70.6555 21.024C70.8075 21.168 70.9155 21.336 70.9795 21.528C71.0435 21.72 71.0755 21.932 71.0755 22.164C71.0755 22.54 70.9675 22.876 70.7515 23.172C70.5435 23.468 70.2555 23.7 69.8875 23.868C69.5275 24.036 69.1195 24.12 68.6635 24.12ZM72.3757 24V15.12H73.8397V18.972L73.5637 19.116C73.6437 18.828 73.7997 18.568 74.0317 18.336C74.2637 18.096 74.5397 17.904 74.8597 17.76C75.1797 17.616 75.5077 17.544 75.8437 17.544C76.3237 17.544 76.7237 17.64 77.0437 17.832C77.3717 18.024 77.6157 18.312 77.7757 18.696C77.9437 19.08 78.0277 19.56 78.0277 20.136V24H76.5517V20.244C76.5517 19.924 76.5077 19.66 76.4197 19.452C76.3317 19.236 76.1957 19.08 76.0117 18.984C75.8277 18.88 75.6037 18.832 75.3397 18.84C75.1237 18.84 74.9237 18.876 74.7397 18.948C74.5637 19.012 74.4077 19.108 74.2717 19.236C74.1437 19.356 74.0397 19.496 73.9597 19.656C73.8877 19.816 73.8517 19.992 73.8517 20.184V24H73.1197C72.9757 24 72.8397 24 72.7117 24C72.5917 24 72.4797 24 72.3757 24Z" fill="white" />
            </svg>
        </span>
    </div>
</div>

<div class="TenantMasterTable">
    <div class="TenantMasterTableHeader">
        <div class="buttons">
            @if (CustomFilter == false)
            {
                <span class="">Filter : </span>
                <span class="buttonbutton" @onclick=GetTodayActivity>Today</span>
                <span class="buttonbutton" @onclick=GetYesterdayActivity>Yesterday</span>
                <span class="buttonbutton" @onclick=GetLastWeekActivity>Last week</span>
                <span class="buttonbutton" @onclick=GetLastMonthActivity>Last month</span>
                <span class="buttonbutton" @onclick=CustomFilterAsync>Custom</span>
            }
            else
            {
                <span>Custom</span>
                <span>From Date:</span>
                <input type="date" @bind-value="@Filter.FromDate" />
                <span>To Date:</span>
                <input type="date" @bind-value="@Filter.ToDate" />
                <span class="buttonbutton" @onclick="@(async ()=> {PageNumber=1;PageSize=10;await GetUserActivity(); CustomFilter = false;})">Submit</span>
                <span class="buttonbutton" @onclick=@(() => CustomFilter = false)>Cancel</span>
            }
            
        </div>
        <span title="Page Size">
            <select id="pagesizeinput" @bind="PageSize" @bind:after="GetUserActivity">
                @foreach (var dv in PagesizeList)
                {
                    <option value="@dv">@dv</option>
                }
            </select>
        </span>
    </div>
    @if (Collection == null)
    {
        <span>No Data Found</span>
    }
    else
    {
        @if (Collection.Any(i => i != null))
        {
            <div class="all-tables">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>SlNo</th>
                                <th>Time</th>
                                <th>User</th>
                                @*<th>TenantId</th>*@
                                <th>Role</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Collection.Select((value, i) => new { value, i }))
                            {
                                <tr>
                                    <td>@(((PageNumber - 1) * PageSize) + @item.i + 1)</td>
                                    <td>@item.value?.ActivityTime</td>
                                    <td>@item.value?.FirstName @item.value?.LastName</td>
                                    @*<td>@UserModel?.TenantId</td>*@
                                    <td>@item.value?.Role</td>
                                    <td>@item.value?.Activity</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                
            </div>
        }
        else
        {
            <span>No Data Found</span>
        }
    }

    <div class="tenantmasterfooter">
        <div class="pagesize">
            <div class="pagesizebox">
                
                <span> @ShowingCount of @TotalCount</span>
            </div>
        </div>
        <div class="pagenumber">
            @if (Collection.Count < PageSize)
            {
                <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()">Back</span>
            }
            else
            {
                @for (int i = PageNumber - 1; i <= PageNumber + 1; i++)
                {
                    if (i < PageNumber || i == 0)
                    {
                        <span class="pagenumberbutton" @onclick="()=> DecreasePageNumber()"> &lt;&lt; </span>
                    }
                    else if (i == PageNumber)
                    {
                        <span id="pagenumbercircle">@i</span>
                    }
                    else if (i > PageNumber)
                    {
                        <span class="pagenumberbutton" @onclick="()=> IncreasePageNumber()">&gt;&gt;</span>
                    }
                }
            }



        </div>
    </div>
</div>