﻿@* <h3>EmailTemplateFilter</h3> *@

@code {

}

<RadzenCard class="">
    <div style="" class="">
        <EditForm class="form-floating" Model="@MailTemaplate" OnInvalidSubmit="SendDueAmountReminderEmailTemplate">
            <div class="form-group">
                <label for="from_date">VendorEmail</label><br />
                <RadzenNumeric @bind-Value="@MailTemaplate.VendorEmail" Name="DatePickerDateOnlyType" />
            </div>
            <div class="form-group">
                <label for="to_date">PendingAmountTo</label><br />
                <RadzenNumeric @bind-Value="@MailTemaplate.VendorEmail" Name="DatePickerDateOnlyType" />
            </div>

            <div class="form-group">
                <label for="VendorNames">VendorNames</label><br />
                <RadzenTextBox @bind-Value="MailTemaplate.VendorNames" class="w-100" aria-label="Enter VendorNames" Placeholder="Enter VendorNames" />
            </div>
            <div class="form-group">
                <label for="LastInoviceNumber">LastInoviceNumber</label><br />
                <RadzenTextBox @bind-Value="MailTemaplate.LastInvoiceNumber" class="w-100" aria-label="Enter LastInoviceNumber" Placeholder="Enter LastInvoiceNumber" />
            </div>
            <div class="form-group">
                <label for="GSTNumber">GSTNumber</label><br />
                <RadzenTextBox @bind-Value="MailTemaplate.GSTNumber" class="w-100" aria-label="Enter GSTNumber" Placeholder="Enter GSTNumber" />
            </div>

            <div class="form-group">
                <label for="billing_type">Billing Type</label><br />
                <InputSelect id="billing_type" @bind-Value="MailTemaplate.BillingType">
                    @foreach (var item in Enum.GetValues(typeof(Lrb.Admin.Data.Enums.BillingType)))
                    {
                        <option value="@item">@item</option>
                    }
                </InputSelect>
            </div>

        </EditForm>
    </div>
    <div class="d-flex flex-row" style="margin-left: 4px">
        <RadzenButton Click=@SendDueAmountReminderEmailTemplate Text="Filter" ButtonStyle="ButtonStyle.Secondary" Style="margin-right:4px" /><br />
        <RadzenButton Click="@ResetTemplate" Text="Reset" ButtonStyle="ButtonStyle.Secondary" Style="margin-right:4px" /><br />
    </div>
</RadzenCard>

