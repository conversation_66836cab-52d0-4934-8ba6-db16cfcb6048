﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Entities.Base
{
    public abstract class BaseEntity : BaseEntity<Guid>
    {
        public BaseEntity() => Id = Guid.NewGuid();
    }

    public abstract class BaseEntity<TId> : IEntity<TId>
    {
        public TId Id { get; set; } = default!;
        public bool IsDeleted { get; set; }
    }
}
