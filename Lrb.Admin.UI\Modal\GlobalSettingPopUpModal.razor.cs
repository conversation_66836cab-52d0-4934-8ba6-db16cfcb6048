﻿using Blazored.LocalStorage;
using Lrb.Admin.Data.Models.GlobalSetting;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json.Linq;

namespace Lrb.Admin.UI.Modal
{
    public partial class GlobalSettingPopUpModal
    {
        [Inject]
        public IGlobalService GlobalService { get; set; }
        [Inject]
        public IJSRuntime JSRuntime { get; set; }
        [Inject]
        public ILocalStorageService LocalStorage { get; set; }
        [Parameter]
        public string TenantId { get; set; }
        [Parameter]
        public EventCallback<bool> ValueChanged { get; set; }
        [Parameter]
        public string? ReadConnectionString { get; set; }
        [Parameter]
        public string? ConnectionString { get; set; }
        private bool IsToggle { get; set; } = false;
        public GlobalSettingViewModel GlobalSettingView { get; set; } = new();
        public DuplicateLeadViewModel DuplicateLeadModel { get; set; } = new();
        public bool OTPSettings { get; set; }
        public bool? IsDomainEnabled { get; set; }
        public bool? ShouldHideSubscription { get; set; }
        public bool? IsCallRecordingEnabled { get; set; }

        protected override async Task OnInitializedAsync()
        {
            GlobalSettingView = await GlobalService.GetSettingByTenantAsync(TenantId, ReadConnectionString);
            OTPSettings = await OTPEnable();
            DuplicateLeadModel = await GlobalService.GetDuplicateLead(TenantId, ReadConnectionString);
            IsDomainEnabled = await GlobalService.GetIsDomainEnabledAsync(TenantId);
            ShouldHideSubscription = await GlobalService.GetShouldHideSubscriptionAsync(TenantId, ReadConnectionString) ?? false;
            if (DuplicateLeadModel == null)
            {
                DuplicateLeadModel = new();
            }
            IsCallRecordingEnabled =  GlobalService.GetIsCallRecordingEnabledAsync(TenantId, ReadConnectionString).Result.Data;
        }

        public async Task<bool> OTPEnable()
        {
            if (GlobalSettingView.OTPSettings != null)
            {
                JObject? jsonObj = JObject.Parse(GlobalSettingView.OTPSettings);
                bool isEnabled = (bool)jsonObj["IsEnabled"];
                if (isEnabled)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        private async Task UpdateInternationalNumber()
        {
            bool confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you Sure ?");
            if (confirmed)
            {
                var response = await GlobalService.UpdateInternationalNumberAsync(TenantId,ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.HasInternationalSupport = response.Data;
                }
            }
        }

        private async Task UpdateDailyStausEnabledFeature()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are you Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateDailyStatusAsync(TenantId,ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.HasDailyStatusFeatureEnabled = response.Data;
                }
            }
        }
        private async Task UpdateLeadStatusPendingToUpdate()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateLeadStatusPendingAsync(TenantId,ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsLeadStatusToPendingUpdatesEnabled = response.Data;
                }
            }
        }
        private async Task UpdateLeadExportEnabled()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateLeadExportAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsLeadsExportEnabled = response.Data;
                }
            }
        }
        private async Task UpdateLeadSourceEditable()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateLeadSourceAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsLeadSourceEditable = response.Data;
                }
            }
        }
        private async Task UpdateDuplicateLeadFeature()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are you Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateDuplicateLeadAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    DuplicateLeadModel.IsFeatureAdded = response.Data;
                }
            }
        }

        private async Task UpdateMicrositeEditable()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateMicrositeFeatureAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsMicrositeFeatureEnabled = response.Data;
                }
            }
        }

        private async Task UpdateZoneLocationEditable()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateZoneLocationFeatureAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsZoneLocationEnabled = response.Data;
                }
            }
        }

        private async Task UpdateShouldEnablePropertyListing()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateShouldEnablePropertyListingAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.ShouldEnablePropertyListing = response.Data;
                }
            }
        }
        private async Task UpdateShouldHideSubscription()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateShouldHideSubscriptionAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    ShouldHideSubscription = response.Data;
                }
            }
        }

        private async Task EnableCustomLeadFormAsync()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.UpdateEnableCustomLeadFormAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    GlobalSettingView.IsCustomLeadFormEnabled = response.Data;
                }
            }
        }

        private async Task DNSEnableDisable()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.DNSEnableDisableAsync(TenantId, IsDomainEnabled);
                if (response != null)
                {
                    if(response.Data == true)
                    {
                        IsDomainEnabled = !IsDomainEnabled;
                    }
                   
                }
            }
        }

        private async Task ToggleCallRecording()
        {
            bool confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are You Sure ?");
            if (confirm)
            {
                var response = await GlobalService.ToggleCallRecordingAsync(TenantId, ConnectionString);
                if (response != null)
                {
                    ShouldHideSubscription = response.Data;
                }
            }
        }
    }
}
