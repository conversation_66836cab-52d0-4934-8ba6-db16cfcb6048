﻿@using Lrb.Admin.Data.Models.PendingPayment
@using Lrb.Admin.Data.Services


@* @page "/email-template" *@

@inject IPaymentService PaymentService
@inject INotificationService NotificationService
@* @inject EmailHelper EmailHelper *@

@page "/parent"
@using Lrb.Admin.UI.Pages.PendingPayment

@* <h3>Send Email</h3> *@

<style>
    /* Styling the chip container to stay left-aligned */
    .chip-container-aligned {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
        gap: 5px;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: #f9f9f9;
        min-height: 60px;
        width: 100%; /* Adjusted width for alignment */
    }

    .chip {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        background-color: #e0e0e0;
        border-radius: 20px;
        margin-right: 5px;
    }

    .chip-close {
        font-size: 16px;
        color: red;
        cursor: pointer;
        margin-left: 5px;
    }

    .email-input {
        border: none;
        outline: none;
        padding: 5px;
        min-width: 150px;
        background-color: transparent;
        flex-grow: 1;
    }

    /* Container for vertical alignment */
    .vertical-container {
        display: flex;
        flex-direction: column;
        gap: 20px; /* Adjust spacing between items */
        width: 100%;
    }

    /* Content body styling */
    .content-body-box {
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 100%; /* Ensures content body takes full width */
    }

    .form-control {
        margin-bottom: 15px;
        width: 100%; /* Ensures form control takes full width */
    }

    /* Make sure input boxes take full width */
    .full-width-box {
        width: 100%;
    }

</style>


<EditForm Model="@Model" OnValidSubmit="HandleSendEmails">
    @* <EmailTemplate OnSendEmail="HandleSendEmail" /> *@
    <DataAnnotationsValidator />
    <ValidationSummary />

    <!-- From (non-removable, loaded from database) -->
    <div class="form-control chip-container-aligned full-width-box">
        <label for="from-email">From:</label>
        <InputText id="from-email" @bind-Value="From" class="form-control" readonly />
    </div>

    <!-- To (Editable, Removable with Chips) -->
<div class="form-control chip-container-aligned full-width-box">
    <label for="to-email">To Recipients:</label>
    <div class="chip-container-aligned">
        @foreach (var email in ToEmails)
        {
            <div class="chip">
                @email
                <span class="chip-close" @onclick="() => RemoveToEmail(email)">❌</span>
            </div>
        }
            <InputText @bind-Value="To" @onkeydown="HandleKeyPressToEmail" class="email-input" placeholder="Add To Email..." />
    </div>
</div>

<!-- Cc (Editable, Removable with Chips) -->
<div class="form-control chip-container-aligned full-width-box">
    <label for="cc-email">Cc Recipients:</label>
    <div class="chip-container-aligned">
        @foreach (var email in CcEmails)
        {
            <div class="chip">
                @email
                <span class="chip-close" @onclick="() => RemoveCcEmail(email)">❌</span>
            </div>
        }
        <InputText  @bind-Value="Cc" @onkeydown="HandleKeyPressCcEmail" class="email-input" placeholder="Add Cc Email..." />
    </div>
</div>

<!-- Bcc (Editable, Removable with Chips) -->
<div class="form-control chip-container-aligned full-width-box">
    <label for="bcc-email">Bcc Recipients:</label>
    <div class="chip-container-aligned">
        @foreach (var email in BccEmails)
        {
            <div class="chip">
                @email
                <span class="chip-close" @onclick="() => RemoveBccEmail(email)">❌</span>
            </div>
        }
        <InputText @bind-Value="Bcc" @onkeydown="HandleKeyPressBccEmail" class="email-input" placeholder="Add Bcc Email..." />
    </div>
</div>

    <!-- Subject (non-removable) -->
    <div class="form-control chip-container-aligned full-width-box">
        <label for="subject">Subject:</label>
        <InputText id="subject" @bind-Value="Subject" class="form-control" />
    </div>

    <!-- Content Body -->
    <div class="form-control chip-container-aligned full-width-box">
        <label for="email_ContentBody">Content Body:</label>
        <div id="email_ContentBody" class="form-control content-body-box" readonly>
            @((MarkupString)bodyContent)
        </div>
    </div>

    @if (ShowWarning)
    {
        <div class="alert alert-warning">Please make sure all email fields have at least one entry before sending.</div>
    }


</EditForm> 

    <div>
        @* <button class="btn btn-secondary btn-lg" style="background-color: gray; color: white;" @onclick="HandleSendEmails"> *@
        <button type="button" @onclick="HandleSendEmails" class="btn btn-primary">Confirm Send Email</button>
            @* <button @onclick="ValidSubmition">Send Emails</button> *@
       @*  </button> *@
    </div>

@* <div>
    <button type="button" @onclick="Reset" class="btn btn-primary">Reset</button>
   @*  <button type="submit" class="btn btn-primary">Submit</button> *@
@* </div> *@