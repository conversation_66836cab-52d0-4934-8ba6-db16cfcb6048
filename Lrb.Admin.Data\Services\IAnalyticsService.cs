﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Analytics;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Services
{
    public interface IAnalyticsService
    { 
        Task<PagedResponse<AnalyticsRecord,string>> GetAnalyticsRecordAsync(AnalyticsFilter filter);
        Task<PagedResponse<AnalyticsRecord, string>> GetTodayRecordAsync(AnalyticsFilter filter);
        Task<PagedResponse<AnalyticsRecord,string>> GetYesterdayRecordAsync(AnalyticsFilter filter);
        Task<PagedResponse<AnalyticsRecord, string>> GetLastWeekRecordAsync(AnalyticsFilter filter);
        Task<PagedResponse<AnalyticsRecord,string>> GetLastMonthRecordAsync(AnalyticsFilter filter);
    }
}
