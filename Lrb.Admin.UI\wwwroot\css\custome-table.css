﻿/*.tble-fixed {
    width: 100%;
    height: auto;  Specify desired height for vertical scrolling 
    overflow-x: auto;
    overflow-y: auto;
    max-height: 550px;
}

.content-table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 0.9em;
    border-radius: 5px;
    overflow: hidden;
    min-width: max-content;
    width: 100%;  Ensures table takes full width 
}

.content-table thead tr {
    background-color: #009879;
    color: #ffffff;
    text-align: left;
    font-weight: bold;
}

.content-table th,
.content-table td {
    padding: 12px 15px;
}

.content-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.content-table tbody tr:nth-of-type(even) {
    background-color: #3f3f3f;
}

.content-table tbody tr:last-of-type {
    border-bottom: 2px solid #009879;
}

.content-table tbody tr.active-row {
    font-weight: bold;
    color: #009879;
}

 Sticky Header 
.content-table th {
position: sticky;
top: 0;
background-color: #009879;
z-index: 1;  Ensure header appears above content during scroll 
}

 Adjustments for scrollbar 
.tble-fixed::-webkit-scrollbar {
    width: 10px;  Width of the scrollbar 
}

.tble-fixed::-webkit-scrollbar-track {
    background: #f1f1f1;  Color of the scrollbar track 
}

.tble-fixed::-webkit-scrollbar-thumb {
    background: #888;  Color of the scrollbar handle 
    border-radius: 5px;  Roundness of the scrollbar handle 
}

.tble-fixed::-webkit-scrollbar-thumb:hover {
    background: #555;  Color of the scrollbar handle on hover 
}
*/