﻿using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Admin.Data.Repos
{
    public interface ICustomMasterLeadRepository
    {
        public Task<IEnumerable<CustomSubStatusDto>> GetMasterLeadSubStatuses(string? tenantId);
        public Task<string?> CreateLeadSubStatus(string tenantId, CustomSubStatusDto custom);
        public Task<CustomSubStatusDto> GetCustomMasterLeadStatusById(Guid id);
        public Task<bool> DeleteLeadSubStatusesById(Guid? id, Guid? transferingId);
        public Task<long> GetLeadCountRequest(Guid? id,string? tenantId);
        public Task<CustomSubStatusDto> UpdateSubStatusAsync(Guid id, Guid baseId, string status, string displayName);
        public Task<IEnumerable<CustomMainStatusesDTO>> GetAllMainStatuses(string? tenantId);
        public Task<Guid> GetMainStatusId(string? tenantId, string? displayNameMainStatus);
        public Task<Guid> GetMasterLeadStatusBaseId(Guid? baseId);
        public Task<bool> GetUserActivity(string? tenantId, string? name);
    }
}
