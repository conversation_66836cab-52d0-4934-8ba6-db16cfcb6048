﻿using Blazored.Modal;
using Blazored.Modal.Services;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Admin.Data.Constant;
using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Entities;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Lrb.Admin.UI.Pages.Tenant
{
    public partial class TenantMasterFilter
    {
        [Parameter]
        public int PageNumber { get; set; }
        [Parameter]
        public int PageSize { get; set; }
        [Parameter]
        public List<int> Count { get; set; }
        [Parameter]
        public List<string> LicenseMonitor { get; set; }
        [Parameter]
        public GetAllTenantParameter Filter { get; set; } = new();
        [Parameter]
        public Func<Task> GetTenantsAsync { get; set; }
        private bool IsDateDisabled => Filter.DateType == null;
        private string? IsPaid { get; set; }
        private string? IsTestingAccount { get; set; }
        protected override void OnInitialized()
        {
            IsPaid = Filter.IsPaid.ToString();
            IsTestingAccount = Filter.IsTestingAccount.ToString();
        }
        private async Task CallGetTenantsAsync()
        {
            if(IsPaid == "True")
            {
                Filter.IsPaid = true;
            }
            else if(IsPaid == "False")
            {
                Filter.IsPaid = false;
            }
            if (IsTestingAccount == "True")
            {
                Filter.IsTestingAccount = true;
            }
            else if (IsTestingAccount == "False")
            {
                Filter.IsTestingAccount = false;
            }
            await GetTenantsAsync.Invoke();
            StateHasChanged();
        }
        private class DropDownOption
        {
            public string Text { get; set; } // What will be displayed
            public object Value { get; set; } // Actual value bound to Filter.IsPaid
        }
        private List<DropDownOption> dropdownOptions = new()
        {
            new DropDownOption { Text = "Select", Value = null },
            new DropDownOption { Text = "True", Value = true },
            new DropDownOption { Text = "False", Value = false }
        };

        private async Task ClearFilters()
        {
            Filter.FromDate = null;
            Filter.ToDate = null;
            Filter.DateType = null;
            Filter.LicenseFromDate = null;
            Filter.LicenseToDate = null;
            Filter.PaymentFromDate = null;
            Filter.PaymentToDate = null;
            Filter.NoOfUsers = default!;
            Filter.City = null;
            Filter.BillingType = default!;
            Filter.LicenseMonitor = null;
            Filter.IsPaid = null;
            Filter.IsTestingAccount = null;
            Filter.IsDormented = null;
            IsTestingAccount = "";
            IsPaid = "";
        }

    }
}
