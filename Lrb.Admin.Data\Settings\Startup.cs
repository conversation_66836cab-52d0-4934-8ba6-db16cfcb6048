﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Admin.Data.Settings
{
    public static class Startup
    {
        public static IServiceCollection AddSettings(this IServiceCollection services, IConfiguration config)
        {
            services.Configure<LrbApiEndPoints>(config.GetSection(nameof(LrbApiEndPoints)));
            services.Configure<LrbDomains>(config.GetSection(nameof(LrbDomains)));
            services.Configure<GoDaddySettings>(config.GetSection(nameof(GoDaddySettings)));
            services.Configure<LrbS3Bucket>(config.GetSection(nameof(LrbS3Bucket)));
            return services;
        }
    }
}
