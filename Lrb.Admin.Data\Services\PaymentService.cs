﻿using Lrb.Admin.Data.Filters;
using Lrb.Admin.Data.Models.Dtos;
using Lrb.Admin.Data.Models.Invoice;
using Lrb.Admin.Data.Models.PendingPayment;
using Lrb.Admin.Data.Models.Subscription;
using Lrb.Admin.Data.Models.Tenant;
using Lrb.Admin.Data.Models.Wrappers;
using Lrb.Admin.Data.Repos;
using Lrb.Admin.Data.Services.HTTP;
using Lrb.Admin.Data.Utility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Lrb.Admin.Data.Settings;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Asn1.Ocsp;

namespace Lrb.Admin.Data.Services
{
    internal class PaymentService : IPaymentService
    {
        private readonly IPaymentRepository _paymentRepository;
        private readonly IHttpService _httpService;
        private string _phpApiKey;
        private readonly IJSRuntime JSRuntime;
        private readonly LrbApiEndPoints _apiEndPoints;
        private readonly string _identityApi;
        private readonly string _webApi;
        private readonly string _mobileApi;

        public PaymentService(IPaymentRepository paymentRepository, IHttpService httpService, IConfiguration config, IOptions<LrbApiEndPoints> endPointsoptions)
        {
            _paymentRepository = paymentRepository;
            _httpService = httpService;
            _phpApiKey = config.GetSection("PHP_PaymentApiKey")["API-Key"];
            _apiEndPoints = endPointsoptions.Value;
            _identityApi = config["LrbApiEndPoints:IdentityApi"];
            _webApi = config["LrbApiEndPoints:WebApi"];
            _mobileApi = config["LrbApiEndPoints:MobileApi"];
        }

        public async Task<Response<bool>> CreatePaymentAsync(LRBPayments model)
        {
            var response = await _paymentRepository.CreatePaymentAsync(model);
            return response;
        }

        public async Task<Response<bool>> CreateInvoiceAsync(CreateInvoice model)
        {
            var response = await _paymentRepository.CreateInvoiceAsync(model);
            return response;
        }

        //This method is being used to call an external API.
        public async Task<Response<IEnumerable<PaymentDetailsDto>>> GetPaymenDeatilstAsync(string baseUrl, string resource)
        {
            Dictionary<string, string> headers = new Dictionary<string, string>();
            headers.Add("API-Key", _phpApiKey);
            var response = await _httpService.GetAsync<Response<IEnumerable<PaymentDetailsDto>>>(baseUrl, resource, headers);
            return response;
        }

        public async Task<LRBPayments> GetPaymentDetailsByIdAsync(Guid? paymentId)
        {
            var response = await _paymentRepository.GetPaymentDetailsById(paymentId);
            return response;
        }

        public async Task<LRBPayments> GetPaymentDetailsBySubscriptionIdAsync(Guid? SubscriptionId)
        {
            var response = await _paymentRepository.GetPaymentDetailsBySubscriptionId(SubscriptionId);
            return response;
        }

        public async Task<int> UpdatePaymentAsync(LRBPayments dto)
        {
            var response = await _paymentRepository.UpdatePayment(dto);
            return response;
        }

        public async Task<IEnumerable<PendingPaymentDetailDto>> GetPendingPaymentDetailsAsync()
        {
            var response = await GetPendingPaymentDetailsAsync();
            return response;
        }

        public Task UpdateIsDormentedAsync()
        {
            throw new NotImplementedException();
        }

        public async Task<PagedResponse<GetPendingPaymentModel, string>?> GetAllPendingPaymentAsync(PendingPaymentFilterParameter? filter)
        {
            try
            {
                var response = await _paymentRepository.GetAllAsync(filter.PageNumber, filter.PageSize, filter);
                return response;
            }
            catch (Exception ex)
            {
                return new PagedResponse<GetPendingPaymentModel, string>(null, 0)

                {
                    Succeeded = false,
                    Errors = new List<string>() { JsonConvert.SerializeObject(ex) },
                    Message = ex.Message
                };
            }
        }

        public async Task<List<PendingPaymentViewModelExport>> GetExportAllPendingPaymentAsync(PendingPaymentFilterParameter filter)
        {
            try
            {
                var response = await _paymentRepository.GetPendingPaymentList(filter);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception();
            }
        }

        public async Task<EmailEventDto> SendEmailViaUtility()
        {
            try
            {
                EmailEventDto response = await _paymentRepository.SendPaymentDueMails();
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<IEnumerable<string>> GetAdminEmailAsync(string tenantId)
        {
            try
            {
                var response = await _paymentRepository.SendAdminMails(tenantId);
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<bool> DeletePaymentAsync(Guid PaymentId)
        {
            var response = await _paymentRepository.DeletePayment(PaymentId);
            return response;
        }

        public async Task<bool> SendPendingPaymentEmailAsync(GetPendingPaymentModel pendingEmail)
        {
            try
            {
                string url = $"{_apiEndPoints.WebApi}/api/v1/utility/SendPendingEmail";

                var formContent = new MultipartFormDataContent();

                formContent.Add(new StringContent(pendingEmail.Sender), "Sender");
                formContent.Add(new StringContent(pendingEmail.Subject), "Subject");
                formContent.Add(new StringContent(pendingEmail.ContentBody), "ContentBody");
                formContent.Add(new StringContent("true"), "IsHtml");

                // Add ToRecipients individually as parameters
                pendingEmail.ToRecipients?.ForEach(toRecipient =>
                {
                    formContent.Add(new StringContent(toRecipient), "ToRecipients");
                });

                // Add CcRecipients individually as parameters
                pendingEmail.CcRecipients?.ForEach(ccRecipient =>
                {
                    formContent.Add(new StringContent(ccRecipient), "CcRecipients");
                });

                // Add BccRecipients individually as parameters
                pendingEmail.BccRecipients?.ForEach(bccRecipient =>
                {
                    formContent.Add(new StringContent(bccRecipient), "BccRecipients");
                });

                foreach (var file in pendingEmail.AttachedFiles)
                {
                    if (!string.IsNullOrEmpty(file) && System.IO.File.Exists(file))
                    {
                        var fileStream = new FileStream(file, FileMode.Open);
                        formContent.Add(new StreamContent(fileStream), "AttachedFiles", Path.GetFileName(file));
                    }
                }
                var response = await _httpService.PostAsync<MultipartFormDataContent, Response<bool>>(url, formContent, null);

                return (bool)response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return false;
            }
        }
    }
}
